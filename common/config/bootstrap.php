<?php

use common\helpers\DataHelper;
use common\models\Career;
use common\models\CareerContent;
use common\models\Olympiad;
use common\models\OlympiadContent;
use common\models\Scholarship;
use common\models\ScholarshipContent;
use common\models\ScholarshipCategory;

Yii::setAlias('@common', dirname(__DIR__));
Yii::set<PERSON>lia<PERSON>('@api', dirname(dirname(__DIR__)) . '/api');
Yii::setAlias('@frontend', dirname(dirname(__DIR__)) . '/frontend');
Yii::setAlias('@backend', dirname(dirname(__DIR__)) . '/backend');
Yii::setAlias('@console', dirname(dirname(__DIR__)) . '/console');

Yii::setAlias('@gmuAssets', '@frontend/web/gmu-assets');
Yii::setAlias('@getmyuniExamAsset', DataHelper::s3Path(null, 'exam_genral', 'path'));

Yii::setAlias('@examTinymceUpload', '/var/www/html/yii/assets/images/main/exam/content');
Yii::setAlias('@examTinymceFrontend', DataHelper::s3Path(null, 'exam', 'path'));

Yii::setAlias('@collegeTinymceUpload', '/var/www/html/yii/assets/images/main/college/content');
Yii::setAlias('@collegeTinymceFrontend', DataHelper::s3Path(null, 'college', 'path'));

Yii::setAlias('@articleGeneralFrontend', DataHelper::s3Path(null, 'article_general', 'path'));
Yii::setAlias('@ncertGeneralFrontend', DataHelper::s3Path(null, 'ncert_general', 'path'));

Yii::setAlias('@studyAbroadArticleTinymce', 'https://getmyuni.azureedge.net/assets/images/study-abroad/tinymce/articles');
Yii::setAlias('@gmuAzureStudyAbroadUpload', DataHelper::s3Path(null, 'article_study_abroad', 'path'));
Yii::setAlias('@articleTinymceUpload', '/var/www/html/yii/assets/images/articles/content');
Yii::setAlias('@articleTinymceFrontend', '@articleGeneralFrontend/content');

Yii::setAlias('@ncertTinymceUpload', '/var/www/html/yii/assets/images/ncert/content');
Yii::setAlias('@ncertTinymceFrontend', '@ncertGeneralFrontend/content');

Yii::setAlias('@categoryTinymceUpload', '/var/www/html/yii/assets/category/content');
Yii::setAlias('@categoryTinymceFrontend', DataHelper::s3Path(null, 'category', 'path'));

Yii::setAlias('@profileDPFrontend', DataHelper::s3Path(null, 'user', 'path'));

Yii::setAlias('@gmuAzerUpload', 'https://getmyuni.azureedge.net/assets/images');
Yii::setAlias('@gmuAzerUploadBackend', DataHelper::s3Path(null, 'board_schema', 'path'));

Yii::setAlias('@boardLogoUploadPath', '/var/www/html/yii/assets/images/board-logos/');
Yii::setAlias('@boardLogoFrontend', DataHelper::s3Path(null, 'board_genral', 'path'));
Yii::setAlias('@boardTinymceUpload', '/var/www/html/yii/assets/images/main/board/content');
Yii::setAlias('@boardTinymceFrontend', DataHelper::s3Path(null, 'board', 'path'));
Yii::setAlias('@samplePaperCoverImageFrontend', DataHelper::s3Path(null, 'board_sample_papers', 'path'));

#Yii::setAlias('@newsSitemapPath', '/var/www/html/getmyuni');

Yii::setAlias('@newsSitemapPath', '/var/www/html/gmu-yii-backend/frontend/web');
Yii::setAlias('@gmuAzerUploadSourceRanking', '@gmuAzerUpload/ranking_source_new');

Yii::setAlias('@gmuAzureCollegeImage', DataHelper::s3Path(null, 'college_genral', 'path'));
Yii::setAlias('@gmuAzureCollegeImageLogo', DataHelper::s3Path(null, 'college_logo', 'path'));

Yii::setAlias('@gmuAzureClpCollegeLogo', DataHelper::s3Path(null, 'clp_college_logo', 'path'));
Yii::setAlias('@gmuAzureClpCollegeBanner', DataHelper::s3Path(null, 'clp_college_banner', 'path'));

Yii::setAlias('@gmuAzureSaCollegeImage', DataHelper::s3Path(null, 'sa_college_genral', 'path'));
Yii::setAlias('@gmuAzureSaCollegeImageLogo', DataHelper::s3Path(null, 'sa_college_logo', 'path'));

Yii::setAlias('@dynamicCtaImageFrontend', 'https://www.getmyuni.com/assets/dynamiccta/images');
Yii::setAlias('@dynamicCtaImageFrontend', DataHelper::s3Path(null, 'dynamic_cta_image', 'path'));
Yii::setAlias('@dynamicCtaFileFrontend', DataHelper::s3Path(null, 'dynamic_cta_file', 'path'));

Yii::setAlias('@courseTinymceUpload', '/var/www/html/yii/assets/images/main/course/content');
Yii::setAlias('@courseTinymceFrontend', DataHelper::s3Path(null, 'course', 'path'));

Yii::setAlias('@cityImageFrontend', DataHelper::s3Path(null, 'city', 'path'));
Yii::setAlias('@twitterImageFrontend', DataHelper::s3Path(null, 'twitter', 'path'));

Yii::setAlias('@newsImageFrontend', DataHelper::s3Path(null, 'news_genral', 'path'));

Yii::setAlias('@newsTinymceUpload', '/var/www/html/yii/assets/images/main/news/content');
Yii::setAlias('@newsTinymceFrontend', DataHelper::s3Path(null, 'news', 'path'));

Yii::setAlias('@boardSamplePaperTinymceUpload', '/var/www/html/yii/assets/images/main/board-sample-paper/content');
Yii::setAlias('@boardSamplePaperTinymceFrontend', DataHelper::s3Path(null, 'board-sample-paper', 'path'));

Yii::setAlias('@liveNewsTinymceUpload', '/var/www/html/yii/assets/images/main/live-news/content');
Yii::setAlias('@liveNewsTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/live-news/content');
Yii::setAlias('@collegeRankPublisherFrontend', DataHelper::s3Path(null, 'ranking', 'path'));


Yii::setAlias('@careerGeneralFrontend', DataHelper::s3Path(null, Career::ENTITY_CAREER, 'path'));
Yii::setAlias('@careerTinymceUpload', '/var/www/html/yii/assets/images/main/career/content');
Yii::setAlias('@careerTinymceFrontend', DataHelper::s3Path(null, CareerContent::CAREER_CONTENT_ENTITY, 'path'));

Yii::setAlias('@homePageSlider', DataHelper::s3Path(null, 'homepage_slides', 'path'));

Yii::setAlias('@scholarshipGeneralFrontend', DataHelper::s3Path(null, Scholarship::ENTITY_SCHOLARSHIP_URL, 'path'));
Yii::setAlias('@scholarshipGeneralFrontendLogo', DataHelper::s3Path(null, Scholarship::ENTITY_SCHOLARSHIP_LOGO, 'path'));
Yii::setAlias('@scholarshipTinymceFrontend', DataHelper::s3Path(null, ScholarshipContent::SCHOLARSHIP_CONTENT_ENTITY, 'path'));
Yii::setAlias('@scholarshipcategoryTinymceFrontend', DataHelper::s3Path(null, ScholarshipCategory::SCHOLARSHIP_CATEGORY_CONTENT, 'path'));

Yii::setAlias('@olympiadGeneralFrontend', DataHelper::s3Path(null, Olympiad::ENTITY_OLYMPIAD, 'path'));
Yii::setAlias('@olympiadTinymceUpload', '/var/www/html/yii/assets/images/main/olympiad/content');
Yii::setAlias('@olympiadTinymceFrontend', DataHelper::s3Path(null, OlympiadContent::ENTITY_OLYMPIAD_CONTENT, 'path'));

Yii::setAlias('@pdfAssets', '@frontend/web/yas/pdf');
