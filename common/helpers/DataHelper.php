<?php

namespace common\helpers;

use Carbon\Carbon;
use common\models\Article;
use common\models\Redirection;
use common\models\Exam;
use common\models\SaCollegeDetail;
use common\models\Status;
use common\services\UserService;
use Exception;
use frontend\helpers\Url;
use GeoIp2\Database\Reader;
use Yii;
use yii\helpers\Inflector;
use DOMDocument;

DataHelper::examCutOff();
class DataHelper
{

    public static $pageWiseDates = [
        'form' => ['application-form.start.Application Form Start Date', 'application-form.end.Application Form End Date'],
        'application-process' => ['application-form.start.Application Form Start Date', 'application-form.end.Application Form End Date'],
        'admit-card' => ['admit-card.start.Admit Card Date', 'exam-start.start.Exam Date'],
        'overview' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'important-dates' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'eligibility' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'exam-centres' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'exam-pattern' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'syllabus' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'results' => ['result-date.start.Result Date', 'counselling-date.start.Counselling Date'],
        'cut-off' => ['result-date.start.Result Date', 'counselling-date.start.Counselling Date'],
        'counselling' => ['result-date.start.Result Date', 'counselling-date.start.Counselling Date'],
        'reference-books' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'notification' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'vacancy' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'recruitment' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'coaching' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'previous-years-papers' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'mock-sample-tests' => ['exam-start.start.Exam Date', 'result-date.start.Result Date'],
        'answer-key' => ['final-answer-key-date.start.Answer Key Date', 'result-date.start.Result Date'],
    ];

    public static $examSponsorClients = [
        'chandigarh-university-common-entrance-test' => 'http://schl.in/yqRS',
        'suat' => 'http://schl.in/pmrC',
        'jklu-eet' => 'http://schl.in/1NO7',
        'atit' => 'http://schl.in/EUsr',
        'smit-offline-test' => 'http://schl.in/ZPk6',
        'iucet' => 'http://schl.in/getw',
        'aifset' => 'http://schl.in/gPdM',
        'de-code-exam' => 'http://schl.in/zdLV',
        'idat' => 'http://schl.in/y7mN',
        'upeseat' => 'http://schl.in/3GEC',
        'upes-met' => 'http://schl.in/3GEC',
        'upes-dat' => 'http://schl.in/3GEC',
        'ulsat' => 'http://schl.in/3GEC',
        'upespat' => 'http://schl.in/3GEC',
        'chandigarh-university-common-entrance-test' => 'http://cucet.cuchd.in/index.aspx?type=gmu_10',
        'lpunest' => 'http://schl.in/3KwB',
        'vgucet' => 'http://schl.in/ctQX',
        //'mrnat' => 'http://schl.in/B5hp',
        'sajee' => 'https://saitm.ac.in/engineering-college/?utm_source=getmyuni&utm_medium=RD01&utm_campaign=saitm',
        'bgu-entrance-test' => 'https://forms.bgu.ac.in/?utm_source=getmyuni&utm_medium=RD01&utm_campaign=BGU',
        'jet' => 'https://lp.getmyuni.com/jain-courses/?utm_source=getmyuni&utm_medium=RD1&utm_campaign=Jain',
        'gat' => 'https://lp.getmyuni.com/gitam-admissions/?utm_source=Getmyuni&utm_medium=RD01&utm_campaign=gitam',
        'klueee' => 'https://admissions.kluniversity.in/?utm_source=getmyuni&utm_medium=RD01&utm_campaign=KLU2021',
        'ausat' => 'http://schl.in/s1NC',
        'kiitee' => 'http://schl.in/gREe',
        'aiecst' => 'http://schl.in/FRGl',
        'gd-goenka-university-aspire-entrance-and-scholarship-test' => 'https://applications.gdgoenkauniversity.com/?utm_source=GetMyUni&utm_medium=RD02&utm_campaign=GDG',
        'aeee' => 'https://admissions.amrita.edu/btech/?utm_source=Getmyuni&utm_medium=RD01&utm_campaign=B.Tech2021&utm_content=Placement',
        'reva-cet' => 'https://admissions.reva.edu.in/?utm_source=getmyuni&utm_medium=RD03&utm_campaign=reva',
        'ibsat' => 'https://admissions.ibsindia.org/IBSAT2021/registration/index.asp?utm_source=getmyuni&utm_medium=directlink',
        'nbsat' => 'https://nbs.edu.in/lp/mba-pgdm-admission/?utm_source=getmyuni&utm_medium=RD02&utm_campaign=NBS',
        'snap' => 'https://www.snaptest.org/symbiosis-entrance-exam-for-mba.html?utm_source=getmyuni&utm_medium=mailer',
    ];

    public static $manageMenuOrder = [
        'exam_content' => 1,
        'board_content' => 2
    ];

    public static $articleTopicPage = [
        'preparation' => 'Preparation',
        'overview' => 'Overview',
        'important-dates' => 'Exam Dates',
        'eligibility' => 'Eligibility Criteria',
        'form' => 'Application Form',
        'admit-card' => 'Admit Card',
        'syllabus' => 'Syllabus',
        'exam-pattern' => 'Exam Pattern',
        'exam-centres' => 'Exam Centres',
        'preparation' => 'Preparation',
        'mock-sample-tests' => 'Mock Tests',
        'reference-books' => 'Books',
    ];

    /**
     * getConstantList: To get constant list based on constant prefix and class name.
     *
     * @param string $prefix    constant prefix
     * @param string $className class name
     *
     * @return array
     */
    public static function getConstantList($prefix, $className, $value = null)
    {
        $constantList = [];
        $prefixLength = strlen($prefix);
        $reflection = new \ReflectionClass($className);
        $constants = $reflection->getConstants();
        foreach ($constants as $name => $val) {
            if (substr($name, 0, $prefixLength) != $prefix) {
                continue;
            }
            $constantList[$val] = Inflector::camel2words(str_replace($prefix, '', $name));
        }

        if (!is_null($value)) {
            return $constantList[$value];
        }
        return $constantList;
    }

    /**
     *
     * @return array
     */
    public static function examContentList()
    {
        return [
            'overview' => 'Overview',
            'important-dates' => 'Exam Dates',
            'eligibility' => 'Eligibility Criteria',
            // 'application-process' => 'Application Process',
            'form' => 'Application Form',
            'admit-card' => 'Admit Card',
            'exam-centres' => 'Exam Centres',
            'exam-pattern' => 'Exam Pattern',
            // 'result-cutoff' => 'Result Cutoff',
            // 'counselling-procedure' => 'Counselling Procedure',
            'syllabus' => 'Syllabus',
            'results' => 'Result',
            'cut-off' => 'Cut Off',
            'counselling' => 'Counselling',
            'reference-books' => 'Books',
            'notification' => 'Notification',
            'vacancy' => 'Vacancy',
            'recruitment' => 'Recruitment',
            'coaching' => 'Coaching',
            'previous-years-papers' => 'Previous Year Question Papers',
            'mock-sample-tests' => 'Mock Tests',
            'answer-key' => 'Answer Key',
            // 'registration' => 'Registration',
            'paper-analysis' => 'Paper Analysis',
            'preparation' => 'Preparation',
            'marks-vs-rank' => 'Marks vs Rank',
            'rank-predictor' => 'Rank Predictor',
            'percentile-predictor' => 'Percentile Predictor',
            'score-vs-percentile' => 'Score vs Percentile',
            // 'slot-booking' => 'Slot Booking',
            'login' => 'Login',
            'topper' => 'Topper',
            'response-sheet' => 'Response Sheet',
            'participating-colleges' => 'Participating Colleges',
            'sample-papers' => 'Sample Papers',
            'coaching-institutes' => 'Coaching Institutes',
            'college-predictor' => 'College Predictor',
            'selection-process' => 'Selection Process'
        ];
    }

    public static function orderedMenuList($menuArray, $activeKey)
    {
        try {
            if ($activeKey == array_key_first($menuArray)) {
                return $menuArray;
            }

            $newArr = [];
            $activeElement = $menuArray[$activeKey];
            unset($menuArray[$activeKey]);
            $i = 0;

            foreach ($menuArray as $key => $value) {
                if ($i == 1) {
                    $newArr[$activeKey] = $activeElement;
                }
                $newArr[$key] = $value;
                $i++;
            }

            return $newArr;
        } catch (Exception $e) {
            return $menuArray;
        }
    }

    /** @var string $filterStreamList */
    public static $filterStreamList = [
        'engineering' => 4,
        'management' => 5,
        'arts' => 6,
        'commerce' => 7,
        'medical' => 8,
        'pharmacy' => 9,
        'science' => 10,
        'architecture' => 11,
        'agriculture' => 12,
        'law' => 13,
        'dental' => 14,
        'computer' => 15,
        'design' => 16,
        'education' => 17,
        'veterinary' => 18,
        'paramedical' => 19,
        'government' => 20,
    ];

    /**
     * List of exams by discipline
     *
     * @var array
     */
    public static $examsList = [
        'Engineering' => [
            ['title' => 'JEE Main', 'slug' => 'jee-main'],
            ['title' => 'UPESEAT', 'slug' => 'upeseat'],
            ['title' => 'TS EAMCET', 'slug' => 'ts-eamcet'],
            ['title' => 'JEE Advanced', 'slug' => 'jee-advanced'],
            ['title' => 'AP EAMCET', 'slug' => 'ap-eamcet'],
            // ['title' => 'COMEDK UGET', 'slug' => 'comedk-uget'],
            // ['title' => 'KCET', 'slug' => 'kcet'],
            // ['title' => 'WBJEE', 'slug' => 'wbjee']
        ],
        'Management' => [
            ['title' => 'CAT', 'slug' => 'cat'],
            ['title' => 'MAT', 'slug' => 'mat'],
            ['title' => 'UPESMET', 'slug' => 'upes-met'],
            ['title' => 'SNAP', 'slug' => 'snap'],
            ['title' => 'TS ICET', 'slug' => 'ts-icet'],
            // ['title' => 'CMAT', 'slug' => 'cmat'],
            // ['title' => 'AP ICET', 'slug' => 'apicet'],
            // ['title' => 'NMAT', 'slug' => 'nmat']
        ],
        'Science' => [
            ['title' => 'AIFSET', 'slug' => 'aifset'],
            ['title' => 'SET Exam', 'slug' => 'set-exam'],
            ['title' => 'IIT JAM', 'slug' => 'iit-jam'],
            ['title' => 'JCECE', 'slug' => 'jcece'],
            ['title' => 'IMU CET', 'slug' => 'imucet'],
        ],
        'Pharmacy' => [
            ['title' => 'GPAT', 'slug' => 'gpat'],
            ['title' => 'TS EAMCET', 'slug' => 'ts-eamcet'],
            ['title' => 'AP EAMCET', 'slug' => 'ap-eamcet'],
            ['title' => 'BITSAT', 'slug' => 'bitsat'],
            ['title' => 'WBJEE', 'slug' => 'wbjee'],
            // ['title' => 'KCET', 'slug' => 'kcet'],
            // ['title' => 'UPTU', 'slug' => 'uptu']
        ],
        'Law' => [
            ['title' => 'CLAT', 'slug' => 'clat'],
            ['title' => 'TS LAWCET', 'slug' => 'ts-lawcet'],
            ['title' => 'ULSAT', 'slug' => 'ulsat'],
            ['title' => 'AILET', 'slug' => 'ailet'],
            ['title' => 'AP LAWCET', 'slug' => 'ap-lawcet'],
            // ['title' => 'DU LLB', 'slug' => 'du-llb'],
            // ['title' => 'MH CET Law', 'slug' => 'mh-cet-law']
        ],
        'Design' => [
            ['title' => 'NIFT', 'slug' => 'nift'],
            ['title' => 'UPES DAT', 'slug' => 'upes-dat'],
            ['title' => 'NID', 'slug' => 'nid'],
            ['title' => 'UCEED', 'slug' => 'uceed'],
            ['title' => 'AIDAT', 'slug' => 'aidat'],
        ],
        'Commerce' => [
            ['title' => 'IPU CET', 'slug' => 'ipu-cet'],
            ['title' => 'NPAT', 'slug' => 'npat'],
            ['title' => 'BHU UET', 'slug' => 'bhu-uet'],
            ['title' => 'DSAT', 'slug' => 'dsat'],
            ['title' => 'OUCET', 'slug' => 'oucet'],
        ],
        'Medical' => [
            ['title' => 'NEET', 'slug' => 'neet'],
            ['title' => 'BCECE', 'slug' => 'bcece'],
            ['title' => 'KEAM', 'slug' => 'keam'],
            ['title' => 'NEET PG', 'slug' => 'neet-pg'],
            ['title' => 'INI CET', 'slug' => 'ini-cet'],
        ],
        'Dental' => [
            ['title' => 'NEET', 'slug' => 'neet'],
            ['title' => 'PGIMER', 'slug' => 'pgimer'],
            ['title' => 'NEET MDS', 'slug' => 'neet-mds'],
            ['title' => 'RUET', 'slug' => 'ruet'],
            ['title' => 'MP DMAT', 'slug' => 'mp-dmat'],
        ],
        'Architecture' => [
            ['title' => 'JEE Main', 'slug' => 'jee-main'],
            ['title' => 'JEE Advanced', 'slug' => 'jee-advanced'],
            ['title' => 'NATA', 'slug' => 'nata'],
            ['title' => 'COMEDK UGET', 'slug' => 'comedk-uget'],
            ['title' => 'KIITEE', 'slug' => 'kiitee'],
        ],
        'Arts' => [
            ['title' => 'CUCET', 'slug' => 'cucet'],
            ['title' => 'KIITEE', 'slug' => 'kiitee'],
            ['title' => 'TISSNET', 'slug' => 'tissnet'],
            ['title' => 'NPAT', 'slug' => 'npat'],
            ['title' => 'GMCET', 'slug' => 'gmcet'],
        ],
        'Agriculture' => [
            ['title' => 'MHT CET', 'slug' => 'mht-cet'],
            ['title' => 'BCECE', 'slug' => 'bcece'],
            ['title' => 'KEAM', 'slug' => 'keam'],
            ['title' => 'ICAR AIEEA', 'slug' => 'icar-aieea'],
            ['title' => 'AGRICET', 'slug' => 'agricet'],
        ],
        'Paramedical' => [
            ['title' => 'CPNET', 'slug' => 'cpnet'],
            ['title' => 'AIPMCET', 'slug' => 'aipmcet'],
            ['title' => 'SMFWBEE', 'slug' => 'smfwbee-exam'],
            ['title' => 'AIIMS Nursing', 'slug' => 'aiims-nursing'],
            ['title' => 'BHU BSc Nursing', 'slug' => 'bhu-bsc-nursing'],
            // ['title' => 'KGMU BSc Nursing', 'slug' => 'kgmu-bsc-nursing-entrance-exam']
        ],
        'Education' => [
            ['title' => 'TS EDCET', 'slug' => 'ts-edcet'],
            ['title' => 'IGNOU BEd', 'slug' => 'ignou-bed'],
            ['title' => 'AP EDCET', 'slug' => 'ap-edcet'],
            ['title' => 'Bihar CET BEd', 'slug' => 'bihar-cet-bed'],
            ['title' => 'MAH BEd CET', 'slug' => 'maha-bed-cet'],
        ],
        'Computer Application' => [
            ['title' => 'TS ICET', 'slug' => 'ts-icet'],
            ['title' => 'AP ICET', 'slug' => 'apicet'],
            ['title' => 'PGCET', 'slug' => 'pgcet'],
            ['title' => 'TANCET', 'slug' => 'tancet'],
            ['title' => 'MAH MCA CET', 'slug' => 'mah-mca-cet'],
        ],
    ];

    public static function streamMapping()
    {
        return [
            'architecture' => 'architecture',
            'pharmacy' => 'pharmacy',
            'agriculture' => 'agriculture',
            'architecture' => 'architecture',
            'arts' => 'arts',
            'business-management' => 'business-management',
            'businsess-management' => 'business-management',
            'commerce' => 'commerce',
            'computer-application' => 'computer applications',
            'computer-applications' => 'computer applications',
            'computer-appplications' => 'computer applications',
            'dental' => 'dental',
            'desgin' => 'design',
            'design' => 'design',
            'education' => 'education',
            'engineering' => 'engineering',
            'engineering' => 'engineering',
            // 'government' => '',
            // 'hospitality' => '',
            'hotel-management' => 'hotel-management',
            // 'humanity-arts' => '',
            'law' => 'law',
            'management' => 'management',
            'mass-communication' => 'mass-communication',
            'mass-communications' => 'mass-communication',
            'medical' => 'medical',
            'paramedical' => 'paramedical',
            'pharmacy' => 'pharmacy',
            'science' => 'science',
            // 'study-abroad' => '',
            'veterinary-sciences' => 'veterinary-sciences',
            // 'vocational-courses' => ''
        ];
    }

    public static function gmuExamTitle()
    {
        return [
            'overview' => 'overview',
            'important_dates' => 'important-dates',
            'eligibility' => 'eligibility',
            'application_process' => 'application-process',
            'application_form_details' => 'form',
            'admit_card' => 'admit-card',
            'exam_centres' => 'exam-centres',
            'exam_pattern' => 'exam-pattern',
            'syllabus' => 'syllabus',
            'results' => 'results',
            'cut_off' => 'cut-off',
            'counselling' => 'counselling',
            'ref_books' => 'reference-books',
            'notification' => 'notification',
            'vacancy' => 'vacancy',
            'recruitment' => 'recruitment',
            'coaching' => 'coaching',
            'previous_year_paper_content' => 'previous-years-papers',
            'sample_test_paper_content' => 'mock-sample-tests',
            'answer_key_content' => 'answer-key',
        ];
    }

    public static function examDateList()
    {
        return [
            'registration-date' => 'Registration Date',
            'application-form' => 'Application Form',
            'admit-card' => 'Admit Card',
            'exam-start' => 'Exam Start',
            'result-date' => 'Result Date',
            'counselling-date' => 'Counselling Date',
            'notification-date' => 'Notification Date',
            'application-form-correction' => 'Application Form Correction',
            'admit-card-download' => 'Admit Card Download',
            'provisional-answer-key-date' => 'Provisional Answer Key Date',
            'final-answer-key-date' => 'Final Answer Key Date',
            'round-one-counselling-registration' => 'Round One Counselling-Registration',
            'round-one-counselling-choice-filling' => 'Round One Counselling-Choice Filling ',
            'round-one-counselling-seat-allotment-list' => 'Round One Counselling-Seat Allotment List',
            'round-one-counselling-date-of-reporting' => 'Round One Counselling-Date of Reporting',
            'round-two-counselling-registration' => 'Round Two Counselling-Registration ',
            'round-two-counselling-choice-filling' => 'Round Two Counselling-Choice Filling ',
            'round-two-counselling-seat-allotment-list' => 'Round Two Counselling-Seat Allotment List',
            'round-two-counselling-date-of-reporting' => 'Round Two Counselling-Date of Reporting',
            'round-three-counselling-registration' => 'Round Three Counselling-Registration ',
            'round-three-counselling-choice-filling' => 'Round Three Counselling-Choice Filling ',
            'round-three-counselling-seat-allotment-list' => 'Round Three Counselling-Seat Allotment List',
            'round-three-counselling-date-of-reporting' => 'Round Three Counselling-Date of Reporting',
            'round-four-counselling-registration' => 'Round Four Counselling-Registration ',
            'round-four-counselling-choice-filling' => 'Round Four Counselling-Choice Filling ',
            'round-four-counselling-seat-allotment-list' => 'Round Four Counselling-Seat Allotment List',
            'round-four-counselling-date-of-reporting' => 'Round Four Counselling-Date of Reporting',
            'round-five-counselling-registration' => 'Round Five Counselling-Registration ',
            'round-five-counselling-choice-filling' => 'Round Five Counselling-Choice Filling ',
            'round-five-counselling-seat-allotment-list' => 'Round Five Counselling-Seat Allotment List',
            'round-five-counselling-date-of-reporting' => 'Round Five Counselling-Date of Reporting',
            'round-six-counselling-registration' => 'Round Six Counselling-Registration ',
            'round-six-counselling-choice-filling' => 'Round Six Counselling-Choice Filling ',
            'round-six-counselling-seat-allotment-list' => 'Round Six Counselling-Seat Allotment List',
            'round-six-counselling-date-of-reporting' => 'Round Six Counselling-Date of Reporting',
            'interview-date' => 'Interview Date',
            'portfolio-review-and-personal-interaction-round' => 'Portfolio Review and Personal Interaction Round',
            'declaration-of-merit-list' => 'Declaration of Merit List',
        ];
    }

    public static function highestQualification()
    {
        return [
            ['value' => 'studying-completed-10th', 'name' => 'Completed 10th', 'displayName' => 'Studying/Completed 10th'],
            ['value' => 'studying-completed-12th', 'name' => 'Completed 12th', 'displayName' => 'Studying/Completed 12th'],
            ['value' => 'studying-completed-diploma', 'name' => 'Diploma - Completed', 'displayName' => 'Studying/Completed Diploma'],
            ['value' => 'studying-completed-graduation', 'name' => 'Bachelors/UG - Completed', 'displayName' => 'Studying/Completed Graduation'],
            ['value' => 'studying-completed-masters-pg', 'name' => 'Masters/PG - Completed', 'displayName' => 'Studying/Completed Masters/PG'],
        ];
    }

    public static function intrestedCourses()
    {
        return [
            ['slug' => 'engineering', 'value' => 'engineering-btech-mtech', 'name' => 'Engineering [BTech / MTech]'],
            ['slug' => 'management', 'value' => 'management-bba-mba', 'name' => 'Management [BBA / MBA ]'],
            ['slug' => 'distance-learning_correspondence', 'value' => 'distance-learning-mba', 'name' => 'Distance Learning MBA'],
            ['slug' => 'computers_it', 'value' => 'bca-mca-computers', 'name' => 'BCA / MCA / Computers'],
            ['slug' => 'design', 'value' => 'fashion', 'name' => 'Fashion'],
            ['slug' => 'design', 'value' => 'design', 'name' => 'Design'],
            ['slug' => 'architecture', 'value' => 'architecture', 'name' => 'Architecture'],
            ['slug' => 'media_films_journalism', 'value' => 'media-films', 'name' => 'Media / Films'],
            ['slug' => 'media_films_journalism', 'value' => 'journalism', 'name' => 'Journalism'],
            ['slug' => 'law', 'value' => 'law', 'name' => 'Law'],
            ['slug' => 'languages-arts-humanities', 'value' => 'arts-humanities', 'name' => 'Arts and Humanities'],
            ['slug' => 'sciences', 'value' => 'science-bsc-msc', 'name' => 'Science [B.Sc / M.Sc]'],
            ['slug' => 'animation_multimedia_web-design', 'value' => 'animation-multimedia', 'name' => 'Animation / Multimedia'],
            ['slug' => 'banking_finance', 'value' => 'finance-accounts-bcom-mcom', 'name' => 'Finance / Accounts [B.Com / M.Com]'],
            ['slug' => 'medicine_health-care', 'value' => 'medicine-healthcare-mbbs-bds-bpharm', 'name' => 'Medicine / Healthcare [MBBS / BDS / B.Pharm]'],
            ['slug' => 'aviation_hospitality_tourism', 'value' => 'hotel-management-hospitality', 'name' => 'Hotel Management / Hospitality'],
            ['slug' => 'aviation_hospitality_tourism', 'value' => 'aviation', 'name' => 'Aviation'],
            ['slug' => 'design', 'value' => 'fashion-design', 'name' => 'Fashion Design'],
        ];
    }

    public static function examPdfPages()
    {
        return [
            'mock-sample-tests' => 'sample',
            'previous-years-papers' => 'prev',
            'answer-key' => 'answer_key',
        ];
    }

    public static function getExamDateByPageWise($page, $dates)
    {
        $examDates = [];
        if (isset(self::$pageWiseDates[$page])) {
            $datesArr = self::$pageWiseDates[$page];

            foreach ($datesArr as $arr) {
                list($slug, $column, $name) = explode('.', $arr);
                foreach ($dates as $date) {
                    if (empty($date->{$column})) {
                        continue;
                    }
                    $dateSlg = date('d', strtotime($date->{$column})) . ' ' . date('M', strtotime($date->{$column})) . " '" . date('y', strtotime($date->{$column}));
                    if ($date->end != '') {
                        $dateSlg = date('d', strtotime($date->{$column})) . ' ' . date('M', strtotime($date->{$column})) . " '" . date('y', strtotime($date->{$column})) . '-' . date('d', strtotime($date->end)) . ' ' . date('M', strtotime($date->end)) . " '" . date('y', strtotime($date->end));
                    }
                    if ($date->slug == $slug) {
                        $examDates[] = [
                            'name' => $name,
                            'date' => $dateSlg,
                        ];
                    }
                }
            }
            if ($page == 'application-process' || $page == 'form') {
                if (!empty($examDates)) {
                    $examDates[0]['name'] = 'Application Form Date';
                    $examDates[0]['date'] = $examDates[0]['date'];
                    unset($examDates[1]);
                }
            }
            return $examDates;
        }
    }

    public static function getSponsorClient($examSlug)
    {
        if (isset(self::$examSponsorClients[$examSlug])) {
            return self::$examSponsorClients[$examSlug];
        }

        return null;
    }

    public static function getRedirectionLink($entity, $entityId)
    {
        $currentDate = Carbon::now()->toDateString();

        $redirection = Redirection::find()
            ->where(['entity' => $entity])
            ->andWhere(['entity_id' => $entityId])
            ->andWhere(['status' => Redirection::STATUS_ACTIVE])
            ->one();

        if (!empty($redirection)) {
            $redirectionDate = date('Y-m-d', strtotime($redirection->expired_at));
            if ($currentDate <= $redirectionDate) {
                return $redirection->redirection_link;
            }
        }
    }

    /**
     * List of ENTITIES used in project
     *
     * @var array
     */
    public static $entities = [

        '' => 'Select...',
        'articles' => 'ARTICLE',
        // 'Category' => 'CATEGORY',
        'news' => 'NEWS',
        'exam' => 'EXAM',
        'board' => 'BOARD',
        'college' => 'COLLEGE',
        'course' => 'COURSE',
        'filter' => 'FILTER',
        'ncert' => 'NCERT',
        'career' => 'CAREER'
    ];

    public static $collegeFaqEntities = [

        '' => 'Select...',
        'college' => 'COLLEGE',
        'filter' => 'FILTER',
    ];
    /**
     * List of ENTITIES used in QNA
     *
     * @var array
     */
    public static $QnaEntities = [

        '' => 'Select...',
        'exam' => 'EXAM',
        'college' => 'COLLEGE',
        'course' => 'COURSE',
        'board' => 'Board'
    ];


    /**
     * List of SUBPAGES
     *
     * @var array
     */
    public static $subPages = [
        'board' => [
            1 =>
            [
                'slug' => 'overview',
                'display_name' => 'Overview'
            ],
            2 => [

                'slug' => 'date-sheet',
                'display_name' => 'Date Sheet'
            ],
            3 => [
                'slug' => 'time-table',
                'display_name' => 'Time Table'
            ],
            4 => [
                'slug' => 'routine',
                'display_name' => 'Routine'
            ],

            5 => [
                'slug' => 'registration-form',
                'display_name' => 'Registration Form'
            ],
            6 => [

                'slug' => 'application-form',
                'display_name' => 'Application Form'
            ],
            7 => [
                'slug' => 'registration-card',
                'display_name' => 'Registration card'
            ],
            8 => [
                'slug' => 'admit-card',
                'display_name' => 'Admit Card'
            ],
            9 => [
                'slug' => 'hall-ticket',
                'display_name' => 'Hall Ticket'
            ],
            10 => [
                'slug' => 'syllabus',
                'display_name' => 'Syllabus'
            ],
            11 => [
                'slug' => 'exam-centres',
                'display_name' => 'Exam Centres'
            ],
            12 =>
            [
                'slug' => 'marking-scheme',
                'display_name' => 'Marking Scheme'
            ],
            13 => [
                'slug' => 'results',
                'display_name' => 'Results'
            ],
            14 => [
                'slug' => 'supplementary',
                'display_name' => 'Supplementary'
            ],
            15 => [
                'slug' => 'sample-papers',
                'display_name' => 'Sample Papers'
            ],
            16 => [
                'slug' => 'previous-year-question-papers',
                'display_name' => 'Previous Year Question Papers'
            ],
            17 => [
                'slug' => 'solved-question-papers',
                'display_name' => 'Solved Question Papers'
            ],
            18 => [
                'slug' => 'reference-books',
                'display_name' => 'Reference Books'
            ],
            19 => [
                'slug' => 'exam-pattern',
                'display_name' => 'Exam Pattern'
            ],
            20 => [
                'slug' => 'preparation',
                'display_name' => 'Preparation'
            ],
            21 => [
                'slug' => 'answer-key',
                'display_name' => 'Answer Key'
            ],
            22 => [
                'slug' => 'books',
                'display_name' => 'Books'
            ]
        ],
        'college' => [
            23 => [
                'slug' => 'info',
                'display_name' => 'Info'
            ],
            24 => [
                'slug' => 'courses-fees',
                'display_name' => 'Courses & Fees'
            ],
            25 => [
                'slug' => 'admission',
                'display_name' => 'Admission'
            ],
            26 => [
                'slug' => 'cut-off',
                'display_name' => 'Cut Off'
            ],
            27 => [
                'slug' => 'reviews',
                'display_name' => 'Reviews'
            ],
            28 => [
                'slug' => 'placements',
                'display_name' => 'Placements'
            ],
            29 => [
                'slug' => 'result',
                'display_name' => 'Result'
            ],
            30 => [
                'slug' => 'facilities',
                'display_name' => 'Infrastructure'
            ],
            31 => [
                'slug' => 'images-videos',
                'display_name' => 'Gallery'
            ],
            32 => [
                'slug' => 'scholarships',
                'display_name' => 'Scholarship'
            ],
            33 => [
                'slug' => 'qna',
                'display_name' => 'Forum'
            ],
            34 => [
                'slug' => 'compare-college',
                'display_name' => 'Compare Colleges'
            ]
        ],
        'exam' => [
            35 => [
                'slug' => 'overview',
                'display_name' => 'Overview'
            ],
            36 => [
                'slug' => 'important-dates',
                'display_name' => 'Exam Dates'
            ],
            37 => [
                'slug' => 'eligibility',
                'display_name' => 'Eligibility Criteria'
            ],
            38 => [
                'slug' => 'application-process',
                'display_name' => 'Application Process'
            ],
            39 => [
                'slug' => 'form',
                'display_name' => 'Application Form'
            ],
            40 => [
                'slug' => 'admit-card',
                'display_name' => 'Admit Card'
            ],
            41 => [
                'slug' => 'exam-centres',
                'display_name' => 'Exam Centres'
            ],
            42 => [
                'slug' => 'exam-pattern',
                'display_name' => 'Exam Pattern'
            ],
            43 => [
                'slug' => 'syllabus',
                'display_name' => 'Syllabus'
            ],
            44 => [
                'slug' => 'results',
                'display_name' => 'Result'
            ],
            45 => [
                'slug' => 'cut-off',
                'display_name' => 'Cut Off'
            ],
            46 => [
                'slug' => 'counselling',
                'display_name' => 'Counselling'
            ],
            47 => [
                'slug' => 'reference-books',
                'display_name' => 'Books'
            ],
            48 => [
                'slug' => 'notification',
                'display_name' => 'Notification'
            ],
            49 => [
                'slug' => 'vacancy',
                'display_name' => 'Vacancy'
            ],
            50 => [
                'slug' => 'recruitment',
                'display_name' => 'Recruitment'
            ],
            51 => [
                'slug' => 'coaching',
                'display_name' => 'Coaching'
            ],
            52 => [
                'slug' => 'previous-years-papers',
                'display_name' => 'Previous Year Question Papers'
            ],
            53 => [
                'slug' => 'mock-sample-tests',
                'display_name' => 'Mock Tests'
            ],
            54 => [
                'slug' => 'answer-key',
                'display_name' => 'Answer Key'
            ],
            55 => [
                'slug' => 'registration',
                'display_name' => 'Registration'
            ],
            56 => [
                'slug' => 'paper-analysis',
                'display_name' => 'Paper Analysis'
            ],
            57 => [
                'slug' => 'preparation',
                'display_name' => 'Preparation'
            ],
            58 => [
                'slug' => 'marks-vs-rank',
                'display_name' => 'Marks vs Rank'
            ],
            59 => [
                'slug' => 'rank-predictor',
                'display_name' => 'Rank Predictor'
            ],
            60 => [
                'slug' => 'percentile-predictor',
                'display_name' => 'Percentile Predictor'
            ],
            61 => [
                'slug' => 'core-vs-percentile',
                'display_name' => 'Score vs Percentile'
            ],
            62 => [
                'slug' => 'slot-booking',
                'display_name' => 'Slot Booking'
            ],
            63 => [
                'slug' => 'login',
                'display_name' => 'Login'
            ],
            64 => [
                'slug' => 'topper',
                'display_name' => 'Topper'
            ],
            65 => [
                'slug' => 'response-sheet',
                'display_name' => 'Response Sheet'
            ]
        ],
        'course' => [
            66 => [
                'slug' => 'about',
                'display_name' => 'About'
            ],
            67 => [
                'slug' => 'syllabus-subjects',
                'display_name' => 'Syllabus and Subjects'
            ],
            68 => [
                'slug' => 'jobs-scope-salary',
                'display_name' => 'Job, Scope and Salary'
            ],
            69 => [
                'slug' => 'admission',
                'display_name' => 'Admission'
            ]
        ]

    ];


    /**
     * List of ENTITIES used in Content Teplate
     *
     * @var array
     */
    public static $entitiesTemplate = [
        '' => 'Select...',
        'college' => 'COLLEGE',
        'exam' => 'EXAM',
        'all_listing' => 'ALL_LISTING'
    ];

    /**
     * List of ENTITIES used in project for audit trails
     *
     * @var array
     */
    public static $audit_entity = [
        '' => 'Select...',
        'article' => 'article',
        'news_subdomain' => 'news_content_subdomain',
        'exam' => 'exam_content',
        'board' => 'board_content',
        'college' => 'college_content',
        'course' => 'course_content',
        'ncert' => 'ncert',
        'career' => 'career_content'
    ];

    /**
     * List of ENTITIES used for Redirection
     *
     * @var array
     */
    public static $redirectionEntities = [

        '' => 'Select...',
        'college' => 'COLLEGE',
        'exam' => 'EXAM',
        'board' => 'BOARD',
    ];

    public static $examDefaultSeoInfo = [
        'overview' => [
            'title' => '{exam-name} {year}: Exam Date, Registration, Pattern, Syllabus, Admit Card, Result',
            'h1' => '{exam-name} {year}: Exam Date, Registration, Pattern, Syllabus, Admit Card, Result',
            'description' => 'Get complete information on {exam-name} {year} Exam. Check the Exam Date, Registration, Pattern, Syllabus, Admit Card, Result and latest updates on {exam-name} {year} exam.',
        ],
        'important-dates' => [
            'title' => '{exam-name} Important Dates {year}',
            'h1' => '{exam-name} Important Dates {year}',
            'description' => 'Get {exam-name} {year} Important Dates and Events. Get complete details on {exam-name} {year} Exam Dates, Application Form Date, Admit Card Date, Result Date.',
        ],
        'eligibility' => [
            'title' => '{exam-name} Eligibility Criteria {year}: Age Limit, Educational Qualification',
            'h1' => '{exam-name} Eligibility Criteria {year}: Age Limit, Educational Qualification',
            'description' => 'Get full details on {exam-name} {year} Eligibility Criteria. Find out the age limit, educational qualification, number of attempts and eligibility criteria for OBC, SC candidates.',
        ],
        'application-process' => [
            'title' => '{exam-name} Application Process {year}',
            'h1' => '{exam-name} Application Process {year}',
            'description' => 'Get {exam-name} {year} Application Process details, which includes instructions on how to apply, Application last date, the documents required and more.',
        ],
        'form' => [
            'title' => '{exam-name} Application Form {year}: Last Date, Fees, Corrections, Documents Required',
            'h1' => '{exam-name} Application Form {year}: Last Date, Fees, Corrections, Documents Required',
            'description' => 'Get detailed information on {exam-name} {year} Application Form, including information on the Fees, Application form dates, corrections, documents required and more.',
        ],
        'admit-card' => [
            'title' => '{exam-name} Admit Card {year}: Get Hall Ticket Download Link, Release Date',
            'h1' => '{exam-name} Admit Card {year}: Get Hall Ticket Download Link, Release Date',
            'description' => 'Get details related to {exam-name} {year} Admit Card. Check {exam-name} {year} Admit Card Release Date, Hall Ticket Download Link, and how to download the Admit Card.',
        ],
        'exam-centres' => [
            'title' => '{exam-name} Exam Centres {year}: Test Cities List and Locations',
            'h1' => '{exam-name} Exam Centres {year}: Test Cities List and Locations',
            'description' => 'Get the complete list of {exam-name} {year} Exam Centres across India. The exam cities locations are also represented in a map.',
        ],
        'exam-pattern' => [
            'title' => '{exam-name} Exam Pattern {year}: Marking Scheme, Negative Marking, Total Marks',
            'h1' => '{exam-name} Exam Pattern {year}: Marking Scheme, Negative Marking, Total Marks',
            'description' => 'Get information on {exam-name} {year} Exam Pattern. Check Marking Scheme, Exam Mode, Duration, Negative Marking, Total Marks, Type and Number of Questions.',
        ],
        'syllabus' => [
            'title' => '{exam-name} Syllabus {year}: Subject-wise Syllabus and Weightage PDF Download',
            'h1' => '{exam-name} Syllabus {year}: Subject-wise Syllabus and Weightage PDF Download',
            'description' => 'Get complete details on {exam-name} {year} Syllabus. Download Subject-wise Syllabus PDF and check topic-wise weightage in {exam-name} {year}.',
        ],
        'results' => [
            'title' => '{exam-name} Result {year}: Get Scorecard Download Link, Rank, Merit List',
            'h1' => '{exam-name} Result {year}: Get Scorecard Download Link, Rank, Merit List',
            'description' => 'Get complete details on {exam-name} {year} Result. Check Result Date, Scorecard, Merit List and how to check {exam-name} {year} Result Online.',
        ],
        'cut-off' => [
            'title' => '{exam-name} Cut Off Marks {year}: Previous Years (2020, 2019, 2018) Cut Off',
            'h1' => '{exam-name} Cut Off Marks {year}: Previous Years (2020, 2019, 2018) Cut Off',
            'description' => 'Check {exam-name} {year} Cutoff Marks. You can also go through previous years {exam-name} Cut Off Marks and Percentile.',
        ],
        'counselling' => [
            'title' => '{exam-name} Counselling {year}: Dates, Registration, Seat Allotment',
            'h1' => '{exam-name} Counselling {year}: Dates, Registration, Seat Allotment',
            'description' => 'Get all details about {exam-name} {year} Counselling details such as Counselling Dates, Registration, Documents Required, Seat Allotment and more.',
        ],
        'reference-books' => [
            'title' => 'Best Books for {exam-name} {year} Preparation',
            'h1' => 'Best Books for {exam-name} {year} Preparation',
            'description' => 'Get all the Reference Books details for {exam-name} {year} along with the best books to be considered for exam preparation.',
        ],
        'mock-sample-tests' => [
            'title' => '{exam-name} Mock Test {year}: Download Free Sample Mock Test Papers PDF',
            'h1' => '{exam-name} Mock Test {year}: Download Free Sample Mock Test Papers PDF',
            'description' => '{exam-name} Mock Test {year} - Check Free Online Mock Tests for {exam-name} and also download free pdfs of Sample Mock Tests.',
        ],
        'answer-key' => [
            'title' => '{exam-name} Answer Key {year}: Release Date, PDF Download Link, Challenge Procedure',
            'h1' => '{exam-name} Answer Key {year}: Release Date, PDF Download Link, Challenge Procedure',
            'description' => 'Get complete information on {exam-name} Answer Key {year}. Check the Release Date, PDF Download Link, Challenge Procedure and {exam-name} {year} Final Answer Key.',
        ],
        'previous-years-papers' => [
            'title' => '{exam-name} Previous Years Question Papers: Free PDF Download of 2020, 2019, 2018 Papers',
            'h1' => '{exam-name} Previous Years Question Papers: Free PDF Download of 2020, 2019, 2018 Papers',
            'description' => '{exam-name} Previous Year Question Papers - Download Free PDF of year-wise (2020, 2019, 2018) questions papers for {exam-name} exam.',
        ],
        'notification' => [
            'title' => '{exam-name} Notification {year}',
            'h1' => '{exam-name} Notification {year}',
            'description' => 'Get all latest updates and Notification related to {exam-name} {year} including Recruitment, Vacancy and more.',
        ],
        'recruitment' => [
            'title' => '{exam-name} Recruitment {year}',
            'h1' => '{exam-name} Recruitment {year}',
            'description' => 'Get all latest and complete information regarding {exam-name} {year} Recruitment including Job Scope, Procedure, Salary and more.',
        ],
        'vacancy' => [
            'title' => '{exam-name} Vacancy {year}',
            'h1' => '{exam-name} Vacancy {year}',
            'description' => 'Get {exam-name} Vacancy details for {year}. Check the latest notification regarding number of vacancies and application details for {exam-name} {year}.',
        ],
        'slot-booking' => [
            'title' => '{exam-name} Slot Booking {year} - Check Dates & Time for Slot Booking',
            'h1' => '{exam-name} Slot Booking {year} - Dates & Time for Slot Booking',
            'description' => 'Students can choose the date and time of the {exam-name} exam according to their preference. Check stepwise procedure to book {exam-name} {year} slot.',
        ],
        'login' => [
            'title' => '{exam-name} {year} Login Portal - Forgot Password, Username, Steps to Retrieve',
            'h1' => '{exam-name} {year} Login Portal - Forgot Password, Username, Steps to Retrieve',
            'description' => '{exam-name} Login window for Application Form, Admit Card, Answer Key, Result and Scorecard. Create login id & password to use the {exam-name} online portal. Know what to do if you forgot your login details.',
        ],
        'topper' => [
            'title' => 'List of {exam-name} {year} Toppers - Check Name, Rank and Score',
            'h1' => 'List of {exam-name} {year} Toppers - Rank and Score',
            'description' => '{exam-name} {year} toppers list will be released with the {exam-name} result. Check the names, marks and ranks of {exam-name} {year} toppers here.',
        ],
        'response-sheet' => [
            'title' => '{exam-name} {year} Response Sheet - Download and Calculate Your Score',
            'h1' => '{exam-name} Response Sheet {year}: Download {exam-name} Response Sheet and Calculate Score',
            'description' => ' Get complete information on {exam-name} Response Sheet {year}. Check the release date, download link and learn how to calculate score using {exam-name} Response Sheet.',
        ],
        'participating-colleges' => [
            'title' => '{exam-name} Participating Institutes {year} - Check the Top Colleges List',
            'h1' => '{exam-name} Participating Institutes {year} - Check the Top Colleges List',
            'description' => '{exam-name} participating colleges {year} will be published on the official website. Candidates can check these {exam-name} participating institutes here.',
        ],
        'phase-1' => [
            'title' => '{exam-name} {year} Session 1: Dates, Registration, Syllabus, Exam Pattern',
            'h1' => '{exam-name} {year} Session 1: Dates, Registration, Syllabus, Exam Pattern',
            'description' => '{exam-name} {year} Session 1 result was declared on the official website. Check here, for details about {exam-name} Answer Key, cut off, counselling, accepting colleges and much more.',
        ],
        'phase-2' => [
            'title' => '{exam-name} {year} Session 1: Dates, Registration, Syllabus, Exam Pattern',
            'h1' => '{exam-name} {year} Session 1: Dates, Registration, Syllabus, Exam Pattern',
            'description' => '{exam-name} {year} Session 1 result was declared on the official website. Check here, for details about {exam-name} Answer Key, cut off, counselling, accepting colleges and much more.',
        ],
        'phase-3' => [
            'title' => '{exam-name} {year} Session 1: Dates, Registration, Syllabus, Exam Pattern',
            'h1' => '{exam-name} {year} Session 1: Dates, Registration, Syllabus, Exam Pattern',
            'description' => '{exam-name} {year} Session 1 result was declared on the official website. Check here, for details about {exam-name} Answer Key, cut off, counselling, accepting colleges and much more.',
        ],
        '2023-question-paper' => [
            'title' => '{exam-name} {year} Question Papers with Solutions PDFs',
            'h1' => '{exam-name} {year} Question Papers with Solutions PDFs',
            'description' => '{exam-name} {year} question papers PDFs with answer key and solutions are available here to download. Click here to download the free PDFs of {exam-name} question papers {year} for all shifts.',
        ],
        '2022-question-paper' => [
            'title' => '{exam-name} 2022 Question Papers with Solutions PDFs',
            'h1' => '{exam-name} 2022 Question Papers with Solutions PDFs',
            'description' => '{exam-name} 2022 question papers PDFs with answer key and solutions are available here to download. Click here to download the free PDFs of {exam-name} question papers 2022 for all shifts.',
        ],
        '2021-question-paper' => [
            'title' => '{exam-name} 2021 Question Papers with Solutions PDFs',
            'h1' => '{exam-name} 2021 Question Papers with Solutions PDFs',
            'description' => '{exam-name} 2021 question papers PDFs with answer key and solutions are available here to download. Click here to download the free PDFs of {exam-name} question papers 2021 for all shifts.',
        ],
        'seat-allotment' => [
            'title' => '{exam-name} Seat Allotment {year}: Dates, Procedure',
            'h1' => '{exam-name} Seat Allotment {year}: Dates, Procedure',
            'description' => '{exam-name} seat allotment {year} will begin after the counselling is over. Candidates need to register for participating in the counselling and seat allotment process.',
        ],
        'reservation' => [
            'title' => '{exam-name} Reservation Criteria  {year}: Category Wise Reservation, Quota, Documents',
            'h1' => '{exam-name} Reservation Criteria {year}: Category Wise Reservation, Quota, Documents',
            'description' => '{exam-name} Reservation Criteria {year} has been announced in the official notification. The details of reservation criteria are available on the {exam-name} {year} official website.',
        ],
        'photo-size-guidelines' => [
            'title' => '{exam-name} Photo Size: (Passport/Postcard) Format, Requirement',
            'h1' => '{exam-name} Photo Size: (Passport/Postcard) Format, Requirement',
            'description' => 'As per the {exam-name} photo size and signature guidelines {year}, the photo & signature must be in JPG/JPEG format within 10-200 KB and 4-30 KB, respectively.',
        ],
        'dress-code' => [
            'title' => '{exam-name} Dress Code {year} for Male and Female',
            'h1' => '{exam-name} Dress Code {year} for Male and Female',
            'description' => 'According to {exam-name} dress code {year}, students must wear simple clothes and avoid wearing caps, scarves, metallic materials, & jewelry items.',
        ],
        'exam-day-guidelines' => [
            'title' => '{exam-name} Exam Day Guidelines and Instructions {year}',
            'h1' => '{exam-name} Exam Day Guidelines and Instructions {year}',
            'description' => 'As per {exam-name} exam day guidelines and instructions {year}, candidates should adhere to the {exam-name} dress code, follow social distancing, & carry their admit cards.',
        ],
        'chapterwise-weightage' => [
            'title' => '{exam-name} Chapter Wise Weightage {year}: Physics, Chemistry, Maths',
            'h1' => '{exam-name} Chapter Wise Weightage {year}: Physics, Chemistry, Maths',
            'description' => 'Know {exam-name} chapter wise weightage of topics for Physics, Chemistry and Mathematics. Knowing the weightage of chapters in {exam-name} 2024 will help candidates excel in the exam.',
        ],
        'syllabus-dropdown' => [
            'title' => '{exam-name} {subject} Syllabus {year}: Topics Wise Syllabus PDFs',
            'h1' => '{exam-name} {subject} Syllabus {year}: Topics Wise Syllabus PDFs',
            'description' => 'Get the detailed list of all the topics covered in {exam-name} {subject} Syllabus {year} along with important concepts and marks distribution.',
        ],
        'qualifying-marks' => [
            'title' => '{exam-name} Qualifying Marks {year}: Passing Marks, Minimum Marks',
            'h1' => '{exam-name} Qualifying Marks {year}: Passing Marks, Minimum Marks',
            'description' => 'The expected {exam-name} Qualifying Marks {year} has been provided in this article. Candidates can check the passing marks and minimum marks.',
        ],
        'merit-list' => [
            'title' => '{exam-name} Merit List {year}: Cut off, Merit List PDF Download',
            'h1' => '{exam-name} Merit List {year}: Cut off, Merit List PDF Download',
            'description' => 'Find the complete details about the {exam-name} merit list name-wise, including state-wise toppers for the years 2022, 2021, and 2020, and how rankings are decided in case of a tie.',
        ],
        'study-material' => [
            'title' => '{exam-name} Study Material {year}: Download PDFs, Top Reference Books',
            'h1' => '{exam-name} Study Material {year}: Download PDFs, Top Reference Books',
            'description' => 'Want to ace the {exam-name} exam on the first attempt? Find here the most appropriate {exam-name} Study Material {year} for exam preparation.',
        ],
        'score-card' => [
            'title' => '{exam-name} Score Card {year}: Dates, Direct Link, Steps to Download',
            'h1' => '{exam-name} Score Card {year}: Dates, Direct Link, Steps to Download',
            'description' => '{exam-name} Score Card {year} will be released on the official website. Candidates can refer to this article for dates, direct links, and steps to download the scorecard.',
        ],
        'seat-matrix' => [
            'title' => '{exam-name} Seat Matrix {year}: Seat Allotment, Reservation',
            'h1' => '{exam-name} Seat Matrix {year}: Seat Allotment, Reservation',
            'description' => '{exam-name} Seat Matrix {year} is going to be released after {exam-name} Counselling Notification.',
        ],
        'paper-analysis' => [
            'title' => '{exam-name} Paper Analysis {year} - Shift-Wise Question Analysis, Difficulty Level',
            'h1' => '{exam-name} Paper Analysis {year} - Shift-Wise Question Analysis, Difficulty Level',
            'description' => '{exam-name} Paper Analysis {year}: Candidates can check the {exam-name} {year} analysis shiftwise question papers, difficulty level, and more at getmyuni.com.',
        ],
        'coaching' => [
            'title' => '{exam-name} Coaching {year}: Top Institute Lists Across India, Fees',
            'h1' => '{exam-name} Coaching {year}: Top Institute Lists Across India, Fees',
            'description' => '{exam-name} Coaching {year}: Candidates can get Top Institute Lists Across India, along with Fees and courses provided by the organisation.',
        ],
        'sample-papers' => [
            'title' => '{exam-name} Sample Papers {year}',
            'h1' => '{exam-name} Sample Papers {year}',
            'description' => '{exam-name} Sample Paper {year}: Check and download the latest sample papers for {exam-name} with solutions.',
        ],
        'coaching-institutes' => [
            'title' => 'Best {exam-name} Coaching Institutes in India {year}',
            'h1' => 'Best {exam-name} Coaching Institutes in India {year}',
            'description' => '{exam-name} Coaching Institutes: Check here list of best coaching institutes in India for {exam-name} {year}.',
        ],
        'percentile-predictor' => [
            'title' => '{exam-name} Predictor {year}',
            'h1' => '{exam-name} Predictor {year}',
            'description' => '{exam-name} Predictor {year}: It helps candidates to predict college based on you percentile.',
        ],
        'college-predictor' => [
            'title' => '{exam-name} College Predictor {year}',
            'h1' => '{exam-name} College Predictor {year}',
            'description' => '{exam-name} College Predictor {year}: It helps candidates to predict college based on you rank or percentile.',
        ],
        'selection-process' => [
            'title' => '{exam-name} Selection Process {year}',
            'h1' => '{exam-name} Selection Process {year}',
            'description' => 'Check {exam-name} selection procedure for the current year with application forms & complete admission details of {exam-name}  {year}.',
        ],
        '{year}' => [
            'title' => '{exam-name} Cutoff {year}: Qualifying Marks',
            'h1' => '{exam-name} Cutoff {year}: Qualifying Marks',
            'description' => 'Check qualifying marks for the exam {exam-name} {year}.',
        ],
        'answer-key-dropdown' => [
            'title' => '{exam-name} {current_year} {subject} Answer Key: Download {subject} Answer Key PDF',
            'h1' => '{exam-name} {current_year} {subject} Answer Key: Download {subject} Answer Key PDF',
            'description' => '{exam-name} {current_year} {subject} Answer Key: Download {subject} Answer Key PDF',
        ],
        'exam-pattern-dropdown' => [
            'title' => '{exam-name} {current_year} {subject} Exam Pattern: Check Marking Scheme, Exam Duration, Number of Questions',
            'h1' => '{exam-name} {current_year} {subject} Exam Pattern: Check Marking Scheme, Exam Duration, Number of Questions',
            'description' => '{exam-name} {current_year} {subject} Exam Pattern: Check Marking Scheme, Exam Duration, Number of Questions',
        ],
    ];

    /**
     * Get Random Numbers
     *
     * @param $name Name will be appended with generated unix timestamp
     *
     * @return string
     */
    public static function getRandomName($name)
    {
        return str_shuffle(time() . Inflector::slug($name));
    }

    /**
     * Get the camelcase char from slug
     *
     * @param $string
     * @param $capitalizeFirstChar
     *
     * @return string
     */
    public static function slugToCamelCase($string, $capitalizeFirstChar = false)
    {
        $str = str_replace(' ', '', ucwords(str_replace('-', ' ', $string)));

        if (!$capitalizeFirstChar) {
            $str[0] = strtolower($str[0]);
        }
        return $str;
    }

    public static $collegeDefaultSeoInfo = [
        'info' => [
            'other_subpage' => [
                'title' => '{college-name}: {ranking} {courses-fees} {fees} {contact-details} {facilities}.',
                'h1' => '{college-name}: {ranking} {courses-fees} {fees} {contact-details} {facilities}.',
                'description' => 'Get complete details on {college-name} {ranking} {courses-fees} {fees} {facilities} {contact-details} {latest-updates} and campus details.',
            ],
            'only_course_subpage' => [
                'title' => '{college-name}: Check College Details',
                'h1' => '{college-name}',
                'description' => 'Is {college-name} your dream college? Get the complete details on {college-name}.',
            ],
        ],

        'courses-fees' => [
            'with-fees' => [
                'title' => '{college-name} Fees & Courses List {#admission_year}',
                'h1' => '{college-name} Fee Structure & Courses',
                'description' => 'Browse {course_count} courses across {program_count} programs and their fees at {college-name}. Get the fee structure, duration, specialisation offered for all the courses.'
            ],
            'without-fees' => [
                'title' => '{college-name} Courses: Total Courses & Specialisations Offered.',
                'h1' => '{college-name} Courses: Total Courses & Specialisations Offered',
                'description' => 'Browse {course_count} courses across {program_count} programs at {college-name}. Get the course duration and specialisation offered for all the courses.'
            ],
        ],

        'admission' => [
            'title' => '{college-name} Admission {#admission_year}: Check {entrance_exams} {courses_offered} Admission Details.',
            'h1' => '{college-name} Admission {#admission_year}: Check {entrance_exams} {courses_offered} Admission Details.',
            'description' => 'Check all details about {college-name} admission process. Know {entrance_exams} {courses_offered} admission details for various courses.',
        ],

        'placements' => [
            'with-salary' => [
                'title' => '{college-name} Placements: Check {salary_package} {companies}',
                'h1' => '{college-name} Placements: Check {salary_package} {companies}',
                'description' => 'Want to get the complete details on {college-name} Placements? Check {salary_package} {companies}.',
            ],
            'without-salary' => [
                'title' => '{college-name} Placement Details',
                'h1' => '{college-name} Placement Details',
                'description' => 'Land your dream job after graduation from {college-name}. Get complete details on {college-name} placements.',
            ],
        ],

        'cut-off' => [
            'with-cut-off' => [
                'title' => '{college-name} Cutoff {cutoff_year}: Check Previous Year Cutoff Ranks',
                'h1' => '{college-name} Cutoff {cutoff_year}: Check Previous Year Cutoff Ranks',
                'description' => 'Check {college-name} course & exam-wise previous year cutoff with closing score/rank trends for {cutoff_year}.',
            ],
            'without-cut-off' => [
                'title' => '{college-name} Cutoff Details',
                'h1' => '{college-name} Cutoff Details',
                'description' => 'Looking for {college-name} Admission? Get the cutoff details to get admission in to {college-name}.',
            ]
        ],

        'facilities' => [
            'with_facilities' => [
                'title' => '{college-name} Infrastructure: {campus} {library} {hostel} {laboratory} {gym} {auditorium} {cafeteria}',
                'h1' => '{college-name} Infrastructure: {campus} {library} {hostel} {laboratory} {gym} {auditorium} {cafeteria}',
                'description' => 'Get complete details on {college-name} infrastructure facilities like {campus} {library} {sports} {laboratory} {hostel} {transport} {gym} {medical} {wi-fi} {cafeteria} {auditorium} and more.',
            ],
            'without_facilities' => [
                'title' => '{college-name} Infrastructure Details',
                'h1' => '{college-name} Infrastructure Details',
                'description' => 'Uncertain about the facilities at {college-name}? Get the complete details on Infrastructure facilities available at {college-name}.',
            ],
        ],

        'reviews' => [
            'title' => '{college-name} Reviews on Placements, Faculty, Facilities & more',
            'h1' => '{college-name} Reviews on Placements, Faculty, Facilities & more',
            'description' => '{college-name} Reviews: Check for latest & genuine student reviews and ratings about infrastructure, fees, academics, faculty, placement, and admission details.',
        ],

        'scholarships' => [
            'title' => '{college-name} Scholarship Details',
            'h1' => '{college-name} Scholarship Details',
            'description' => 'Get detailed information on {college-name} scholarship and finanical assistance program details.',
        ],

        'images-videos' => [
            'title' => '{college-name} Images and Videos (High Resolution Pictures & Videos)',
            'h1' => '{college-name} Images and Videos (High Resolution Pictures & Videos)',
            'description' => 'View {college-name} images, photos and videos on campus, infrastructure, hostel and more.',
        ],

        'result' => [
            'title' => '{college-name} Result 2023: Check Exam Results',
            'h1' => '{college-name} Result 2023: Check Exam Results',
            'description' => 'Check out the latest results for various courses offered at {college-name}. Check steps to download scorecard, revaluation procedure, result list, etc',
        ],

        // 'course' => [
        //     'title' => '{college-name} {course-name}: {fees} Courses, Eligibility.',
        //     'h1' => '{college-name} {course-name}: {fees} Courses, Eligibility.',
        //     'description' => 'Is {college-name} {course-name} your dream course? Get the details on {fees} eligibility for {course-name} at {college-name}.',
        // ],
        'course' => [
            'title' => '{college-name} {course-name}: Fees, Admission 2024, Placement, Courses',
            'h1' => '{college-name} {course-name}: Fees, Admission 2024, Placement, Courses',
            'description' => 'Check {college-name} {course-name} Courses & Fees structure for 2024. Also find admission procedure, cutoff, eligibility, placement details for {course-name} at {college-name}.',
        ],
        'program' => [
            'title' => '{college-name} {program-name}: Fees, Admission 2024, Placement, Eligibility',
            'h1' => '{program-name} in {college-name}',
            'description' => 'Why {college-name} {program-name}? Read more about fees, admission 2024, eligibility, placement highest & average salary packages.'
            // 'with_data' => [
            //     'title' => '{college-name} {program-name}: {fees} {placements_salary} {eligibility} {ranking} {dates}.',
            //     'h1' => '{program-name} in {college-name}',
            //     'description' => 'Why {college-name} {program-name}? Read more about {fees} {eligibility} {placements_salary} {salary_packages} {ranking} {dates}.',
            // ],
            // 'without_data' => [
            //     'title' =>  '{program-name} in {college-name}',
            //     'h1' => '{program-name} in {college-name}',
            //     'description' => 'Want to pursue {program-name} from {college-name} ? Get the complete details on {college-name} {program-name} course.',
            // ],
        ],

        'ranking' => [
            'title' => '{college-name} Ranking 2023: NIRF & Other Rankings',
            'h1' => '{college-name} Ranking in India',
            // 'h1' => '{college-name} Ranking {#admission_year}: NIRF & Other Rankings',
            'description' => 'Want to know the {college-name} Ranking {#admission_year}? Get the NIRF and other rankings for the current and previous years',
        ],

        'alumni' => [
            'title' => '{college-name} Alumni: List, Association, Official Portal',
            'h1' => '{college-name} Alumni:  List, Association, Official Portal',
            'description' => 'Want to know the {college-name} Alumni List? Get the list of famous alumni, associations, and the official links.',
        ],

        'hostel' => [
            'title' => '{college-name} Hostel: Fees, Mess, Rooms, Seats Available',
            'h1' => '{college-name} Hostel: Fees, Mess, Rooms, Seats Available',
            'description' => 'Get {college-name} Hostel details with mess, rooms, seats available, admission procedure, and rules & regulation.',
        ],

        'application-form' => [
            'title' => '{college-name} Application Form {#admission_year}: Dates, Steps to Apply, Fees',
            'h1' => '{college-name} Application Form {#admission_year}: Dates, Steps to Apply, Fees',
            'description' => 'Want to know the {college-name} Application Form {#admission_year} details? Get the Important Dates, Fees, Eligibility, and Step by Step procedure to apply.',
        ],

        'syllabus' => [
            // 'title' => '{college-name} Syllabus: Download Course Wise Syllabus PDF',
            'title' => '{college-name} Syllabus',
            'h1' => '{college-name} Syllabus: Download Course Wise Syllabus PDF',
            // 'description' => 'Want to know the complete {college-name} Syllabus? Get the latest {college-name} Course Wise Syllabus with the download links and PDF.',
            'description' => 'Download {college-name} syllabus for all courses. Semester-wise subjects, core topics, and electives included.',
        ],

        'verdict' => [
            'title' => '{college-name}: Ranking, Courses, Fees, Admission, Placements',
            'h1' => '{college-name}: Ranking, Courses, Fees, Admission, Placements',
            'description' => 'Get complete details on {college-name} ranking, courses, fees, admission, cutoff, placements, latest updates, campus details, address, contact details, and more.',
        ],

        'admission-dropdown' => [
            'title' => '{college-name} {program-name} Admission {#admission_year}: Dates, Courses List, Admission Process',
            'h1' => '{college-name} {program-name} Admission {#admission_year}: Dates, Courses List, Admission Process',
            'description' => 'Looking for {college-name} {program-name} Admission {#admission_year} details? Get {program-name} Courses List, Important Dates, Admission Process.',
        ],

        'cut-off-dropdown' => [
            'title' => '{college-name} {program-name} Cutoff 2023, 2022, 2021, 2020',
            'h1' => '{college-name} {program-name} Cutoff 2023, 2022, 2021, 2020',
            'description' => 'Want to know the {college-name} {program-name} Cutoff 2023? Get detailed {college-name} {program-name} Cutoff for 2023, 2022, 2022, 2021, and 2020.',
        ],

        'syllabus-dropdown' => [
            'title' => '{college-name} {program-name} Syllabus: Download Course Wise Syllabus PDF',
            'h1' => '{college-name} {program-name} Syllabus: Download Course Wise Syllabus PDF',
            'description' => 'Want to know the complete {college-name} {program-name} Syllabus? Get the latest {college-name} Course Wise Syllabus with the download links and PDF.',
        ],
        'news' => [
            'title' => '{college-name} Latest News & Articles | GetMyUni',
            'h1' => '{college-name} Latest News & Articles',
            'description' => 'Get the latest news and articles on {college-name}. Stay informed about upcoming events, important updates, and expert advice.',
        ],
        'eligibility' => [
            'title' => '{college-name} Eligibility',
            'description' => 'Check eligibility criteria for courses at {college-name}. Academic qualifications and entrance exam details included.'
        ],
        'refund' => [
            'title' => '{college-name} Refund Policy',
            'description' => '{college-name} fee refund policy, withdrawal rules, and processing time. Check refund eligibility and documents required.'
        ],
        'naac-grade' => [
            'title' => '{college-name} NAAC Grade',
            'description' => '{college-name} NAAC accreditation and grade details. Check rankings, quality assessment, and accreditation status.'
        ],
        'dress-code' => [
            'title' => '{college-name} Dress Code',
            'description' => '{college-name} dress code policy for students. Uniform guidelines, formal attire requirements, and special rules explained.'
        ],
        'timings' => [
            'title' => '{college-name} College Timings',
            'description' => '{college-name} class timings, daily schedule, and exam schedules. Check lecture hours and semester-wise routine.'
        ],
        'address' => [
            'title' => '{college-name} Address',
            'description' => '{college-name} campus location, address, and directions. Find nearby landmarks and travel routes.'
        ],
        'previous-year-papers' => [
            'title' => '{college-name} Previous Year Papers',
            'description' => '{college-name} past year question papers, sample papers, and solved papers. Download for exam preparation.'
        ],
        'average-package' => [
            'title' => '{college-name} Average Package',
            'description' => '{college-name} placement average package, top recruiters, and year-wise average salary trends. Get comparison with other similar colleges.'
        ],
        'fees' => [
            'title' => '{college-name} Fees',
            'description' => '{college-name} course fees, hostel charges, and payment details. Check tuition and other costs here.'
        ],
        'admission-form' => [
            'title' => '{college-name} Admission Form',
            'description' => '{college-name} admission form download and application process. Apply online for courses easily.'
        ],
        'cgpa-to-percentage' => [
            'title' => '{college-name} CGPA to Percentage',
            'description' => 'Convert CGPA to percentage for {college-name} students. Official conversion formula and guidelines provided.'
        ],
        'marksheet' => [
            'title' => '{college-name} Marksheet',
            'description' => '{college-name} marksheet request, verification, and duplicate certificate process. Check how to get your marksheet.'
        ],
        'ci-pi-subpage-admission' => [
            'title' => '{college-name} {course-name} Admission',
            'description' => '{college-name} {course-name} admission process, entrance exam details, and application procedure.'
        ],
        'ci-pi-subpage-cut-off' => [
            'title' => '{college-name} {course-name} Cutoff',
            'description' => '{college-name} {course-name} cutoff marks, category-wise previous year trends, and closing ranks.'
        ],
        'ci-pi-subpage-eligibility' => [
            'title' => '{college-name} {course-name} Eligibility',
            'description' => '{college-name} {course-name} eligibility criteria, required academic qualifications, and age limits.'
        ],
        'ci-pi-subpage-placement' => [
            'title' => '{college-name} {course-name} Placement',
            'description' => '{college-name} {course-name} placement salary, top recruiters, and job roles offered. Get placement reports.'
        ]
    ];

    public static $collegeCourseDegreeList = [
        'bachelors' => 'Bachelors',
        'masters' => 'Masters',
        'doctorate' => 'Doctorate',
        'diploma' => 'Diploma',
        'postgraduate-diploma' => 'Postgraduate Diploma',
        'certificate' => 'Certificate',
        'postgraduate-certificate' => 'Postgraduate Certificate',
    ];

    //sa-lead-details

    public static function plannedYear()
    {
        return [
            ['value' => 2025, 'name' => 2025],
            ['value' => 2026, 'name' => 2026],
            ['value' => 2027, 'name' => 2027],
        ];
    }

    /**
     * List of Template Name used for Dynamic CTA
     *
     * @var array
     */
    public static $dynamicCtaTemplateName = [

        'know-more' => 'Know More',
        'ask-us' => 'Ask Us',
        'show-more' => 'Show More',
        'show-more-p' => 'Show More Paragraph',
        'cta-button' => 'CTA Button',
    ];

    /**
     * Cache Flush Data (category,page and slug)
     *
     * @var array
     */

    public static function cacheFlush()
    {
        $item = [];

        $data = [
            'News' => [
                'detail-page' => [
                    'tag' => 'news-detail-{slug}',
                    'url' => Url::toDomain() . 'news/',
                ],
                'category-page' => [
                    'tag' => 'news-category-{slug}',
                    'url' => Url::toDomain() . 'news/',
                ],
                'landing-page' => [
                    'tag' => 'news-landing-page',
                    'url' => Url::toDomain() . 'news',
                ],
            ],
        ];

        foreach ($data as $key => $values) {
            foreach ($values as $category => $value) {
                $item[$key][$category] = [
                    'category' => $category,
                    'page' => $value['tag'],
                    'slug' => $value['url'],
                ];
            }
        }

        return $item ?? [];
    }

    //ci page qualification data
    public static $educationalQualification = [
        '' => '-- Select option --',
        'Completed 10th' => 'Completed 10th',
        'Completed 12th' => 'Completed 12th',
        'Completed 12th [PCM]' => 'Completed 12th [PCM]',
        'Completed 12th [PCB]' => 'Completed 12th [PCB]',
        'Completed Bachelors' => 'Completed Bachelors',
        'Completed Masters' => 'Completed Masters',
        'Completed Diploma' => 'Completed Diploma',
    ];

    public static $scale = [
        '' => '-- Select option --',
        '%' => 'Percentage',
        'cgpa' => 'CGPA',
    ];

    public static $brochureEntity = [
        'college' => 'College',
        'course' => 'Course',
    ];

    public static $liveAppFormDegreeList = [
        'diploma' => 'Diploma',
        'bachelors' => 'Bachelors',
        'bachelors-masters' => 'Bachelors + Masters',
        'masters' => 'Masters',
        'doctorate' => 'Doctorate',
        'masters-doctorate' => 'Masters + Doctorate',
        'postgraduate-diploma' => 'Postgraduate Diploma',
    ];

    public static $breadCrumbList = [
        'news',
        'college',
        'ncert',
        'board',
        'exam',
        'course',
        'qna'
    ];

    public static $parseContentData = [
        '#year',
    ];

    /**
     * Parse content to replace hash tags to years
     * @param $content string
     * @return string
     */
    public static function parseMetaTopContent(string $content): string
    {
        $year = date('Y');
        $data = [];
        foreach (self::$parseContentData as $key) {
            if ($key) {
                $data[$key] = $year;
            }
        }

        return strtr(html_entity_decode($content), $data);
    }

    public static function s3Path($fileName, $fileType, $fullUrl = false, $appendPath = null)
    {
        $paths = [
            'review' => 'reviews',
            'student' => 'students',
            'article_general' => 'assets/images/articles',
            'ncert_general' => 'assets/images/ncert',
            'article_study_abroad' => 'azure/assets/images/study-abroad/articles',
            'college_banner_study_abroad' => 'azure/study-abroad/clg_banner/',
            'college_logo_study_abroad' => 'azure/study-abroad/clg_logos/',
            'board_schema' => 'azure/assets/images/schema_board',
            'board_papers' => 'azure/assets/docs/boards_papers',
            'board_article' => 'assets/images/board_articles',
            'brochure' => 'azure/assets/brochure',
            'college_genral' => 'azure/college-image/big',
            'college_logo' => 'azure/college-image/small',
            'board_genral' => 'assets/images/board-logos',
            'board_images' => 'azure/assets/images/boards',
            'board_sample_papers' => 'assets/images/main/board/paper',
            'city' => 'assets/images/city-logos',
            'dynamic_cta_file' => 'assets/dynamiccta/pdfs',
            'dynamic_cta_image' => 'assets/dynamiccta/images',
            'exam_genral' => 'assets/images/main/exam',
            'gallery' => 'azure/college-images-test',
            'news_genral' => 'assets/images/news-images',
            'user' => 'assets/images/author',
            'articles' => 'assets/images/articles/content',
            'ncert' => 'assets/images/ncert/content',
            'study-abroad' => 'azure/assets/images/study-abroad/tinymce/articles',
            'category' => 'assets/category/content',
            'exam' => 'assets/images/main/exam/content',
            'college' => 'assets/images/main/college/content',
            'board' => 'assets/images/main/board/content',
            'course' => 'assets/images/main/course/content',
            'news' => 'assets/images/main/news/content',
            'board-sample-paper' => 'assets/images/main/board-sample-paper/content',
            'ranking' => 'azure/assets/images/ranking_source_new',
            'article_audio' => 'assets/audios/articles',
            'news_audio' => 'assets/audios/news',
            'azure_college_image' => 'azure/college-image',
            'downloadables' => 'assets/downloadables',
            'clp_recruiter_logos' => 'assets/clp/recruiter_logos',
            'clp_college_logo' => 'assets/clp/college/logo',
            'clp_college_banner' => 'assets/clp/college/banner',
            'career' => 'assets/images/career',
            'career-content' => 'assets/images/career/content',
            'bde_central' => 'assets/bde-central',
            'homepage_slides' => 'assets/images/homepage/homepage_slides',
            'scholarship' => 'assets/images/scholarship',
            'scholarship_logo' => 'assets/images/scholarship_logo',
            'olympiad' => 'assets/images/olympiad',
            'olympiad-content' => 'assets/images/olympiad/content',
            'twitter' => 'assets/images/twiiter',
        ];

        if (empty($paths[$fileType])) {
            return null;
        }
        if (!empty($appendPath)) {
            $paths[$fileType] = $paths[$fileType] . '/' . $appendPath;
        }
        if (substr($fileName, 0, 1) != '/') {
            $fileName = '/' . $fileName;
        }
        $paths[$fileType] = $paths[$fileType];
        $fullPath = $paths[$fileType] . $fileName;

        if (empty($fullUrl)) {
            return $fullPath;
        } else {
            if ($fullUrl === 'path') {
                return 'https://media.getmyuni.com/' . $paths[$fileType];
                // return Yii::$app->params['aws']['s3']['url'] ?? 'https://media.getmyuni.com' . '/' . $paths[$fileType];
            } else {
                return 'https://media.getmyuni.com/' . $fullPath;
            }
        }
        // return empty($fullUrl) ? $fullPath : Yii::$app->params['aws']['s3']['url'] . '/' . $fullPath;
    }

    //lead auto fetch
    public static $leadAutoHighestQualification = [
        'diploma' =>
        [
            'name' => 'Studying/Completed 12th',
            'value' => 'studying-completed-12th',
        ],
        'bachelors' => [
            'name' => 'Studying/Completed 12th',
            'value' => 'studying-completed-12th',
        ],
        'integrated-degree' =>
        [
            'name' => 'Studying/Completed 12th',
            'value' => 'studying-completed-12th',
        ],
        'masters' =>
        [
            'name' => 'Studying/Completed Graduation',
            'value' => 'studying-completed-graduation',
        ],
        'certificate' =>
        [
            'name' => 'Studying/Completed 12th',
            'value' => 'studying-completed-12th',
        ],
        'doctorate' =>
        [
            'name' => 'Studying/Completed Masters/PG',
            'value' => 'studying-completed-masters-pg',
        ],
        'postgraduate-diploma' =>
        [
            'name' => 'Studying/Completed Graduation',
            'value' => 'studying-completed-graduation',
        ],
        'postgraduate-certificate' =>
        [
            'name' => 'Studying/Completed Graduation',
            'value' => 'studying-completed-graduation',
        ],
    ];

    //get user city and state using ipaddress
    public static function getUserLocation()
    {
        $result = [
            'cityId' => '',
            'stateId' => '',
        ];

        try {
            $reader = new Reader('/var/www/geoip/GeoIP2-City.mmdb');

            $ipaddress = self::getClientIp();
            // $ipaddress = '***************';

            if (empty($ipaddress)) {
                return $result;
            }

            $record = $reader->city($ipaddress);

            $data = [
                'state' => $record->mostSpecificSubdivision->name ?? '',
                'city' => $record->city->name ?? '',
            ];

            $data['city'] = self::$correctedCityNames[$data['city']] ?? $data['city'];

            if (empty($data['city'])) {
                return $result;
            }

            $cityDetails = (new UserService)->getCityId($data['city']);

            $result['cityId'] = $cityDetails['cityId'] ?? '';
            $result['stateId'] = $cityDetails['stateId'] ?? '';

            return $result;
        } catch (\Exception $e) {
            return $result;
        }
    }

    /*********Get City And State for webenage Event****************/

    public static function getUserLocationWebengageEvent()
    {
        $result = [
            'cityName' => '',
            'stateName' => '',
        ];

        try {
            $reader = new Reader('/var/www/geoip/GeoIP2-City.mmdb');

            $ipaddress = self::getClientIp();
            //$ipaddress = '***************';
            if (empty($ipaddress)) {
                return $result;
            }

            $record = $reader->city($ipaddress);
            $data = [
                'state' => $record->mostSpecificSubdivision->name ?? '',
                'city' => $record->city->name ?? '',
            ];
            if (empty($data['city'])) {
                return $result;
            }

            $result['cityName'] = $data['city'];
            $result['stateName'] = $data['state'];
            return $result;
        } catch (\Exception $e) {
            return $result;
        }
    }

    /*************************/

    public static $correctedCityNames = [
        'Bengaluru' => 'Bangalore',
        'Tiruchi' => 'Tiruchirapalli',
    ];

    public static function getClientIp()
    {
        $ipaddress = '';
        $headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR',
        ];

        foreach ($headers as $header) {
            if (isset($_SERVER[$header])) {
                $ipaddress = $_SERVER[$header];
                break;
            }
        }

        return $ipaddress;
    }

    public static function trackStudentActivity($type, $page = null, $location = null, $data = null, $filter = null)
    {
        $activityMapper = [
            'article-index' => ['entityType' => 'article', 'pageType' => 'landing-page'],
            'article-route-category' => ['entityType' => 'article', 'pageType' => 'category'],
            'article-route-detail' => ['entityType' => 'article', 'pageType' => 'detail'],
            'board-index' => ['entityType' => 'board', 'pageType' => 'landing-page'],
            'board-detail' => [
                'date-sheet' => ['entityType' => 'board', 'pageType' => 'date-sheet'],
                'syllabus' => ['entityType' => 'board', 'pageType' => 'syllabus'],
                'exam-pattern' => ['entityType' => 'board', 'pageType' => 'exam-pattern'],
                'registration-form' => ['entityType' => 'board', 'pageType' => 'registration-form'],
                'admit-card' => ['entityType' => 'board', 'pageType' => 'admit-card'],
                'results' => ['entityType' => 'board', 'pageType' => 'result'],
                'supplementary' => ['entityType' => 'board', 'pageType' => 'supplementary'],
                'books' => ['entityType' => 'board', 'pageType' => 'books'],
                'answer-key' => ['entityType' => 'board', 'pageType' => 'answer-key'],
                'preparation' => ['entityType' => 'board', 'pageType' => 'preparation'],
                'sample-papers' => ['entityType' => 'board', 'pageType' => 'sample-paper'],
            ],
            'all-colleges' => [
                'city' => ['entityType' => 'college-listing', 'pageType' => 'city'],
                'state' => ['entityType' => 'college-listing', 'pageType' => 'state'],
                'all' => ['entityType' => 'college-listing', 'pageType' => 'landing-page'],
            ],
            'college-index' => [
                'facilities' => ['entityType' => 'college', 'pageType' => 'infrastructure'],
                'images-videos' => ['entityType' => 'college', 'pageType' => 'gallery'],
            ],
            'college-review' => ['entityType' => 'college', 'pageType' => 'review'],
            'college-admissions' => ['entityType' => 'college', 'pageType' => 'review'],
            'course-detail' => [
                'about' => ['entityType' => 'course', 'pageType' => 'detail-page'],
                'jobs-scope-salary' => ['entityType' => 'course', 'pageType' => 'jobs-scope-salary-and-placements'],
                'syllabus-subjects' => ['entityType' => 'course', 'pageType' => 'syllabus'],
            ],
            'course-index' => ['entityType' => 'course', 'pageType' => 'landing-page'],
            'exam-home' => ['entityType' => 'exam', 'pageType' => 'landing-page'],
            'exam-detail' => [
                'admit-card' => ['entityType' => 'exam', 'pageType' => 'admit-card'],
                'form' => ['entityType' => 'exam', 'pageType' => 'application-form-details'],
            ],
            'exam-filter' => ['entityType' => 'exam', 'pageType' => 'category'],
            'news-index' => ['entityType' => 'news', 'pageType' => 'landing-page'],
            'news-detail' => ['entityType' => 'news', 'pageType' => 'detail-page'],
            'news-route-category' => ['entityType' => 'news', 'pageType' => 'category'],
            'review-index' => ['entityType' => 'review', 'pageType' => 'landing-page'],
            'course-information' => ['entityType' => 'college', 'pageType' => 'course-information'],
            'program-information' => ['entityType' => 'college', 'pageType' => 'program-information'],
            'career-index' => ['entityType' => 'career', 'pageType' => 'landing-page'],
            'career-category' => ['entityType' => 'career', 'pageType' => 'category'],
            'career-detail' => ['entityType' => 'career', 'pageType' => 'detail'],
            'ncert-index' => ['entityType' => 'ncert', 'pageType' => 'landing-page'],
            'ncert-route-category' => ['entityType' => 'ncert', 'pageType' => 'category'],
            'ncert-route-detail' => ['entityType' => 'ncert', 'pageType' => 'detail'],
            'scholarship-index' => ['entityType' => 'scholarship', 'pageType' => 'landing-page'],
            'scholarship-category' => ['entityType' => 'scholarship', 'pageType' => 'category'],
            'scholarship-detail' => ['entityType' => 'scholarship', 'pageType' => 'detail'],
        ];

        if ($type == 'college-filter') {
            $urlType = null;
            if (($filter == 'private' || $filter == 'public') && in_array($filter, array_keys($data['selectedFilters'] ?? []))) {
                $urlType = 'private/ public';
            } elseif (in_array($filter, array_keys($data['filters']['Streams'] ?? []))) {
                $urlType = 'stream';
            } elseif (in_array($filter, array_keys($data['filters']['Specialization'] ?? []))) {
                $urlType = 'specialization';
            } elseif (in_array($filter, array_keys($data['filters']['Courses'] ?? []))) {
                $urlType = 'course';
            } elseif (in_array($filter, array_keys($data['selectedFilters'] ?? []))) {
                $urlType = 'exam';
            }
            if ($location != null) {
                if ($data['colleges']['0']['city_slug'] == $location) {
                    return ['entityType' => 'college-listing', 'pageType' => $urlType . ' + city'];
                } elseif ($data['colleges']['0']['state_slug'] == $location) {
                    return ['entityType' => 'college-listing', 'pageType' => $urlType . ' + state'];
                }
            } else {
                return ['entityType' => 'college-listing', 'pageType' => $urlType];
            }
        } elseif ($type == 'all-colleges') {
            if ($location != null) {
                if (empty($data['colleges'])) {
                    return $activityMapper[$type]['all'];
                } else {
                    if ($data['colleges']['0']['city_slug'] == $location) {
                        return $activityMapper[$type]['city'];
                    } elseif ($data['colleges']['0']['state_slug'] == $location) {
                        return $activityMapper[$type]['state'];
                    }
                }
            } else {
                return $activityMapper[$type]['all'];
            }
        } elseif ($type == 'board-detail' || $type == 'college-index' || $type == 'course-detail' || $type == 'exam-detail') {
            if ($location == 'drop-down') {
                if ($type == 'board-detail') {
                    return ['entityType' => 'board', 'pageType' => $page];
                }
            }
        } else {
            return $activityMapper[$type] ?? ['entityType' => null, 'pageType' => null];
        }
        return ['entityType' => null, 'pageType' => null];
    }

    public static $highestQualificationIdMapping = [
        'studying-completed-10th' => 1,
        'studying-completed-12th' => 2,
        'studying-completed-diploma' => 3,
        'studying-completed-graduation' => 4,
        'studying-completed-masters-pg' => 5,
    ];

    public static function highestQualificationCourseMapping()
    {
        return [
            ['value' => 1, 'name' => 'Completed 10th', 'displayName' => 'Studying/Completed 10th'],
            ['value' => 2, 'name' => 'Completed 12th', 'displayName' => 'Studying/Completed 12th'],
            ['value' => 3, 'name' => 'Diploma - Completed', 'displayName' => 'Studying/Completed Diploma'],
            ['value' => 4, 'name' => 'Bachelors/UG - Completed', 'displayName' => 'Studying/Completed Graduation'],
            ['value' => 5, 'name' => 'Masters/PG - Completed', 'displayName' => 'Studying/Completed Masters/PG'],
        ];
    }

    public static $leadSource = [
        'organic' => 0,
        'affiliate' => 1,
        'procured' => 2,
        'google_paid_ads' => 3,
        'google_ads_lead' => 4,
        'clp' => 'clp',
        'unbounce' => 'unbounce',
    ];

    public static function educationBudget()
    {
        return [
            ['value' => 1, 'name' => 'Below 50k'],
            ['value' => 2, 'name' => '50k - 2 Lakh'],
            ['value' => 3, 'name' => '2 Lakh - 3 Lakh'],
            ['value' => 4, 'name' => '3 Lakh - 5 Lakh'],
            ['value' => 5, 'name' => 'Above 5 Lakh'],
            ['value' => 6, 'name' => 'No constraints'],
        ];
    }

    public static function leadTwelveSpecialization()
    {
        return [
            ['value' => 1, 'name' => 'PCMB'],
            ['value' => 2, 'name' => 'PCMC'],
            ['value' => 3, 'name' => 'PCME'],
            ['value' => 4, 'name' => 'Other'],
        ];
    }

    public static $educationBudgetInLakhs = [
        1 => 'Below 50k',
        2 => '50k - 2 Lakh',
        3 => '2 Lakh - 3 Lakh',
        4 => '3 Lakh - 5 Lakh',
        5 => 'Above 5 Lakh',
        6 => 'No constraints',
    ];

    public static $educationBudgetArray = [
        1 => [0, 5000],
        2 => [50000, 200000],
        3 => [200000, 300000],
        4 => [300000, 500000],
        5 => [500000],

    ];

    public static $leadSpecializationTwelveId = [
        1 => 'PCM',
        2 => 'PCMC',
        3 => 'PCME',
        4 => 'Other',
    ];

    public static function userProfileGender()
    {
        return [
            ['value' => 1, 'name' => 'Male'],
            ['value' => 2, 'name' => 'Female'],
            ['value' => 3, 'name' => 'Others'],
        ];
    }

    public static $languageCode = [
        'en' => 1,
        'hi' => 2,
        'te' => 3,
    ];
    public static $languageCodeReverse = [
        2 => 'hi',
    ];

    public static function getLangCode($lang_code)
    {
        if (empty($lang_code)) {
            return '';
        }
        $langCode = array_search($lang_code, DataHelper::$languageCode);
        if (empty($langCode)) {
            return '';
        }
        return ($langCode == 'en') ? '' : $langCode;
    }

    public static $selectlanguageCode = [
        'hi' => 2,
    ];

    public static function getLangId()
    {
        return isset(DataHelper::$languageCode[Yii::$app->language]) ? DataHelper::$languageCode[Yii::$app->language] : DataHelper::$languageCode['en'];
    }

    public static $backendNewsCtaDefault = [
        'news_detail_lead_capture_panel_right_cta1' => [
            'cta_text' => 'Subscribe',
            'web' => 'news_detail_web_lead_capture_panel_right_cta1',
            'wap' => 'news_detail_wap_top_sticky_right_cta2',
        ],
        'news_detail_lead_capture_panel_left_cta1' => [
            'cta_text' => 'Get news Alert',
            'web' => 'news_detail_web_lead_capture_panel_left_cta1',
            'wap' => 'news_detail_wap_top_sticky_left_cta1',
        ],
    ];

    public static $backendArticleCtaDefault = [
        'articles_detail_lead_capture_panel_right_cta2' => [
            'cta_text' => 'Get ₹1 Lakh Scholarship',
            'web' => 'articles_{name}_detail_web_lead_capture_panel_right_cta2',
            'wap' => 'articles_{name}_detail_wap_bottom_right_sticky_cta3',
        ],
        'articles_detail_lead_capture_panel_left_cta1' => [
            'cta_text' => 'Subscribe',
            'web' => 'articles_{name}_detail_web_lead_capture_panel_left_cta2 ',
            'wap' => 'articles_{name}_detail_wap_bottom_left_sticky_cta2',
        ],
    ];
    public static function generateCacheKey()
    {
        $fullUrl = Yii::$app->request->absoluteUrl;
        $parsedUrl = parse_url($fullUrl);
        $slug = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
            if (isset($queryParams['cache']) && $queryParams['cache'] === 'true') {
                unset($queryParams['cache']);
            }

            $query = http_build_query($queryParams);
            if ($query !== '') {
                $slug .= '?' . $query;
            }
        }

        $cacheKey = base64_encode($slug);
        return $cacheKey;
    }

    public static $modeArr = [
        'offline' => 'Offline',
        'online' => 'Online',
        'distance' => 'Distance',
        'distance_learning' => 'Distance Learning'
    ];

    public static $collegeFilter301Url = [
        'bsc-hons' => 'bsc',
        'bcom-hons' => 'bcom',
        'ba-hons' => 'ba',
        'dpharma' => 'pharmd'
    ];

    public static function getExamDropDownSubPages()
    {
        return [
            'overview' => [
                '1' => 'Phase 1',
                '2' => 'Phase 2',
                '3' => 'Phase 3'
            ],
            'eligibility' => [
                '1' => 'Reservation'
            ],
            'form' => [
                '1' => 'Registration',
                '2' => 'Photo Size Guidelines',
                '3' => 'Form Correction',
                '4' => 'Application Process',
            ],
            'exam-centres' => [
                '1' => 'Dress Code',
                '2' => 'Exam Day Guidelines'
            ],
            // 'exam-pattern' => [
            //     '1' => 'Chapterwise Weightage',
            // ],
            'syllabus' => self::$commonSubjects,
            'answer-key' => self::$commonSubjects,
            'exam-pattern' => self::$commonSubjects,
            'reference-books' => ['1' => 'Study Material'],
            'previous-years-papers' => [
                '1' => '2023 Question Paper',
                '2' => '2022 Question Paper',
                '3' => '2021 Question Paper',
                '4' => '2024 Question Paper',
            ],
            'results' => [
                '1' => 'Merit List',
                '2' => 'Score Card'
            ],
            'cut-off' => self::generateCutOffYears(),
            'counselling' => [
                '1' => 'Slot Booking',
                '2' => 'Seat Allotment',
                '3' => 'Seat Matrix'
            ]
        ];
    }

    public static $commonSubjects = [
        '1' => 'Physics',
        '2' => 'Chemistry',
        '3' => 'Mathematics',
        '4' => 'Drawing',
        '5' => 'Planning',
        '6' => 'Biology',
        '7' => 'English',
        '8' => 'Botany',
        '9' => 'Zoology',
        '10' => 'Quantitative Aptitude',
        '11' => 'Logical Reasoning & Data Interpretation',
        '12' => 'Verbal Ability and Reading Comprehension',
        '13' => 'General Awareness',
        '14' => 'Innovation and Entrepreneurship',
        '15' => 'Decision Making',
        '16' => 'General Knowledge',
        '17' => 'Language Comprehension',
        '18' => 'Agriculture',
        '19' => 'Animal Husbandry and Veterinary Science',
        '20' => 'Anthropology',
        '21' => 'Civil Engineering',
        '22' => 'Commerce & Accountancy',
        '23' => 'Economics',
        '24' => 'Electrical Engineering',
        '25' => 'Geography',
        '26' => 'Geology',
        '27' => 'History',
        '28' => 'Law',
        '29' => 'Management',
        '30' => 'Mechanical Engineering',
        '31' => 'Medical Science',
        '32' => 'Philosophy',
        '33' => 'Political Science & International Relations',
        '34' => 'Psychology',
        '35' => 'Public Administration',
        '36' => 'Sociology',
        '37' => 'Statistics',
        '38' => 'Chapterwise Weightage'
    ];

    public static function generateCutOffYears()
    {
        $cutOffYears = ['1' => 'Qualifying Marks'];
        $currentYear = date('Y');

        for ($i = 0; $i <= 5; $i++) {
            $year = $currentYear - $i;
            $cutOffYears[$i + 2] = (string)$year;
        }

        return $cutOffYears;
    }

    public static function examCutOff()
    {
        self::getExamDropDownSubPages()['cut-off'] = self::generateCutOffYears();
    }

    public static $examSeoSubPages = [
        'phase-1',
        'phase-2',
        'phase-3',
        'reservation',
        'photo-size-guidelines',
        'form-correction',
        'dress-code',
        'exam-day-guidelines',
        'chapterwise-weightage',
        'study-material',
        '2023-question-paper',
        '2022-question-paper',
        '2021-question-paper',
        'merit-list',
        'score-card',
        'qualifying-marks',
        'slot-booking',
        'seat-allotment',
        'seat-matrix'
    ];

    public static function getExamType($examData, $entity)
    {
        $recentExam = [];
        $recentExamUni = [];
        $recentExamState = [];
        $recentExamNational = [];
        foreach ($examData as $exam) {
            if ($exam['entity'] != 3) {
                continue;
            }
            $examType = Exam::find()->select(['exam_type_id'])->where(['id' => $exam['entity_id']])->one();
            if ($examType->exam_type_id == 1) {
                $recentExamUni[] = $exam;
                continue;
            } elseif ($examType->exam_type_id == 2) {
                $recentExamState[] = $exam;
                continue;
            } elseif ($examType->exam_type_id == 3) {
                $recentExamNational[] = $exam;
                continue;
            }
        }
        $recentExam = array_merge($recentExamUni, $recentExamState, $recentExamNational);
        if ($entity == 'course') {
            $recentExam = array_merge($recentExamNational, $recentExamState, $recentExamUni);
        }

        return $recentExam;
    }

    /**
     * Parse content to replace anchor tags cta button
     * @param $content string
     * @return string
     */
    public static function parseDomainUrlInContent(string $content): string
    {
        $data = [];
        $domainUrl = Url::toDomain();

        $urls = [
            'https://www.getmyuni.com/assets/images/',
            'https://www.getmyuni.com/',
            'http://www.getmyuni.com/',
            'https://www.getmyuni.com',
        ];

        foreach ($urls as $url) {
            if ((strpos($content, $url) == true) && (strpos($url, 'www.getmyuni.com') == true)) {
                $data[$url] = $domainUrl;
            } else {
                $data[$url] = 'javascript:;';
            }
        }

        return strtr(html_entity_decode($content), $data);
    }

    public static $breadCrumbListNews = [
        'news'
    ];

    public static $examBreadCrumList = [
        'home',
        'filter',
        'category',
        'index'
    ];

    public static $scienceCourses = [
        'be' => 'BE',
        'btech' => 'B.Tech',
        'bpharm' => 'B.Pharmacy',
        'bsc' => 'BSc',
        'mbbs' => 'MBBS',
        'blibisc' => 'BLIS',
        'bams' => 'BAMS',
        'bums' => 'BUMS',
        'bds' => 'BDS',
        'bca' => 'BCA'
    ];

    public static $artsCourses = [
        'ba' => 'BA',
        'bfa' => 'BFA',
        'bsw' => 'BSW',
        'bachelor-of-performing-arts' => 'BPA',
        'ba-hons' => 'BA Hons',
        'baslp' => 'BASLP',
        'bth' => 'BTh',
        'bva' => 'BVA',
        'bftech' => 'BFTech',
        'bmm' => 'BMM'
    ];

    public static $commerceCourses = [
        'chartered-accountancy-ca' => 'CA',
        'bcom' => 'BCom',
        'bcom-hons' => 'BCom Honours',
        'baf' => 'BAF',
        'bba' => 'BBA',
        'cma' => 'CMA',
        'company-secretary-cs' => 'CS',
        'chartered-financial-analyst-cfa' => 'CFA',
        'acca' => 'ACCA',
        'bbm' => 'BBM'
    ];

    public static $boardStreams = [
        'engineering' => 1,
        'management' => 2,
        'science' => 3,
        'commerce' => 4,
        'arts' => 5,
        'pharmacy' => 6,
        'medical' => 7,
        'law' => 8,
        'computer' => 9,
        'architecture' => 10,
    ];

    public static $boardStates = [
        'maharashtra' => 'Maharashtra',
        'tamil-nadu' => 'Tamil Nadu',
        'uttar-pradesh' => 'Uttar Pradesh',
        'madhya-pradesh' => 'Madhya Pradesh',
        'andhra-pradesh' => 'Andhra Pradesh',
        'karnataka' => 'Karnataka',
        'telangana' => 'Telangana',
        'kerala' => 'Kerala',
        'gujarat' => 'Gujarat',
        'haryana' => 'Haryana',
    ];

    public static $olympiadSubPages = [
        'overview' => 'Overview',
        'awards' => 'Awards',
        'scholarships' => 'Scholarships',
        'admit-card' => 'Admit Card',
        'syllabus' => 'Syllabus',
        'registration-form' => 'Registration Form',
        'sample-papers' => 'Sample Papers',
        'reference-books' => 'Reference Books',
        'results' => 'Results',
        'preparation' => 'Preparation',
        'career' => 'Career',
        'exam-date' => 'Exam Date',
        'answer-sheets' => 'Answer Sheets',
        'answer-key' => 'Answer Key'
    ];

    public static $featurArr = [
        'total_marks' => 'percentIcon',
        'duration' => 'clockIcon',
        'official_url' => 'opportunityIcon',
        'number_of_seats' => 'degreeIcon',
        'takers' => 'employmentIcon',
        'exam_fees' => 'feesIcon'
    ];

    public static $courseStates = [
        'karnataka' => 'Karnataka',
        'maharashtra' => 'Maharashtra',
        'delhi-ncr' => 'Delhi NCR',
        'tamil-nadu' => 'Tamil Nadu',
        'telangana' => 'Telangana',
        'west-bengal' => 'West Bengal',
        'rajasthan' => 'Rajasthan',
        'madhya-pradesh' => 'Madhya Pradesh',
        'gujarat' => 'Gujarat',
        'odisha' => 'Odisha'
    ];

    public static $courseCities = [
        'bangalore-bengaluru' => 'Bangalore',
        'mumbai' => 'Mumbai',
        'delhi' => 'Delhi',
        'chennai' => 'Chennai',
        'hyderabad' => 'Hyderabad',
        'kolkata' => 'Kolkata',
        'jaipur' => 'Jaipur',
        'bhopal' => 'Bhopal',
        'ahmedabad' => 'Ahmedabad',
        'bhubaneswar' => 'Bhubaneswar'
    ];

    //course mapping
    public static $levelMappingWithDegreeTableCourse = [
        'bachelors' => 1,
        'masters' => 2,
        'doctorate' => 3,
        'diploma' => 4,
        'postgraduate-diploma' => 5,
        'certificate' => 6,
        'postgraduate-certificate' => 7,
        'integrated-degree' => 8,
        'tenth' => 9,
        'eleventh' => 10,
        'twelfth' => 11,
    ];

    public static $degreeNameMapping = [
        1 => 'UG',
        2 => 'PG',
        3 => 'Ph.D',
        4 => 'Diploma',
        6 => 'Certificate',
    ];

    // sso mapping for level in cld end
    public static $mapSSOHighestLevel = [
        // 9 => 1,
        // 11 => 2,
        // 4 => 3,
        // 1 => 4,
        // 2 => 5,
        9 => 1,
        11 => 2,
        4 => 1,
        1 => 2,
        2 => 4,
    ];

    // clp mapping for level in cld end
    public static $mapCLPHighestLevel = [
        1 => 9,
        2 => 11,
        3 => 3,
        4 => 1,
        5 => 2,
    ];

    public static function checkRestrictedUrl($str)
    {
        $dom = new DomDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($str);
        libxml_use_internal_errors(false);
        $output = [];
        foreach ($dom->getElementsByTagName('a') as $item) {
            $output[] = [
                'str' => $dom->saveHTML($item),
                'href' => $item->getAttribute('href'),
                'anchorText' => $item->nodeValue
            ];
        }
        if (!empty($output)) {
            foreach ($output as $value) {
                if (preg_match('(cache=true|shiksha.com|collegedunia.com|careers360.com|zollege.in)', $value['href']) === 1) {
                    Yii::$app->session->setFlash('error', 'There might be cache url or competitor links in content.');
                    return true;
                }
            }
        }
        return false;
    }

    // cta_position for lead bucket ctas
    public static $cta_positions = [
        'top_left_cta',
        'top_right_cta',
        'bottom_left_cta',
        'bottom_right_cta',
        'card_top_cta',
        'auto_popup_cta',
        'card_right_cta',
        'card_center_cta',
        'card_left_cta',
        'card_right_cta1',
        'card_right_cta2',
        'single_top_cta',
        'pdf_mtf_cta'
    ];

    // upload drive options
    public static $exam_upload_drive = [
        'Previous Year Question Paper',
        'Memory Based Question papers',
        'Official Notifications on Announcements',
        'Candidate\'s Rejection List',
        'Exam Dates change',
        'Accepted candidate\'s list',
        'Postponements',
        'Sample Papers',
        'Mock Tests',
        'Cutoff files',
        'Merit Lists',
        'Syllabus PDFs',
        'Unofficial and Official Answer Keys',
        'Rank Lists',
        'Exam Day Guidelines PDFs',
        'Exam Date Notification',
        'Information Boucher ',
        'Previous Year Cutoff marks'
    ];

    public static $board_upload_drive = [
        'Answer keys',
        'Question paper',
        'PYQP',
        'Syllabus',
        'Official routine/date sheet',
        'Sample papers'
    ];

    public static $college_upload_drive = [
        'Brochure'
    ];

    public static $entity_cta_count = [
        '1' => '2',
        '2' => '4',
        '3' => '4',
        '4' => '4',
        '5' => '4',
        '6' => '2',
        '7' => '2',
        '8' => '2',
        '9' => '2',
        '10' => '4',
        '11' => '2',
        '12' => '2',
        '13' => '2'
    ];

    public static $utmDefaultMedium = [
        'old' => ['source' => 'getmyuni', 'medium' => 'LO01'],
        'ncert' => ['source' => 'getmyuni', 'medium' => 'LO01'],
        'career' => ['source' => 'getmyuni', 'medium' => 'LO01'],
        'review' => ['source' => 'getmyuni', 'medium' => 'LO01'],
        'college-compare' => ['source' => 'getmyuni', 'medium' => 'LO01'],
        'student-signup' => ['source' => 'getmyuni', 'medium' => 'LO01'],
        'articles' => ['source' => 'getmyuni', 'medium' => 'LA02'],
        'board' => ['source' => 'getmyuni', 'medium' => 'LB03'],
        'college' => ['source' => 'getmyuni', 'medium' => 'LC04'],
        'course' => ['source' => 'getmyuni', 'medium' => 'LR05'],
        'course_stream' => ['source' => 'getmyuni', 'medium' => 'LR05'],
        'exam' => ['source' => 'getmyuni', 'medium' => 'LE06'],
        'exam_landing' => ['source' => 'getmyuni', 'medium' => 'LE06'],
        'college-listing' => ['source' => 'getmyuni', 'medium' => 'LP07'],
        'scholarships' => ['source' => 'getmyuni', 'medium' => 'LS08'],
        'news' => ['source' => 'getmyuni', 'medium' => 'LN09'],
        'home' => ['source' => 'getmyuni', 'medium' => 'LH10'],
        'swipe-pages' => ['source' => 'getmyuni', 'medium' => 'LD11'],
        'olympiad' => ['source' => 'getmyuni', 'medium' => 'LD12'],
        'college-admissions' => ['source' => 'getmyuni', 'medium' => 'LD13'],
        'clp' => ['source' => 'getmyuni', 'medium' => 'GD01'],
    ];

    public static $googleAdsLeadCourseMapping =
    [
        'Engineering [B.Tech, B.E.,M.Tech]' => ['stream' => '11', 'level' => '1'],
        'MBA/PGDM' => ['stream' => '14', 'level' => '2'],
        'BBA' => ['stream' => '14', 'level' => '1'],
        'Law [LLB, BA LLB, BBA LLB, LLM]' => ['stream' => '13', 'level' => '1'],
        'Agriculture' => ['stream' => '1', 'level' => '1'],
        'Paramedical & Nursing' => ['stream' => '17', 'level' => '1'],
        'Science [B.Sc]' => ['stream' => '19', 'level' => '1'],
        'Design [Fashion Design, Interior Design, VFX and Other Design Courses]' => ['stream' => '9', 'level' => '1'],
        'Commerce and Banking (B.Com, M.Com)' => ['stream' => '6', 'level' => '1'],
        'Information Technology (BCA, BSc IT,MCA)' => ['stream' => '7', 'level' => '1'],
        'Pharmacy' => ['stream' => '18', 'level' => '1'],
        'Media & Mass Communication and Journalism' => ['stream' => '15', 'level' => '1'],
        'Arts & Humanities and Performing Arts' => ['stream' => '4', 'level' => '1'],
        'Hotel Management' => ['stream' => '12', 'level' => '1'],
        'Others' => ['stream' => '11', 'level' => '1'],
        'Engineering [B.Tech, M.Tech, B.E]' => ['stream' => '11', 'level' => '1'],
        'Information Technology (Bachelor in Computer Application, BSc IT, MCA)' => ['stream' => '7', 'level' => '1'],
    ];

    public static $disatnceEduationSSo = [
        1 => 3, //distance
        0 => 1 //full time
    ];

    public static $budgetDataSSO = [
        '300000-500000' => 1,
        '500000-' => 2, //5l and above
        '0-50000' => 3,
        '50000-200000' => 4,
        '200000-300000' => 5
    ];

    public static function parseDomainUrl(string $url): string
    {
        if (strpos($url, 'https://www.getmyuni.com/') !== false) {
            $url = str_replace('https://www.getmyuni.com/', \YII::$app->params['siteUrl'], $url);
        }

        return $url;
    }

    public static $ctaMappingCategory = [
        'Course' => 'course',
        'Board' => 'board',
        'Exam' => 'exam',
        'College' => 'college',
    ];

    public static $ctaEntityFieldsArr = [
        'board' => [
            'model' => 'BoardContent',
            'column_name' => 'page',
            'where' => 'board_id',
        ],
        'college' => [
            'model' => 'CollegeContent',
            'column_name' => 'sub_page',
            'where' => 'entity_id',
        ],
        'exam' => [
            'model' => 'ExamContent',
            'column_name' => 'name',
            'where' => 'exam_id',
        ],
        'course' => [
            'model' => 'CourseContent',
            'column_name' => 'page',
            'where' => 'course_id'
        ]
    ];

    public static $ctaSubPageNameRedirect = [
        'Predict Your Rank' => 'Rank Predictor',
        'Predict your Percentile' => 'Percentile Predictor',
        'Predict My College' => 'College Predictor'
    ];

    public static $ctaOtherCategoryarray = [
        2 => [
            'entity' => 'board',
            'entityRedirect' => 'boards',
            'model' => 'Board'
        ],
        3 => [
            'entity' => 'course',
            'model' => 'Course'
        ],
        4 => [
            'entity' => 'college',
            'model' => 'College'
        ],
        5 => [
            'entity' => 'exam',
            'entityRedirect' => 'exams',
            'model' => 'Exam'
        ]
    ];

    public static $ctaEntityMapping = [
        2 => 'Boards',
        3 => 'Courses',
        4 => 'Colleges',
        5 => 'Exam',
        6 => 'News',
        7 => 'College Listing',
        8 => 'Career',
        9 => 'NCERT',
        10 => 'News & Article',
        11 => 'Olympiad',
        12 => 'Scholarship',
        13 => 'College Admission',
    ];

    public static $ctEntityIdMapping = [
        'board' => 2,
        'course' => 3,
        'college' => 4,
        'exam' => 5,
        'news' => 10,
        'college-lisiting' => 7,
        'career' => 8,
        'ncert' => 9,
        'articles' => 10,
        'olympiad' => 11,
        'scholarship' => 12,
        'college-admission' => 13,
    ];

    public static $saFeesType = [
        'average_bachelors_tuition_fees' => 'average_bachelors_tuition_fees',
        'average_postgraduates_tuition_fees' => 'average_postgraduates_tuition_fees',
        'average_diploma_tuition_fees' => 'average_diploma_tuition_fees',
        'average_tuition_fees' => 'average_tuition_fees',
        'average_accomodation_fees' => 'average_accomodation_fees',
        'application_fee' => 'application_fee',
    ];

    public static $saDurationType = [
        'YEARS' => 1,
        'MONTHS' => 2,
        'YEAR' => 3,
        'WEEKS' => 4,
        'MONTH' => 5
    ];

    public static $saFeesDegreeName = [
        'average_bachelors_tuition_fees' => 'Undergraduate',
        'average_postgraduates_tuition_fees' => 'Post Graduate',
        'average_diploma_tuition_fees' => 'Diploma',
        'average_tuition_fees' => 'Average Annual Tuition Fees',
        'average_accomodation_fees' => 'Average Accommodation',
        'application_fee' => 'Application Fees',
    ];

    public static $saApplicationDate = [
        'application_process_startdate' => 'Application Start Date',
        'application_process_enddate' => 'Application End Date',
        'application_process_deadline' => 'Application Deadline'
    ];

    public static function saUniversityStatsData($college)
    {
        $dynamicData = [
            'student_faculty_ratio' => ['label' => 'Student-Faculty Ratio', 'icon' => 'CP_14'],
            'male_female_ratio' => ['label' => 'Male-Female Ratio', 'icon' => 'CP_11'],
            'international_students' => ['label' => 'International Students', 'icon' => 'CP_15'],
            'total_enrollment' => ['label' => 'Total Enrolment', 'icon' => 'CP_13']
        ];

        $data = [];
        foreach ($dynamicData as $key => $details) {
            if (!empty($college->saCollegeDetails->{$key})) {
                $data[] = [
                    'key' => $key,
                    'label' => $details['label'],
                    'icon' => $details['icon']
                ];
            }
        }

        return $data;
    }

    public static function saAdmissionDetails($college)
    {
        return [
            [
                'key' => 'minimum_toefl',
                'label' => 'Minimum Score <br /> TOEFL Requirements',
                'value' => $college->saCollegeDetails->minimum_toefl ?? null,
            ],
            [
                'key' => 'minimum_ielts',
                'label' => 'Minimum Score <br /> IELTS Requirements',
                'value' => $college->saCollegeDetails->minimum_ielts ?? null,
            ],
            [
                'key' => 'application_website',
                'label' => 'Application Website',
                'value' => $college->saCollegeDetails->application_website ?? null,
                'is_link' => true,
            ],
            [
                'key' => 'accepts_direct_application',
                'label' => 'Accepts Direct Application',
                'value' => isset($college->saCollegeDetails->accepts_direct_application) &&
                    $college->saCollegeDetails->accepts_direct_application == SaCollegeDetail::DIRECT_APPLICATION_YES
                    ? 'Yes'
                    : 'No',
            ],
            [
                'key' => 'accepts_common_application',
                'label' => 'Accepts Common Application',
                'value' => isset($college->saCollegeDetails->accepts_common_application) &&
                    $college->saCollegeDetails->accepts_common_application == SaCollegeDetail::COMMON_APPLICATION_YES
                    ? 'Yes'
                    : 'No',
            ],
            [
                'key' => 'total_applications',
                'label' => 'Total Applications',
                'value' => $college->saCollegeDetails->total_applications ?? null,
            ],
        ];
    }

    public static function getSaContactDetails($college)
    {
        $contactDetails = [
            ['icon' => '#CP_6', 'value' => $college->location ?? null, 'label' => 'Location'],
            ['icon' => '#CP_7', 'value' => $college->telephone ?? null, 'label' => 'Contact No'],
            ['icon' => '#CP_8', 'value' => $college->toll_free ?? null, 'label' => 'Toll-Free No'],
            ['icon' => '#CP_9', 'value' => $college->website ?? null, 'label' => 'Website', 'isLink' => true],
            ['icon' => '#CP_10', 'value' => $college->email ?? null, 'label' => 'Email', 'isEmail' => true],
        ];

        return  $contactDetails;
    }

    public static $sideMenuIcons =
    [
        'overview' => '#CP_x5F_40',
        'university' => '#CP_x5F_25',
        'admission' => '#CP_x5F_46',
        'average_tuition' => '#CP_x5F_23',
        'application' => '#CP_x5F_26',
        'other' => '#CP_x5F_22',
        'course' => '#CP_x5F_20',
    ];

    public static $examOptions = [
        'ielts' => 'IELTS',
        'toefl' => 'TOEFL',
        'others' => 'Others',
        'none' => 'None'
    ];


    public static function getSaCollegeDefaultSeo($college)
    {
        $defaultSaSeo = [
            'admission' => [
                'title' => $college->name . ' Admission Requirements ' . date('Y') . ', Application Deadlines & Acceptance Rate',
                'description' => 'Get detailed info on ' . $college->name . ' admissions ' . date('Y') . ', acceptance rate, GRE/SAT/ACT score requirements & application deadlines',
            ],
            'ranking' => [
                'title' => $college->name . ' Ranking ' . date('Y') . ' : Get National and World rankings 2021,2020,2019.',
                'description' => 'Get the latest World Rankings and National Rankings of ' . $college->name . '. Download Brochure & Check all latest Ranks, Fees & Placement details of ' . $college->name,
            ],
        ];

        return $defaultSaSeo;
    }

    public static function ieltsBand()
    {
        return [
            '10' => 10.0,
            '9.5' => 9.5,
            '9' => 9.0,
            '8.5' => 8.5,
            '8' => 8.0,
            '7.5' => 7.5,
            '7' => 7.0,
            '6.5' => 6.5,
            '6' => 6.0,
            '5.5' => 5.5,
            '5' => 5.0,
            '4.5' => 4.5,
            '4' => 4.0,
            '3.5' => 3.5,
            '3' => 3.0,
            '2.5' => 2.5,
            '2' => 2.0,
            '1.5' => 1.5,
            '1' => 1.0
        ];
    }

    public static $boardDefaultSeoInfo = [
        'overview' => [
            '10' => '{board-name} Class 10 Exam 2025: {board-name} Class 10 2025 Date Sheet, Admit Card, Exam Pattern, Syllabus, Result, PYQP, Sample Papers',
            '12' => '{board-name} Class 12 Exam 2025: {board-name}  Class 12 2025 Date Sheet, Admit Card, Exam Pattern, Syllabus, Result, PYQP, Sample Papers',
        ],
        'date-sheet' => [
            '10' => '{board-name} Class 10 Date Sheet 2025: {board-name} 10th Date Sheet PDF Download Here, Direct Link {board-name}',
            '12' => '{board-name} Class 12 Date Sheet 2025: {board-name} 12th Date Sheet PDF Download Here for All Streams, Direct Link {board-name}'
        ],
        'syllabus' => [
            '10' => '{board-name} Class 10 Syllabus 2024-25 Released: Download Latest and Revised {board-name} Class 10th Syllabus PDF with Subject-wise',
            '12' => '{board-name} Class 12 Syllabus 2024-25 Released: Download Latest and Revised {board-name} Class 12th Syllabus PDF with Subject-wise'
        ],
        'subject-wise-syllabus' => [
            '10' => '{board-name} Class 10 {subpage-name} Latest Syllabus 2024-25: Download Latest and Revised {board-name} Class 10th {subpage-name} Syllabus PDF',
            '12' => '{board-name} Class 12 {subpage-name} Latest Syllabus 2024-25: Download Latest and Revised {board-name} Class 12th {subpage-name} Syllabus PDF'
        ],
        'previous-year-question-papers' => [
            '10' => '{board-name} Previous Year Question Papers Class 10 with Solutions: Download Free PDF',
            '12' => '{board-name} Previous Year Question Papers Class 12 with Solutions: Download Free PDF'
        ],
        'sample-papers' => [
            '10' => '{board-name} Class 10 Sample Paper 2024-25: {board-name} Sample Paper Class 10 All Subject with Solutions PDF Download',
            '12' => '{board-name} Class 12 Sample Paper 2024-25: {board-name} Sample Paper Class 12 All Subject with Solutions PDF Download'
        ],
        'exam-pattern' => [
            '10' => '{board-name} Class 10 Exam Pattern 2025: Check Subject Wise Pattern, Marking Scheme',
            '12' => '{board-name} Class 12 Exam Pattern 2025: Check Subject Wise Pattern, Marking Scheme'
        ],
        'admit-card' => [
            '10' => '{board-name} Class 10 Admit Card 2025: Date, Steps to Download {board-name} 10th Hall Ticket Online',
            '12' => '{board-name} Class 12 Admit Card 2025: Date, Steps to Download {board-name} 12th Hall Ticket Online'
        ],
        'admit-card-id-finder' => [
            '10' => '{board-name} Class 10 Admit Card ID Finder: Find out Admit Card ID Here',
            '12' => '{board-name} Class 12 Admit Card ID Finder: Find out Admit Card ID Here'
        ],
        'preparation' => [
            '10' => '{board-name} Class 10 Exam Preparation Tips 2025: Best Books, Complete Strategy & Guide',
            '12' => '{board-name} Class 12 Exam Preparation Tips 2025: Best Books, Complete Strategy & Guide'
        ],
        'answer-key' => [
            '10' => '{board-name} Class 10 Answer Key 2025: Download {board-name} Class 10 Answer Key PDFs Here',
            '12' => '{board-name} Class 12 Answer Key 2025: Download {board-name} Class 12 Answer Key PDFs Here'
        ],
        'subject-wise-answer-key' => [
            '10' => '{board-name} Class 10 {subpage-name} Answer Key 2025: Download {board-name} Class 10 Answer Key PDFs Here',
            '12' => '{board-name} Class 12 {subpage-name} Answer Key 2025: Download {board-name} Class 12 Answer Key PDFs Here'
        ],
        'books' => [
            '10' => '{board-name} Class 10 Books 2025: Check Best Books for Preparation',
            '12' => '{board-name} Class 12 Books 2025: Check Best Books for Preparation'
        ],
        'subject-wise-deleted-syllabus' => [
            '10' => '{board-name} Class 10 {subject} Deleted Syllabus 2024-25: Check Chapter Wise Deleted Syllabus',
            '12' => '{board-name} Class 12 {subject} Deleted Syllabus 2024-25: Check Chapter Wise Deleted Syllabus'
        ],
        'deleted-syllabus' => [
            '10' => '{board-name} Class 10 Deleted Syllabus {year}: Download Subject Wise Deleted Syllabus Here',
            '12' => '{board-name} Class 12 Deleted Syllabus {year}: Download Subject Wise Deleted Syllabus Here'
        ],
        'exam-centres' => [
            '10' => '{board-name} Class 10 Exam Centre List 2025: Find Out Center Locator/Finder',
            '12' => '{board-name} Class 12 Exam Centre List 2025: Find Out Center Locator/Finder'
        ],
        'grading-system' => [
            '10' => '{board-name} Class 10 Grading System 2025: Check Marking System, Exam Pattern, CGPA and Percentage Calculator',
            '12' => '{board-name} Class 12 Grading System 2025: Check Marking System, Exam Pattern, CGPA and Percentage Calculator'
        ],
        'marksheet' => [
            '10' => '{board-name} Class 10 Marksheet: Download {board-name} Class 10 Original Marksheet PDF Here',
            '12' => '{board-name} Class 12 Marksheet: Download {board-name} Class 12 Original Marksheet PDF Here'
        ],
        'passing-marks' => [
            '10' => '{board-name} Class 10 Passing Marks: Check {board-name} Class 10 Passing Maximum and Minimum Marks',
            '12' => '{board-name} Class 12 Passing Marks: Check {board-name} Class 12 Passing Maximum and Minimum Marks'
        ],
        'practical-exam-date' => [
            '10' => '{board-name} Class 10 Practical Exam Date 2025: {board-name} Class 10 Practical Exam Date PDF Download Here',
            '12' => '{board-name} Class 12 Practical Exam Date 2025: {board-name} Class 12 Practical Exam Date PDF Download Here'
        ],
        'registration-form' => [
            '10' => '{board-name} Class 10 Registration Form 2025: Check Details Here',
            '12' => '{board-name} Class 12 Registration Form 2025: Check Details Here'
        ],
        'results' => [
            '10' => '{board-name} Class 10 Result 2025: Check {board-name} Class 10th Result @{board-name}',
            '12' => '{board-name} Class 12 Result 2025: Check {board-name} Class 12th Result @{board-name}'
        ],
        'roll-number-finder' => [
            '10' => '{board-name} Class 10 Roll Number Finder: Find {board-name} Class 10 Roll Number Here',
            '12' => '{board-name} Class 12 Roll Number Finder: Find {board-name} Class 12 Roll Number Here'
        ],
        'subject-wise-supplementary-date-sheet' => [
            '10' => '{board-name} Class 10 Supplementary Date Sheet 2025: {board-name} 10th Supplementary Date Sheet PDF Download Here',
            '12' => '{board-name} Class 12 Supplementary Date Sheet 2025: {board-name} 12th Supplementary Date Sheet PDF Download Here'
        ],
        'subject-wise-supplementary-result' => [
            '10' => '{board-name} Class 10 Supplementary Result 2025: Check {board-name} Class 10 Supplementary Result @{board-name}',
            '12' => '{board-name} Class 12 Supplementary Result 2025: Check {board-name} Class 12 Supplementary Result @{board-name}'
        ],
        'toppers' => [
            '10' => '{board-name} Class 10 Toppers List 2025: Check Toppers List, Name, Marks, Percentage',
            '12' => '{board-name} Class 12 Toppers List 2025: Check Toppers List, Name, Marks, Percentage'
        ]
    ];

    public static $courseCiPiSubpageyearList = [
        '' => 'Select Year',
        '2018' => '2018',
        '2019' => '2019',
        '2020' => '2020',
        '2021' => '2021',
        '2022' => '2022',
        '2023' => '2023',
        '2024' => '2024',
        '2025' => '2025'
    ];

    public static $courseCiPiSubpageItems = [
        ['id' => 'admission', 'name' => 'Admission'],
        ['id' => 'dates', 'name' => 'Dates'],
        ['id' => 'eligibility', 'name' => 'Eligibility'],
        ['id' => 'placement', 'name' => 'Placement'],
        ['id' => 'cut-off', 'name' => 'Cut-Off'],
    ];

    public static function hasSideMenuContent($key, $sideMenu, $college, $fees, $degreeStreamArray, $contactDetails)
    {
        switch ($key) {
            case 'university':
                return !empty($sideMenu[$key]->content) ||
                    !empty($college->saCollegeDetails->student_faculty_ratio) ||
                    !empty($college->saCollegeDetails->male_female_ratio) ||
                    !empty($college->saCollegeDetails->international_students) ||
                    !empty($college->saCollegeDetails->total_enrollment);
            case 'admission':
                return !empty($sideMenu[$key]->content) ||
                    !empty($college->saCollegeDetails->minimum_toefl) ||
                    !empty($college->saCollegeDetails->minimum_ielts) ||
                    !empty($college->saCollegeDetails->application_website) ||
                    !empty($college->saCollegeDetails->accepts_direct_application) ||
                    !empty($college->saCollegeDetails->accepts_common_application) ||
                    !empty($college->saCollegeDetails->total_applications);
            case 'average_tuition':
                return !empty($sideMenu[$key]->content) ||
                    !empty($fees['average_diploma_tuition_fees']['fees']) ||
                    !empty($fees['average_postgraduates_tuition_fees']['fees']) ||
                    !empty($fees['average_bachelors_tuition_fees']['fees']);
            case 'application':
                return !empty($sideMenu[$key]->content) ||
                    !empty($college->saCollegeDetails->application_process_startdate) ||
                    !empty($college->saCollegeDetails->application_process_enddate) ||
                    !empty($college->saCollegeDetails->application_process_deadline);
            case 'other':
                return !empty($sideMenu[$key]->content) ||
                    !empty($fees);
            case 'course':
                return !empty($degreeStreamArray);
            case 'contact':
                return !empty($contactDetails);
            default:
                return !empty($sideMenu[$key]->content);
        }
    }

    public static function getCardNameExamDownloadResource($examName, $year, $category, &$samplePaperCount)
    {
        $lowerCategory = strtolower($category);

        $categoryFormats = [
            'sample paper' => "{$examName} {$year} Practice Question Paper " . $samplePaperCount++,
            'answer key' => "{$examName} {$year} Answer Key",
            'previous year question paper' => "{$examName} {$year} Previous Year Question Paper"
        ];

        foreach ($categoryFormats as $key => $format) {
            if (str_contains($lowerCategory, $key)) {
                return $format;
            }
        }

        return "{$examName} {$year} {$category}";
    }

    public static $ArticleNewsBucketDownloadableResource = [
        'Syllabus',
        'Sample Paper',
        'Registration',
        'Question Paper',
        'Previous Year Question Paper',
        'Predicted Question Paper',
        'Mock Test',
        'Important Dates',
        'Exam Prep Strategy',
        'Exam Pattern',
        'Notification Released',
        'Marking Scheme',
        'Admit Card Released',
        'Result Expected Dates',
        'Result Out',
        'Rank Predictor',
        'Rankings',
        'Placements',
        'Percentile Predictor',
        'Paper Analysis',
        'Merit List',
        'Answer Key Released'
    ];

    public static $yearDropdownCollegePredictor = [
        '2024' => '2024',
        '2025' => '2025',
        '2026' => '2026',
        '2027' => '2027'
    ];

    public static $mediaDownloadableResource = [
        'Syllabus',
        'Sample Papers',
        'Previous Year Question Paper',
        'Paper Analysis',
        'Unofficial and Official Answer Keys'
    ];

    public static $clpFeatureContents = [
        'streamsoffered' => 'Streams Offered',
        'collegeusps' => 'College USPs',
        'toprecruiters' => 'Top Recruiters',
        'ourcampuses' => 'Our Campuses',
        'about' => 'About',
        'whygetmyuni1' => 'Why Getmyuni Content 1',
        'whygetmyuni2' => 'Why Getmyuni Content 2',
        'whygetmyuni3' => 'Why Getmyuni Content 3',
        'programsoffered' => 'Programs Offered',
        'ourpartnercolleges' => 'Our Partner Colleges'
    ];

    public static $clpTemplates = [
        1 => 'generic',
        7 => 'generic_two',
        3 => 'generic_three',
        2 => 'generic_four',
        8 => 'generic_five',
        6 => 'college_based_one',
        4 => 'college_based_two',
        5 => 'college_based_three',
    ];

    public static $domainClp = [
        'https://admission.getmyuni.com/' => 'https://admission.getmyuni.com/',
        'https://www.getmyuni.com/' => 'https://www.getmyuni.com/'
    ];

    public static $whyGetmyuniImages = [
        1 => [
            'whygetmyuni1' => [
                'image' => '/yas/images/clp/generic/explore-icon.png',
                'text' => 'Explore over 12000+ colleges to find the right one for you'
            ],
            'whygetmyuni2' => [
                'image' => '/yas/images/clp/generic/elevate-icon.png',
                'text' => 'Elevate your college admissions journey with GetMyUni’s expert guidance'
            ],
            'whygetmyuni3' => [
                'image' => '/yas/images/clp/generic/users-icon.png',
                'text' => '128,000+ users trust us for reliable educational guidance'
            ],
        ],
        3 => [
            'whygetmyuni1' => [
                'image' => '/yas/images/clp/generic/explore-icon.png',
                'text' => 'Explore over 12000+ colleges to find the right one for you'
            ],
            'whygetmyuni2' => [
                'image' => '/yas/images/clp/generic/elevate-icon.png',
                'text' => 'Elevate your college admissions journey with GetMyUni’s expert guidance'
            ],
            'whygetmyuni3' => [
                'image' => '/yas/images/clp/generic/users-icon.png',
                'text' => '128,000+ users trust us for reliable educational guidance'
            ],
        ],
        7 => [
            'whygetmyuni1' => [
                'image' => '/yas/images/clp/generic/explore-icon.png',
                'text' => 'Explore over 12000+ colleges to find the right one for you'
            ],
            'whygetmyuni2' => [
                'image' => '/yas/images/clp/generic/elevate-icon.png',
                'text' => 'Elevate your college admissions journey with GetMyUni’s expert guidance'
            ],
            'whygetmyuni3' => [
                'image' => '/yas/images/clp/generic/users-icon.png',
                'text' => '128,000+ users trust us for reliable educational guidance'
            ],
        ],
        2 => [
            'whygetmyuni1' => [
                'image' => '/yas/images/clp/generic_one/image-1.png',
                'text' => 'Explore over 12000+ colleges to find the right one for you'
            ],
            'whygetmyuni2' => [
                'image' => '/yas/images/clp/generic_one/image-2.png',
                'text' => 'Elevate your college admissions journey with GetMyUni’s expert guidance'
            ],
            'whygetmyuni3' => [
                'image' => '/yas/images/clp/generic_one/image-3-.png',
                'text' => '128,000+ users trust us for reliable educational guidance'
            ],
        ],
    ];

    public static $clpProgramImages = [
        '/yas/images/clp/generic/bsc-img.jpg',
        '/yas/images/clp/generic/bdes-ani-img.jpg',
        '/yas/images/clp/generic/bdes-img.jpg',
    ];

    // public static $clpStreamImages = [
    //     4 => [
    //         '/yas/images/clp/college-based/engineering-img.jpg',
    //         '/yas/images/clp/college-based/mba-img.jpg',
    //         '/yas/images/clp/college-based/management-img.jpg',
    //     ],
    //     5 => [
    //         '/yas/images/clp/college-based-two/engg.png',
    //         '/yas/images/clp/college-based-two/mba.png',
    //     ],
    //     6 => [
    //         '/yas/images/clp/college-based-three/engineering-img.jpg',
    //         '/yas/images/clp/college-based-three/mba-img.jpg',
    //         '/yas/images/clp/college-based-three/management-img.jpg',
    //     ]
    // ];

    public static $clpStreamImagesBrandSpecific = [
        'Agriculture' => '/yas/images/clp/brand-specific/agriculture-img.jpg',
        'Animation' => '/yas/images/clp/brand-specific/animation-img.jpg',
        'Architecture' => '/yas/images/clp/brand-specific/architecture-img.jpg',
        'Arts' => '/yas/images/clp/brand-specific/arts-img.jpg',
        'Aviation' => '/yas/images/clp/brand-specific/aviation-img.jpg',
        'Commerce' => '/yas/images/clp/brand-specific/commerce-img.jpg',
        'Computer' => '/yas/images/clp/brand-specific/computer-img.jpg',
        'Dental' => '/yas/images/clp/brand-specific/dental-img.jpg',
        'Design' => '/yas/images/clp/brand-specific/design-img.jpg',
        'Education' => '/yas/images/clp/brand-specific/education-img.jpeg',
        'Engineering' => '/yas/images/clp/brand-specific/engineering-img.jpg',
        'Hotel Management' => '/yas/images/clp/brand-specific/hotel-img.jpg',
        'Law' => '/yas/images/clp/brand-specific/law-img.jpg',
        'Management' => '/yas/images/clp/brand-specific/management-img.jpeg',
        'Mass Communication' => '/yas/images/clp/brand-specific/mass-communication-img.jpeg',
        'Medical' => '/yas/images/clp/brand-specific/medical-img.jpg',
        'Paramedical' => '/yas/images/clp/brand-specific/paramedical-img.jpg',
        'Pharmacy' => '/yas/images/clp/brand-specific/pharmacy-img.jpg',
        'Science' => '/yas/images/clp/brand-specific/science-img.jpg',
        'Veterinary' => '/yas/images/clp/brand-specific/veterinary-img.jpg',
        'Vocational' => '/yas/images/clp/brand-specific/vocational-img.jpg',
    ];

    public static $modalSelectFormId =
    [
        'banner' => [
            'state' => 'user_state',
            'city' => 'user_city',
            'course' => 'user_course',
            'degree' => 'user_degree',
            'program' => 'user_program',
            'stream' => 'user_stream',
            'clpStreamConatiner' => 'clpStreamConatiner'
        ],
        'modal' => [
            'state' => 'modal_state',
            'city' => 'modal_city',
            'course' => 'modal_course',
            'degree' => 'modal_degree',
            'program' => 'modal_program',
            'stream' => 'modal_stream',
            'clpStreamConatiner' => 'clpModalStreamConatiner'
        ]
    ];

    public static $clpUspImages = [
        'color' => [
            '/yas/images/clp/college-based/usp-icon1-color.png',
            '/yas/images/clp/college-based/usp-icon2-color.png',
            '/yas/images/clp/college-based/usp-icon3-color.png',
            '/yas/images/clp/college-based/usp-icon4-color.png',
        ],
        'white' => [
            '/yas/images/clp/college-based/usp-icon1-white.png',
            '/yas/images/clp/college-based/usp-icon2-white.png',
            '/yas/images/clp/college-based/usp-icon3-white.png',
            '/yas/images/clp/college-based/usp-icon4-white.png',
        ]
    ];

    public static function getRandPwd($length = 8, $capital = true, $small = true, $number = true, $special = true)
    {
        $sets = [];
        $capital && $sets[] = 'ABCDEFGHJKMNPQRSTUVWXYZ';
        $small && $sets[] = 'abcdefghjkmnpqrstuvwxyz';
        $number && $sets[] = '23456789';
        $special && $sets[] = '!@#$%^&*';
        if (empty($sets)) {
            return;
        }
        $all = $password = '';
        foreach ($sets as $set) {
            $password .= $set[array_rand(str_split($set))];
            $all .= $set;
        }
        $all = str_split($all);
        for ($i = 0; $i < $length - count($sets); $i++) {
            $password .= $all[array_rand($all)];
        }
        $password = str_shuffle($password);
        return $password;
    }

    public static $cutOffH1DesDefaultValue = [
        'h1' => '{College_Name} {Degree_Name} {Exam_Name} Cutoff {year} - {Category_Name}',

        'multi_desc' => '{College_Name} {Degree_Name} {Exam_Name} cutoff {year} has been released for Round {Cutoff_Round_Number}. The overall {College_Name} {Degree_Name} cutoff {year} {Rank/Score/Perecentile} for {Category_Name} category is {Minimum_Closing_Rank/Score/Percentile} for {Min_Program_Name} and {Maximum_Closing_Rank/Score/Percentile} for {Max_Program_Name}. {College_Name} {Exam_Name} Cutoff {year} for {Degree_Name} admission is listed below.',

        'single_desc' => '{College_Name} {Degree_Name} {Exam_Name} cutoff {year} has been released for Round {Cutoff_Round_Number}. The overall {College_Name} {Degree_Name} cutoff {year} {Rank/Score/Perecentile} for {Category_Name} category is {Minimum_Closing_Rank/Score/Percentile}.'

    ];
}
