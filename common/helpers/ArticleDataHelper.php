<?php

namespace common\helpers;

use frontend\helpers\Url;
use Yii;

class ArticleDataHelper
{
    public static $categoryIds = [
        'Courses' => 'gmu_category_articles.3',
        'Olympiad' => 'gmu_category_articles.5',
        'Scholarships' => 'gmu_category_articles.6',
        'Boards' => 'gmu_category_articles.1',
        'Exams' => 'gmu_category_articles.4',
        'Colleges' => 'gmu_category_articles.2',
        'General' => 'gmu_articles'
    ];

    public static $categoryArticleType = [
        '1' => 'boards',
        '2' => 'colleges',
        '3' => 'courses',
        '4' => 'exams',
        '5' => 'olympiad',
        '6' => 'scholarships'
    ];

    public static $oldCommentType = [
        1 => 'articles',
        4 => 'boards',
        5 => 'colleges',
        6 => 'courses',
        7 => 'exams',
        8 => 'olympiad',
        9 => 'scholarships',
        10 => 'study-abroad'
    ];

    public static $oldArticleStatus = [
        '3' => 0,
        // '2' => 1, // marking all article as delete/draft
        '1' => '2'
    ];

    public static function getImage($image = null)
    {
        if (!empty($image)) {
            return Yii::getAlias('@articleGeneralFrontend') . '/' . $image;
        }

        return  'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
    }

    public static function getSaImage($image)
    {
        if (!empty($image)) {
            return Yii::getAlias('@studyAbroadArticleGeneralFrontend') . '/' . $image;
        }

        return 'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
    }

    public static $productMappingCta = [
        'article_product_mapping_cta' => [
            'desktopCta' => [
                1 => 'articles_detail_web_lead_capture_panel_left_cta1',
                2 => 'articles_detail_web_lead_capture_panel_right_cta2',
            ],
            'mobileCta' =>
            [
                1 => 'articles_detail_wap_bottom_left_sticky_cta2',
                2 => 'articles_detail_wap_bottom_right_sticky_cta3',
            ],
        ],
    ];

    public static $productMappingLead = [
        'colleges' => [
            'cta_text' => 'Get 1 Lakh Scholarship',
            'lead_form_title' => 'APPLY NOW TO GET ONE LAKH SCHOLARSHIP',
            'sub_heading_text' => 'Fill in your details and stand a chance to get our student scholarship',
            'image' => 'logo_image',
        ],
        'courses' => [
            'cta_text' => ' Get {slug} Course Guide',
            'lead_form_title' => 'Register to Get Course Guide',
            'sub_heading_text' => '{slug}',
        ],
        'exams' => [
            'cta_text' => '<i class="spriteIcon alarmIcon"></i> GET EXAMS ALERT',
            'lead_form_title' => 'REGISTER TO GET EXAM ALERTS',
            'sub_heading_text' => '{slug}',
            'image' => 'cover_image',
        ],
        'boards' => [
            'cta_text' => '<i class="spriteIcon alarmIcon"></i> GET BOARD ALERTS',
            'lead_form_title' => 'REGISTER TO GET Boards ALERTS',
            'sub_heading_text' => '{slug}',
            'image' => 'cover_image',
        ],
        'articles' => [
            'cta_text' => 'Register',
            'lead_form_title' => 'REGISTER TO GET ALERTS',
            'sub_heading_text' => '{slug}',
        ],
    ];

    public static function parseCtaText($ctaText, $name)
    {
        $data = strtr($ctaText, [
            '{slug}' => $name ?? '',
        ]);

        return $data ?? [];
    }
}
