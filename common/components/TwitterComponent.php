<?php

namespace common\components;

use Yii;
use yii\base\Component;
use yii\helpers\FileHelper;
use yii\httpclient\Client;

class TwitterComponent extends Component
{

    public $apiKey;
    public $apiSecret;
    public $accessToken;
    public $accessSecret;

    public function __construct()
    {
        $this->apiKey       = '*************************';
        $this->apiSecret    = 'XH5mX4prX9ml4xvAnpkpc8bgPFzXhJRgLXWfrRjdcvuZwThj9d';
        $this->accessToken  = '972413009904594944-DWeu8EKYpG0n2Pxci2yujz90tHlizUB';
        $this->accessSecret = 'IQt1fLMWHUC2FxOPEDaEvZxBU3QoroQdEszMwVd82mGPd';

        parent::__construct();
    }

    private function buildOauthHeader($url, $method, $params)
    {
        $oauth = [
            'oauth_consumer_key'     => $this->apiKey,
            'oauth_nonce'            => bin2hex(random_bytes(16)),
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp'        => time(),
            'oauth_token'            => $this->accessToken,
            'oauth_version'          => '1.0',
        ];

        // Merge the OAuth parameters with the actual parameters
        $baseParams = array_merge($oauth, $params);

        // Sort parameters by key
        ksort($baseParams);

        // Create the base string for the OAuth signature
        $baseString = strtoupper($method) . '&' . rawurlencode($url) . '&' . rawurlencode(http_build_query($baseParams, '', '&', PHP_QUERY_RFC3986));

        // Create the signing key
        $signingKey = rawurlencode($this->apiSecret) . '&' . rawurlencode($this->accessSecret);

        // Create the OAuth signature
        $oauth['oauth_signature'] = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

        // Build the Authorization header
        $header = 'OAuth ';
        $values = [];
        foreach ($oauth as $key => $value) {
            $values[] = $key . '="' . rawurlencode($value) . '"';
        }
        $header .= implode(', ', $values);

        return $header;
    }

    public function uploadMedia($imageUrl)
    {
        $mediaData = file_get_contents($imageUrl);

        if ($mediaData === false) {
            return ['error' => 'Unable to fetch image'];
        }

        $mediaUploadUrl = 'https://upload.twitter.com/1.1/media/upload.json';

        // Use base64 encoded media data
        $mediaParams = [
            'media_data' => base64_encode($mediaData),
        ];

        // Generate OAuth header
        $authHeader = $this->buildOauthHeader($mediaUploadUrl, 'POST', []);

        // Perform upload via curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $mediaUploadUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: ' . $authHeader]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $mediaParams);
        $response = curl_exec($ch);
        curl_close($ch);

        $responseObj = json_decode($response, true);
        
        return $responseObj;
    }
    
    public function postTweet($status, $mediaId = null)
    {
        $client = new Client();
        $url    = 'https://api.twitter.com/2/tweets';

        $params = ['text' => $status];
        if ($mediaId) {
            $params['media'] = [
                'media_ids' => array_map('strval', (array) $mediaId)
            ];
        }
   
        $authHeader = $this->buildOauthHeader($url, 'POST', []);

        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl($url)
            ->addHeaders([
                'Authorization' => $authHeader,
                'Content-Type'  => 'application/json',
            ])
            ->setContent(json_encode($params))
            ->send();
        return [
            'httpCode' => $response->getStatusCode(),
            'data'     => $response->getData(),
            'raw'      => $response->content,
        ];
    }
}
