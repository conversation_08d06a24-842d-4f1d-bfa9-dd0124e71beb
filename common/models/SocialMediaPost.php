<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "socail_media_post".
 *
 * @property int $id
 * @property int|null $post_section
 * @property int|null $post_id
 * @property int|null $social_media_type
 * @property string|null $title
 * @property string|null $description
 * @property string|null $hastag
 * @property string|null $schedule_at
 * @property int|null $published_status
 * @property string|null $error_log
 * @property int|null $status
 * @property string|null $created_at
 * @property int|null $created_by
 * @property string|null $updated_at
 * @property int|null $updated_by
 * @property string|null $published_at
 */
class SocialMediaPost extends \yii\db\ActiveRecord
{
    const POST_SECTION_NEWS = '1';
    const POST_SECTION_ARTICLES = '2';

    // const TYPE_FACEBOOK = '1';
    const TYPE_TWITTER = '2';

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    const PUBLISHED_STATUS_PENDING = 0;
    const PUBLISHED_STATUS_POSTED = 1;
    const PUBLISHED_STATUS_FAILED = 2;

    // const IS_PUBLISHED_YES = 1;
    // const IS_PUBLISHED_NO = 0;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'social_media_post';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['post_section', 'post_id', 'social_media_type', 'status', 'title', 'hastag','description'], 'required'],
            [['post_section', 'post_id', 'social_media_type', 'published_status', 'status', 'created_by', 'updated_by'], 'integer'],
            [['description','image'], 'string'],
            [['schedule_at', 'error_log','image_log', 'created_at', 'updated_at', 'published_at', 'published_by'], 'safe'],
            [['title', 'hastag'], 'string', 'max' => 255],
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ]
        ];
    }
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'post_section' => 'Post Section',
            'post_id' => 'Post ID',
            'social_media_type' => 'Social Media Type',
            'title' => 'Title',
            'description' => 'Description',
            'hastag' => 'Hashtags',
            'image' => 'Image',
            'schedule_at' => 'Schedule At',
            'published_status' => 'Published Status',
            'error_log' => 'Error Log',
            'status' => 'Status',
            'created_at' => 'Created At',
            'created_by' => 'Created By',
            'updated_at' => 'Updated At',
            'updated_by' => 'Updated By',
            'published_at' => 'Published At',
            'published_by' => 'Published By'
        ];
    }
    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getCreatedUser()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }

    public function getUpdatedUser()
    {
        return $this->hasOne(User::className(), ['id' => 'updated_by']);
    }

    public function getArticles()
    {
        return $this->hasOne(Article::className(), ['id' => 'post_id']);
    }

    public function getNews()
    {
        return $this->hasOne(NewsSubdomain::className(), ['id' => 'post_id']);
    }
}
