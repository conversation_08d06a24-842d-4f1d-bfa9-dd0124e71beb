<?php

namespace common\models;

use common\event\SitemapEvent;
use common\event\SitemapEventNew;
use Yii;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use Exception;
use common\helpers\DataHelper;
use yii\base\DynamicModel;
use yii\helpers\ArrayHelper;
use common\services\ElasticSearchService;
use common\services\MysqlSearchService;
use common\services\v2\NewsService;

/**
 * This is the model class for table "board".
 *
 * @property int $id
 * @property int|null $state_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $display_name
 * @property string|null $logo
 * @property int $type 0: National, 1: State, 2: Open
 * @property string|null $level
 * @property int|null $status
 * @property string|null $dates
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property BoardContent[] $boardContents
 * @property BoardExam[] $boardExams
 * @property Exam[] $exams
 */

class Board extends \yii\db\ActiveRecord
{
    public $exams;

    const LEVEL_COMMON = 13;
    const LEVEL_TENTH = 10;
    const LEVEL_TWELFTH = 12;

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    const TYPE_NATIONAL = 0;
    const TYPE_STATE = 1;
    const TYPE_OPEN = 2;

    const MODE_TENTATIVE = 0;
    const MODE_OFFICIAL = 1;

    const ENTITY_BOARD = 'board';

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'display_name',
                'immutable' => true
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'board';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            // [['name', 'slug', 'display_name'], 'required'],
            [['state_id', 'type', 'status', 'lang_code'], 'integer'],
            [['dates'], 'validateDates'],
            [['created_at', 'updated_at'], 'safe'],
            [['slug', 'lang_code'], 'unique', 'targetAttribute' => ['slug', 'lang_code']],
            [['logo'], 'file', 'skipOnEmpty' => true, 'skipOnError' => false, 'extensions' => 'webp'],
            [['logo'], 'image', 'maxWidth' => '276', 'maxHeight' => '207', 'maxSize' => 1024 * 50, 'extensions' => 'webp', 'message' => 'Image size should not be greater than 50kb'],
            [['name', 'slug', 'display_name', 'level'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'state_id' => 'State ID',
            'name' => 'Name',
            'slug' => 'Slug',
            'display_name' => 'Display Name',
            'lang_code' => 'Language Code',
            'logo' => 'Logo',
            'type' => 'Type',
            'level' => 'Level',
            'status' => 'Status',
            'dates' => 'Dates',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[BoardContents]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\BoardContentQuery
     */
    public function getBoardContents()
    {
        return $this->hasMany(BoardContent::className(), ['board_id' => 'id']);
    }

    /**
     * Gets query for [[State]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\StateQuery
     */
    public function getState()
    {
        return $this->hasOne(State::className(), ['id' => 'state_id'])->select(['name','id','slug']);
    }

    /**
     * Gets query for [[Exams]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getExams()
    {
        return $this->hasMany(Exam::className(), ['id' => 'exam_id'])->viaTable('board_exam', ['board_id' => 'id']);
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\NewsQuery
     */
    public function getNews()
    {
        $lang_code = DataHelper::getLangId();
        $tableName = NewsService::getInstance('board_news', Yii::$app->controller->id);
        return $this->hasMany(News::className(), ['id' => 'news_id'])->viaTable($tableName, ['board_id' => 'id'])->orderBy(['updated_at' => SORT_DESC])->where(['status' => News::STATUS_ACTIVE])->andWhere(['lang_code' => $lang_code])->select(['id','slug','name','banner_image','updated_at']);
    }

    /**
     * Gets query for [[Article]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ArticleQuery
     */
    public function getArticle()
    {
        $lang_code = DataHelper::getLangId();
        return $this->hasMany(Article::className(), ['id' => 'article_id'])->viaTable('article_board', ['board_id' => 'id'])->orderBy(['updated_at' => SORT_DESC])->where(['status' => Article::STATUS_ACTIVE])->andWhere(['lang_code' => $lang_code])->select(['id','slug','title','author_id','cover_image']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\BoardQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\BoardQuery(get_called_class());
    }

    /**
     * To save Exams
     *
     * @return void
     */
    public function saveExams(array $examIds = [])
    {
        if (empty($this->id) || empty($examIds)) {
            throw new Exception(Exam::class . ' id or exam ids is required');
        }

        if (!$this->isNewRecord) {
            $this->unlinkAll('exams', true);
        }

        foreach ($examIds as $examId) {
            $examModel = Exam::findOne($examId);
            if (!$examModel) {
                continue;
            }

            $this->link('exams', $examModel);
        }
        return true;
    }

    public function saveNews(array $newsIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(News::class . ' id is required');
        }

        if (empty($newsIds) || !$this->isNewRecord) {
            $this->unlinkAll('news', true);
        }

        foreach ($newsIds as $newsId) {
            $newsModel = News::findOne($newsId);
            if (!$newsModel) {
                continue;
            }

            $this->link('news', $newsModel);
        }
    }

    public function saveArticle(array $articleIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Article::class . ' id is required');
        }

        if (empty($articleIds) || !$this->isNewRecord) {
            $this->unlinkAll('article', true);
        }

        foreach ($articleIds as $articleId) {
            $articleModel = Article::findOne($articleId);
            if (!$articleModel) {
                continue;
            }

            $this->link('article', $articleModel);
        }
    }

    /**
     * Board Dates validation.
     *
     * @param $attribute
     */
    public function validateDates($attribute)
    {
        $items = $this->$attribute;

        if (!is_array($items)) {
            $items = [];
        }

        $dynamicModel = new DynamicModel(['name', 'start-date', 'end-date']);
        // $dynamicModel->addRule(['name', 'start-date', 'end-date'], 'required');

        foreach ($items as $index => $item) {
            $dynamicModel->setAttributes($item);

            if (!$dynamicModel->validate()) {
                $erros = $dynamicModel->getErrors();
                foreach ($erros as $att => $error) {
                    $key = $attribute . '[' . $index . '][' . $att . ']';
                    $this->addError($key, $error['0'] ?? 'Required field, cannot be blank.');
                }
            }
        }
    }

    // public function beforeSave($insert)
    // {
    //     $this->dates = json_encode($this->dates);
    //     if (!parent::beforeSave($insert)) {
    //         return false;
    //     }
    //     return parent::beforeSave($insert);
    // }

    // public function afterFind()
    // {
    //     $this->dates = ArrayHelper::toArray(json_decode($this->dates));
    //     return parent::afterFind();
    // }

    // update sitemap collections
    // public function afterSave($insert, $changedAttributes)
    // {
    //     if (YII_ENV == 'prod') {
    //         (new ElasticSearchService)->updateElasticSearch($this, 'board');
    //     }

    //     (new SitemapEvent())->updateBoardSitemap($this);
    //     (new MysqlSearchService)->updateMysqlSearch($this, 'board');

    //     return parent::afterSave($insert, $changedAttributes);
    // }

    public function getActiveTranslation()
    {
        $query1 = $this->hasMany(Board::className(), ['id' => 'tag_id'])->viaTable('board_translation', ['board_id' => 'id']);
        $query2 = $this->hasMany(Board::className(), ['id' => 'board_id'])->viaTable('board_translation', ['tag_id' => 'id']);
        return (new yii\db\Query())
            ->select('*')
            ->from(
                $query1->union($query2)
            )
            ->where(['status' => self::STATUS_ACTIVE])
            ->orderBy(['updated_at' => SORT_DESC])
            ->all();
    }

    public function getTranslation()
    {
        return $this->hasMany(Board::className(), ['id' => 'tag_id'])->viaTable('board_translation', ['board_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveTranslation(array $translationIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Board::class . ' id is required');
        }

        if (empty($translationIds) || !$this->isNewRecord) {
            $this->unlinkAll('translation', true);
        }

        foreach ($translationIds as $translationId) {
            $translationModel = Board::findOne($translationId);
            if (!$translationModel) {
                continue;
            }

            $this->link('translation', $translationModel);
        }
    }

    public function afterSave($insert, $changedAttributes)
    {
        (new SitemapEventNew())->generateBoardSitemap($this->id);
    
        return parent::afterSave($insert, $changedAttributes);
    }
}
