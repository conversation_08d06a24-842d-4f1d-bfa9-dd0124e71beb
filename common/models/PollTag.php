<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "poll_tag".
 *
 * @property int $id
 * @property int $entity_id
 * @property int $poll_id
 * @property string|null $expiry_date
 * @property int|null $tagged_by
 * @property int|null $modified_by
 * @property string $created_at
 * @property string $updated_at
 *
 * @property User $modifiedBy
 * @property NewsSubdomain $news
 * @property Poll $poll
 * @property User $taggedBy
 */
class PollTag extends \yii\db\ActiveRecord
{
    public static $fields = [
        'exam' => ['id', 'name'],
        'board' => ['id', 'display_name'],
        'college' => ['id', 'name'],
        'articles' => ['id', 'title'],
        'ncert' => ['id', 'title'],
        'news' => ['id', 'name'],
        'others' => ['id', 'name'],
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'poll_tag';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity_id', 'poll_id', 'expiry_date'], 'required'],
            [['entity_id'], 'unique'],
            [['entity_id', 'poll_id', 'tagged_by', 'modified_by'], 'integer'],
            [['expiry_date', 'created_at', 'updated_at'], 'safe'],
            [['modified_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['modified_by' => 'id']],
            [['entity'], 'string', 'max' => 255],
            [['poll_id'], 'exist', 'skipOnError' => true, 'targetClass' => Poll::className(), 'targetAttribute' => ['poll_id' => 'id']],
            [['tagged_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['tagged_by' => 'id']],
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            [
                'class' => \yii\behaviors\BlameableBehavior::class,
                'createdByAttribute' => 'tagged_by',
                'updatedByAttribute' => 'modified_by',
            ],
            // Audit trail
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity_id' => 'Entity ID',
            'poll_id' => 'Poll ID',
            'expiry_date' => 'Expiry Date',
            'tagged_by' => 'Tagged By',
            'modified_by' => 'Modified By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[ModifiedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getModifiedBy()
    {
        return $this->hasOne(User::className(), ['id' => 'modified_by']);
    }

    /**
     * Gets query for [[Poll]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPoll()
    {
        return $this->hasOne(Poll::className(), ['id' => 'poll_id']);
    }

    /**
     * Gets query for [[TaggedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTaggedBy()
    {
        return $this->hasOne(User::className(), ['id' => 'tagged_by']);
    }

    public function getTitle()
    {
        if (!empty($this->entity)) {
            switch ($this->entity) {
                case 'article':
                case 'articles': // handle both
                    return $this->article ? $this->article->title : '';
                case 'news':
                    return $this->news ? $this->news->name : '';
                default:
                    return '';
            }
        }
        return '';
    }

    public function getArticle()
    {
        return $this->hasOne(Article::className(), ['id' => 'entity_id']);
    }

    public function getNews()
    {
        return $this->hasOne(NewsSubdomain::className(), ['id' => 'entity_id']);
    }
}
