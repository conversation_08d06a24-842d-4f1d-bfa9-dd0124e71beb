<?php

namespace backend\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\PollTag;

/**
 * PollTagSearch represents the model behind the search form of `common\models\PollTag`.
 */
class PollTagSearch extends PollTag
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'tagged_by', 'modified_by'], 'integer'],
            [['entity_id', 'poll_id', 'entity'], 'string'],
            [['expiry_date', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = PollTag::find();
        $query->joinWith(['article', 'news', 'poll']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'poll_tag.id' => $this->id,
            // 'tagged_by' => $this->tagged_by,
            // 'modified_by' => $this->modified_by,
            // 'created_at' => $this->created_at,
            // 'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere([
            'or',
            ['like', 'article.title', $this->entity_id],
            ['like', 'news_subdomain.name', $this->entity_id]
        ]);

        $query->andFilterWhere(['like', 'poll_tag.entity', $this->entity]);
        $query->andFilterWhere(['like', 'poll.question', $this->poll_id]);

        if (!empty($this->expiry_date)) {
            $query->andWhere(['DATE(expiry_date)' => $this->expiry_date]);
        }

        return $dataProvider;
    }
}
