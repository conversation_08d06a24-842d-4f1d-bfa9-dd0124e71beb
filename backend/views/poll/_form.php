<?php

use common\helpers\DataHelper;
use common\models\Article;
use common\models\Poll;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\NewsPoll */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="news-poll-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>

    <div class="box-body table-responsive">
        <div class="text-muted well well-sm no-shadow">
            <div class="row">
                <div class="col-md-12">
                    <?= $this->render('/widget/tinymce', [
                        'form' => $form,
                        'model' => $model,
                        'entity' => 'question',
                        'type' => Article::ENTITY_ARTICLE,
                    ]) ?>
                </div>
            </div>

            <div class="row">
                <?php for ($i = 0; $i < 4; $i++): ?>
                    <div class="col-md-6">
                        <?= $form->field($model, "option_inputs[$i]")->textInput([
                            'value' => $existingOptions[$i] ?? '',
                            'placeholder' => 'Option ' . ($i + 1),
                        ])->label('Option ' . ($i + 1)) ?>
                    </div>
                <?php endfor; ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', Poll::class)); ?>
            </div>
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>