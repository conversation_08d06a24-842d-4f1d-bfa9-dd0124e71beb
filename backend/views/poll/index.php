<?php

use common\helpers\DataHelper;
use common\models\Poll;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\PollSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Polls';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="poll-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Poll', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>

    <div class="box-body table-responsive no-padding">
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'id',
                [
                    'label' => 'Question',
                    'attribute' => 'question',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return $model->question ? $model->question : null;
                    },
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', Poll::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', Poll::class)
                ],
                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>