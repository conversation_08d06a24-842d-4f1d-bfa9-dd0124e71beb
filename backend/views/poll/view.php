<?php

use common\helpers\DataHelper;
use common\models\Poll;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\Poll */

$this->title = 'Poll';
$this->params['breadcrumbs'][] = ['label' => 'Polls', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="poll-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>

    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                [
                    'label' => 'Question',
                    'attribute' => 'status',
                    'format' => 'raw',
                    'value' => $model->question,
                ],
                [
                    'label' => 'Options',
                    'format' => 'raw',
                    'value' => function () use ($model) {
                        $options = json_decode($model->options, true);
                        if (empty($options)) {
                            return '<em>No options available.</em>';
                        }
                        $html = '<table class="table table-bordered table-striped">';
                        $html .= '<thead><tr><th>Option</th><th>Value</th></tr></thead><tbody>';
                        foreach ($options as $key => $opt) {
                            $answer = isset($opt) ? Html::encode($opt) : '';
                            $html .= '<tr><td>' . ($key + 1) . "</td><td>{$answer}</td></td></tr>";
                        }
                        $html .= '</tbody></table>';
                        return $html;
                    }
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', Poll::class), $model->status)
                ],
                [
                    'label' => 'Created By',
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return $model->createdBy ? $model->createdBy->name : null;
                    },
                ],
                [
                    'label' => 'Modified By',
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->modifiedBy ? $model->modifiedBy->name : null;
                    },
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>