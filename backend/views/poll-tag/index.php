<?php

use common\models\PollTag;
use kartik\date\DatePicker;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\PollTagSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Poll Tags';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="poll-tag-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Poll Tag', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'id',
            [
                'attribute' => 'entity',
                'filter' => Html::activeDropDownList(
                    $searchModel,
                    'entity',
                    ArrayHelper::map(PollTag::find()->select('entity')->distinct()->all(), 'entity', 'entity'),
                    ['class' => 'form-control', 'prompt' => '']
                ),
            ],
            [
                'attribute' => 'entity_id',
                'label' => 'Title',
                'value' => function ($model) {
                    return $model->getTitle();
                }
            ],
            [
                'label' => 'Poll Question',
                'attribute' => 'poll_id',
                'format' => 'raw',
                'value' => function ($model) {
                    return $model->poll ? $model->poll->question : null;
                },
            ],
            [
                'attribute' => 'expiry_date',
                'format' => ['datetime', 'php:Y-m-d H:i:s'],
                'filter' => DatePicker::widget([
                    'model' => $searchModel,
                    'attribute' => 'expiry_date',
                    'type' => DatePicker::TYPE_INPUT,
                    'pluginOptions' => [
                        'autoclose' => true,
                        'format' => 'yyyy-mm-dd'
                    ]
                ]),
            ],
            ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
        ],
    ]); ?>
</div>