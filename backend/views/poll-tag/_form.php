<?php

use common\helpers\DataHelper;
use common\models\NewsSubdomain;
use common\models\Poll;
use kartik\datetime\DateTimePicker;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\PollTag */
/* @var $form yii\widgets\ActiveForm */

if (!empty($model->poll_id)) {
    $pollData = ArrayHelper::map(Poll::find()->where(['id' => $model->poll_id])->all(), 'id', 'question');
}

if (!empty($model->entity_id)) {
    $newsData = ArrayHelper::map(NewsSubdomain::find()->where(['id' => $model->entity_id])->all(), 'id', 'name');
}

$entitiesArr = DataHelper::$entities;
$excludedArr = ['NCERT', 'FILTER', 'CAREER', 'COURSE', 'EXAM', 'BOARD', 'COLLEGE', 'ARTICLE'];
foreach ($excludedArr as $value) {
    if (($key = array_search($value, $entitiesArr)) !== false) {
        unset($entitiesArr[$key]);
    }
}
?>

<div class="poll-tag-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'entity')->dropDownList($entitiesArr, [
                    'id' => 'entity',
                    'options' => [
                        'others' => ['Selected' => true],
                    ],
                    'disabled' => !$model->isNewRecord,
                ])->label('Type*') ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'expiry_date')->widget(DateTimePicker::class, [
                    'options' => ['placeholder' => 'Select expiry date & time'],
                    'pluginOptions' => [
                        'autoclose' => true,
                        'format' => 'yyyy-mm-dd hh:ii:ss',
                        'todayHighlight' => true,
                        'todayBtn' => true,
                        'startDate' => date('Y-m-d H:i'), // disables past date/time
                    ]
                ]); ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
                    'data' => !empty($model->getTitle()) ? [$model->entity_id => $model->getTitle()] : [],
                    'language' => 'en',
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => false,
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'placeholder' => '--Select--',
                        'disabled' => !$model->isNewRecord,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => Url::to(['/faq/get-list']),
                            'dataType' => 'json',
                            'data' => new JsExpression("function(params) {return {q:params.term,depdrop_parents:$('#entity').val()}; }")
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Select Articles/News*'); ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'poll_id')->widget(Select2::classname(), [
                    'data' => $pollData ?? [],
                    'options' => ['placeholder' => '--Select--'],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        // 'maximumInputLength' => 10,
                        'ajax' => [
                            'url' => ['../ajax/poll-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) { return {q: params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Poll Question'); ?>
            </div>
        </div>
        <div class="form-group">
            <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>