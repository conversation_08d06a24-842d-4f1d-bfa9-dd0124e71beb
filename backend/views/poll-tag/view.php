<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\PollTag */

$this->title = 'Poll Tag';
$this->params['breadcrumbs'][] = ['label' => 'Poll Tags', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="poll-tag-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            [
                'label' => 'Entity',
                'attribute' => 'entity',
            ],
            [
                'attribute' => 'entity_id',
                'label' => 'Title',
                'value' => function ($model) {
                    return $model->getTitle();
                }
            ],
            [
                'label' => 'Poll Question',
                'attribute' => 'poll_id',
                'format' => 'raw',
                'value' => function ($model) {
                    return $model->poll ? $model->poll->question : null;
                },
            ],
            'expiry_date:datetime',
            [
                'label' => 'Tagged By',
                'attribute' => 'tagged_by',
                'value' => function ($model) {
                    return $model->taggedBy ? $model->taggedBy->name : null;
                },
            ],
            [
                'label' => 'Modified By',
                'attribute' => 'modified_by',
                'value' => function ($model) {
                    return $model->modifiedBy ? $model->modifiedBy->name : null;
                },
            ],
            'created_at:datetime',
            'updated_at:datetime',
        ],
    ]) ?>

</div>