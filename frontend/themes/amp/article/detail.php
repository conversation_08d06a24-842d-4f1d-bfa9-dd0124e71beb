<?php

use common\helpers\ArticleDataHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Article;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use common\models\LiveUpdate;
use frontend\helpers\Ad;
use common\helpers\CourseHelper;
use common\models\Exam;
use common\services\UserService;

// utils
if (empty($article->author)) {
    $author = $article->defaultuser;
} else {
    $author = $article->author;
}
$currentUrl = Url::base(true) . Url::current();
$canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = $author && !empty($author->slug) ? ContentHelper::getUserProfilePic($author->slug) : '';
if ($entityName == 'exams') {
    $image = $article->exam[0]->cover_image;
} elseif ($entityName == 'college') {
    $image = $article->college[0]->logo_image;
} else {
    $image = Url::defaultCollegeLogo();
}

// meta key
$this->title = $article->title . ' - Getmyuni' ?? '';
$this->context->description = $article->meta_description ?? '';
$this->context->ogType = 'article';
$popularExamsPages = ['syllabus', 'application-form-details', 'admit-card', 'results'];
$interestedInPages = ['important-dates', 'syllabus', 'application-form-details', 'cut-off'];
$upcomingDisciplinePages = ['application-form-details', 'admit-card', 'sample-test-paper-content', 'results'];
$assetUrl = Yii::getAlias('@getmyuniExamAsset/');
$this->context->ampCss .= file_get_contents(Yii::getAlias('@frontend') . '/web' . Yii::$app->params['cssPath'] . 'amp/article-amp.css');


if ($category->slug == 'others') {
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, nofollow']);
}

if (!empty($article->cover_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $article->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:alt', 'content' => $article->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['href' => ArticleDataHelper::getImage($article->cover_image),  'fetchpriority' => 'high', 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => ArticleDataHelper::getImage($article->cover_image) . ' 300w', 'imagesizes' => '50vw']);
    $this->context->ogImage = ArticleDataHelper::getImage($article->cover_image);
}
if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}
$this->registerMetaTag(['name' => 'robots', 'content' => 'max-image-preview:large']);
$this->registerMetaTag(['property' => 'article:published_time', 'content' => !empty($article->published_at) ? date(DATE_ATOM, strtotime($article->published_at)) : date(DATE_ATOM, strtotime($article->created_at))]);

$this->registerMetaTag(['property' => 'article:modified_time', 'content' => date(DATE_ATOM, strtotime($article->updated_at))]);
// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if ($article->lang_code != 1) {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Articles'), 'url' => Url::toArticles(DataHelper::getLangCode($article->lang_code)), 'title' => 'Articles'];
} else {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Articles'), 'url' => Url::toArticles(), 'title' => 'Articles'];
}
if ($category->name != 'Others') {
    $this->params['breadcrumbs'][] = ['label' => $category->name, 'url' => Url::toArticleDetail($category->slug, DataHelper::getLangCode($article->lang_code)), 'title' => $category->name . ' Articles'];
}

$this->params['breadcrumbs'][] = ContentHelper::htmlDecode(stripslashes($article->h1), false);
$this->params['entity_name'] = addslashes($article->title) ?? '';
$this->params['entity_id'] = $article->id ?? 0;
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['entitySlug'] = $entitySlug ?? $article->slug;
$this->params['product_mapping_entity'] = empty($entityName) ? 'articles' : $entityName;
$this->params['product_mapping_entity_id'] = empty($entityId) ? 0 : $entityId;
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = empty($entityDisplayName) ? $article->title : $entityDisplayName;
$canonicalUrl = str_replace('amp/', '', $canonicalUrl);
$this->params['canonicalUrl'] = $canonicalUrl;

//code for translation of article
if (!empty($translation_data)) {
    if (!empty($translation_data[0]['lang'])) {
        $msg = 'Switch to Hindi';
    } else {
        $msg = 'Switch to English';
    }
    $url = Url::toArticleDetail($translation_data[0]['slug'], $translation_data[0]['lang']);
    $url = str_replace('amp/', 'hi/', $url);

    if (!empty($translation_data[0]['lang']) && $translation_data[0]['lang'] == 'hi') {
        $this->registerLinkTag(['href' =>  $url, 'rel' => 'alternate', 'hreflang' => $translation_data[0]['lang']]);
    } elseif (!empty($translation_data[0]['cu_lang']) && $translation_data[0]['cu_lang'] == 'hi') {
        $this->registerLinkTag(['href' =>  $url, 'rel' => 'alternate', 'hreflang' => 'hi']);
    }
} else {
    $url = '#';
}
$urlEn = Url::toArticleDetail($article->slug, '');
$urlEn = str_replace('amp/', '', $urlEn);

$defaulturl = Url::toArticleDetail($article->slug, '');

$defaulturl = str_replace('amp/', '', $defaulturl);
$this->registerLinkTag(['href' =>   $urlEn, 'rel' => 'alternate', 'hreflang' => 'en']);
$this->registerLinkTag(['href' =>   $defaulturl, 'rel' => 'alternate', 'hreflang' => 'x-default']);

$ctaPosition0 = $this->params['dynamicCta']['cta_position_0'] ?? null;
$ctaPosition1 = $this->params['dynamicCta']['cta_position_1'] ?? null;
$ctaPosition2 = $this->params['dynamicCta']['cta_position_2'] ?? null;
$ctaPosition3 = $this->params['dynamicCta']['cta_position_3'] ?? null;

$hasCtaPosition0 = !empty($ctaPosition0) && !empty(array_filter($ctaPosition0));
$ctaText0 = $hasCtaPosition0 ? ($ctaPosition0['cta_text'] ?? 'Free Counseling') : 'Free Counseling';
$ctaLocation0 = $hasCtaPosition0 ? ($ctaPosition0['wap'] ?? 'articles_' . $article->slug . '_detail_wap_bottom_left_sticky_cta') : 'articles_' . $article->slug . '_detail_wap_bottom_left_sticky_cta';
$formTitle0 = $hasCtaPosition0 ? ($ctaPosition0['lead_form_title'] ?? 'Signup to continue') : 'Signup to continue';

$hasCtaPosition1 = !empty($ctaPosition1) && !empty(array_filter($ctaPosition1));
$ctaText1 = $hasCtaPosition1 ? ($ctaPosition1['cta_text'] ?? 'Apply Now') : 'Apply Now';
$ctaLocation1 = $hasCtaPosition1 ? ($ctaPosition1['wap'] ?? 'articles_' . $article->slug . '_detail_wap_bottom_right_sticky_cta') : 'articles_' . $article->slug . '_detail_wap_bottom_right_sticky_cta';
$formTitle1 = $hasCtaPosition1 ? ($ctaPosition1['lead_form_title'] ?? 'Signup to continue') : 'Signup to continue';

$hasCtaPosition2 = !empty($ctaPosition2) && !empty(array_filter($ctaPosition2));
$ctaText2 = $hasCtaPosition2 ? ($ctaPosition2['cta_text'] ?? 'Register') : 'Register';
$ctaLocation2 = $hasCtaPosition2 ? ($ctaPosition2['wap'] ?? 'articles_' . $article->slug . '_detail_wap_top_left_sticky_cta') : 'articles_' . $article->slug . '_detail_wap_top_left_sticky_cta';
$formTitle2 = $hasCtaPosition2 ? ($ctaPosition2['lead_form_title'] ?? 'Signup to continue') : 'Signup to continue';

$hasCtaPosition3 = !empty($ctaPosition3) && !empty(array_filter($ctaPosition3));
$ctaText3 = $hasCtaPosition3 ? ($ctaPosition3['cta_text'] ?? 'Apply Now') : 'Apply Now';
$ctaLocation3 = $hasCtaPosition3 ? ($ctaPosition3['wap'] ?? 'articles_' . $article->slug . '_detail_wap_top_right_sticky_cta') : 'articles_' . $article->slug . '_detail_wap_top_right_sticky_cta';
$formTitle3 = $hasCtaPosition3 ? ($ctaPosition3['lead_form_title'] ?? 'Signup to continue') : 'Signup to continue';

?>

<?php
$courseState = json_encode([]);
if (!empty($stateList)) {
    $this->params['stateList'] = $stateList;
    if (isset($stateList['data']) && !empty($stateList['data'])) {
        $courseState = json_encode(array_keys($stateList['data']));
    }
}

$h2Text = ContentHelper::getGenerateHtmlArticleAmp($article->description, '', $article->slug);
$article->description = ContentHelper::parseAmpNewContent($h2Text['content']);
if (!empty($h2Text['colText'])) {
    $styleCss = '';
    foreach ($h2Text['colText'] as $class) {
        $styleCss .= '.class-' . $class . '{ width:' . $class . 'px;}';
    }
    $this->context->ampCss .=  $styleCss;
}
?>
<div class="containerMargin">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <!--span class="spriteIcon liveIcon"></span-->
                            <h1><?= ContentHelper::htmlDecode(stripslashes($article->h1), false) ?></h1>
                            <div class="authorInfoAndTranslateBtn">
                                <div class="updated-info row">
                                    <?php if (!empty($authorImage)):?>
                                    <img class="lazyload" loading="lazy" width="60" height="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />
                                    <?php endif; ?>
                                    <span class="updatedDetails">
                                        <?php if ($author): ?>
                                            <div class="updatedBy">
                                                <?php if ($article->lang_code != 1) { ?>
                                                    <p><a href="<?= $author ? '/hi/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                                                <?php } else { ?>
                                                    <p><a href="<?= $author ? '/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                                                <?php } ?>
                                            </div>
                                            <p><span><?= Yii::$app->formatter->asDate($article->updated_at ?? 'today') ?> </span>
                                                <?php if (!empty($article->college[0])): ?>
                                                    | <a href="<?= Url::toCollege($article->college[0]['slug']) ?>"> <?= $article->college[0]['display_name'] ?? $article->college[0]['name'] ?></a>
                                                <?php elseif (!empty($article->exam[0])): ?>
                                                    | <a href="<?= Url::toExamDetail($article->exam[0]['slug']) ?>"><?= $article->exam[0]['display_name'] ?? $article->exam[0]['name'] ?></a>
                                                <?php elseif (!empty($article->course[0])): ?>
                                                    | <a href="<?= Url::toCourseDetail($article->course[0]['slug']) ?>"> <?= $article->course[0]['short_name'] ?? $article->course[0]['name'] ?></a>
                                                <?php elseif (!empty($article->board[0])): ?>
                                                    | <a href="<?= Url::toBoardDetail($article->board[0]['slug']) ?>"> <?= $article->board[0]['display_name'] ?? $article->board[0]['name'] ?></a>
                                                <?php endif; ?>
                                        <?php endif; ?>
                                            </p>
                                            <ul>
                                                <p><?= Yii::t('app', 'Share it on') ?>:</p>
                                                <li>
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $canonicalUrl ?>" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon"></a>
                                                </li>
                                                <li>
                                                    <a href="https://twitter.com/share?url=<?= $canonicalUrl ?>" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank"></a>
                                                </li>
                                            </ul>
                                    </span>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="getSupport topCta">
                        <button class="open-lead-form" id="leadpopup"
                            data-ctaLocation='<?= $ctaLocation2 ?>'
                            data-ctaText='<?= $ctaText2 ?>'
                            on="tap:AMP.setState({ hidePopup: true }), my-bindable-lightbox-lead-2.open">
                            <?= $ctaText2 ?>
                        </button>

                        <button class="open-lead-form" id="leadpopuptwo"
                            data-ctaLocation='<?= $ctaLocation3 ?>'
                            data-ctaText='<?= $ctaText3 ?>'
                            on="tap:AMP.setState({ hidePopup: true }), my-bindable-lightbox-lead-3.open">
                            <?= $ctaText3 ?>
                        </button>

                    </div>
                </section>
            </div>
            <div class="getSupport bottomCta">
                <button class="open-lead-form" id="leadpopupthree"
                    data-ctaLocation='<?= $ctaLocation0 ?>'
                    data-ctaText='<?= $ctaText0 ?>'
                    on="tap:AMP.setState({ hidePopup: true }), my-bindable-lightbox-lead-0.open">
                    <?= $ctaText0 ?>
                </button>
                <button class="open-lead-form" id="leadpopupfour"
                    data-ctaLocation='<?= $ctaLocation1 ?>'
                    data-ctaText='<?= $ctaText1 ?>'
                    on="tap:AMP.setState({ hidePopup: true }), my-bindable-lightbox-lead-1.open">
                    <?= $ctaText1 ?>
                </button>
            </div>
        </div>
        <nav class="stickyNavCls">
            <?php if (!empty($menus)): ?>
                <div class="examRelataedLinks">
                    <?php if (count($menus) > 14 && !$isMobile): ?>
                        <p class="btn_left over">
                            <i class="spriteIcon left_angle"></i>
                        </p>
                    <?php endif; ?>
                    <?php if (count($menus) > 12 && !$isMobile): ?>
                        <p class="btn_right">
                            <i class="spriteIcon right_angle"></i>
                        </p>
                    <?php endif; ?>
                    <?php if (count($menus) > 3 && $isMobile): ?>
                        <!-- <p class="btn_right">
                                <i class="spriteIcon right_angle"></i>
                            </p> -->
                    <?php endif; ?>
                    <?=
                    $this->render('partials/_menu-card', [
                        'menus' => $menus,
                        'entityName' => $entityName,
                        'entitySlug' => $entitySlug,
                        'entityDisplayName' => $entityDisplayName,
                        'stateId' => !empty($college->city) ? $college->city->state->old_id : '',
                        'interestedLocation' => !empty($college->city) ? $college->city->id : '',
                    ]);
                    ?>
                </div>
            <?php endif; ?>
        </nav>
        <div class="col-md-8">
            <main>
                <article>
                    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>background:#EAEAEA;">
                                <?php /* if ($isMobile) : ?>
                                    <?php echo Ad::unit('GMU_ARTICLES_LANDING_WAP_300x50_ATF', '[300,50]') ?>
                                <?php else : */ ?>
                                <?php //echo Ad::unit('GMU_ARTICLES_LANDING_WEB_728x90_ATF', '[728,90]')
                                ?>
                                <?php //endif;
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="articelNote">
                        <p><?= $article->meta_description ?></p>
                    </div>
                    <?php if (!empty($article->cover_image)): ?>
                        <div class="bannerImg">

                            <amp-img layout="responsive" width="1200" height="675" src="<?= ArticleDataHelper::getImage($article->cover_image) ?>" alt="<?= $article->h1 ?>"></amp-img>

                        </div><br />
                    <?php endif; ?>
                    <?php /*if (!empty($article->audio)): ?>
                        <div class="audio-container">
                            <span class="audio-text"><?= Yii::t('app', 'Listen to this article') ?></span>
                            <amp-audio controls id="myAudio" preload="none">
                                <source src="<?php echo $article->audio ?>" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </amp-audio>
                        </div>
                    <?php endif;*/ ?>

                    <div class="articleInfo">
                        <?php
                        // $createdDate = new DateTime($article->created_at);
                        // $comparisonDate = new DateTime('2024-09-03 12:00:00');
                        /*if (isset($h2Text['h2']) && !empty($h2Text['h2']) && $createdDate > $comparisonDate):
                             if (isset($h2Text['h2']) && !empty($h2Text['h2'])):
                               //$this->render('partials/_table-of-content', [
                                 'h2Content' => $h2Text['h2'],
                             ]);
                        <?php endif;*/ ?>
                        <?php
                        if (!empty($recentActivity)): ?>
                            <?= $this->render('partials/_recentActivity', [
                                'recentActivity' => $recentActivity,
                            ]) ?>
                        <?php endif; ?>
                        <?= ContentHelper::removeStyleTag(stripslashes(
                            html_entity_decode(DataHelper::parseDomainUrlInContent($article->description))
                        )) ?>
                        <?php if (!empty($tags)): ?>
                            <div class="tagsDiv">
                                <ul>
                                    <li><?= Yii::t('app', 'TAGS') ?></li>
                                    <?php foreach ($tags as $tag): ?>
                                        <li><a href="<?= Url::toTag($tag->slug) ?>" title="<?= $tag->name ?>"><?= strtoupper($tag->name) ?></a></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>

                </article>


                <?php if (!empty($faqs)): ?>
                    <amp-script src="<?= Url::toDomain() ?>yas/js/version2/_faq_of_content.js" width="1" height="1" sandbox="allow-forms" layout="container">
                        <section class="faq_section">
                            <?php if (empty($article->display_name)) { ?>
                                <h2><?= Yii::t('app', 'FAQs') ?></h2>
                            <?php } else { ?>
                                <h2><?= Yii::t('app', ' FAQs on ' . $article->display_name) ?></h2>
                            <?php } ?>
                            <div class="faqDiv">

                                <?php foreach ($faqs as $faq): ?>
                                    <div>
                                        <p class="faq_question"><?= 'Q: ' . $faq->question ?></p>
                                        <div class="faq_answer" style="display: none;">
                                            <?= 'A: ' . ContentHelper::htmlDecode(ContentHelper::parseAmpNewContent($faq->answer), false) ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>

                            </div>
                        </section>
                    </amp-script>
                <?php endif; ?>
            </main>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php //echo Ad::unit('GMU_ARTICLES_DETAIL_WAP_300x250_MTF_2', '[300,250]')
                            ?>
                            <?= $this->render('partials/_freestar_ad', [
                                'slot' => '/15188745,21840540389/FS-getmyuni-AMP/getmyuni_AMP_2'

                            ]); ?>
                        <?php else: ?>
                            <?php //echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_720x90_MTF_2', '[728,90]')
                            ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-md-4">
            <aside class="article-aside">
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <!-- <div class="getSupport"> -->
                    <!--div class="row">
                            <img width="80" height="80" class="lazyload" loading="lazy" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="" />
                            <p><!-?= //Yii::t('app', 'Get Expert Counseling and Student Scholarship') ?></p>
                        </div-->
                    <!--p class="getSupport__subheading"><?= Yii::t('app', 'Get Expert Counseling and Student Scholarship') ?></p>
                        <div class="button__row__container">
                            <div class="lead-cta" data-entity="article" data-lead_cta='1'></div>
                        </div-->
                    <!-- </div> -->
                <?php endif;
                if (!empty($upcomingExams)): ?>
                    <div class="trendingArtilce trendingArtilerList">
                        <p><?= Yii::t('app', 'UPCOMING EXAMS') ?></p>

                        <?php $upExamsCount = 0; ?>
                        <?php foreach ($upcomingExams as $upExams): ?>
                            <?php if ($upExamsCount == 6) {
                                break;
                            }
                            $upExamsCount++; ?>
                            <div class="trendingArtilerDiv row">
                                <div class="dateLabel">
                                    <span><?= Yii::$app->formatter->asDate(substr($upExams->start, 0, 10), 'd') ?></span>
                                    <?= Yii::$app->formatter->asDate(substr($upExams->start, 0, 10), 'php:M') ?>
                                </div>
                                <div class="trendingArtileText">
                                    <a href="<?= Url::toExamDetail($upExams->exam->slug) ?>" title="<?= $upExams->exam->display_name ?? $upExams->exam->name ?>"><?= $upExams->exam->display_name ?? $upExams->exam->name ?></a>
                                </div>
                            </div>
                        <?php endforeach; ?>

                    </div>
                <?php endif; ?>
                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>*/
                if (!empty($recentArticles)): ?>
                    <?= $this->render('partials/_sidebar-articles', [
                        // 'trendings' => $trendings,
                        'recentArticles' => $recentArticles,
                        'article' => $article
                    ]); ?>
                <?php endif; ?>

                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="squareDiv">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php //echo Ad::unit('GMU_ARTICLES_DETAIL_WAP_300x250_MTF_1', '[300,250]')
                                ?>
                                <?= $this->render('partials/_freestar_ad_amp1', [
                                    'slot' => '/15188745,21840540389/FS-getmyuni-AMP/getmyuni_AMP_1'

                                ]); ?>
                            <?php else: ?>
                                <?php //echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_300x250_MTF_1', '[300,250]')
                                ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif;
                if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="verticleRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php //echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_300x600_MTF_3', '[300,600]')
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($coursebyStream) && !$isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => 'Other Popular ' . $streamName . ' Courses',
                        'lists' => $coursebyStream,
                        'class' => 'desktopOnly',

                    ]) ?>
                <?php endif; ?>
                <?php if (count($pages) > 1): ?>
                    <div class="quickLinks">
                        <h2><?= Yii::t('app', 'Quick Links') ?></h2>
                        <ul>
                            <?php foreach (DataHelper::examContentList() as $key => $value): ?>
                                <?php if (in_array($key, $menus)): ?>
                                    <li>


                                        <a title="<?= $exam->display_name . ' ' . ($key == 'overview' ? '' : $value) ?>" href="<?= $key == 'overview' ? Url::toExamDetail($exam->slug) : Url::toExamDetail($exam->slug, '', $key) ?>">
                                            <?= $exam->display_name . ' ' . $value ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif ?>
                <?php if (!empty($coursebyStream) && $isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => 'Other Popular ' . $streamName . ' Courses',
                        'lists' => $coursebyStream,
                    ]) ?>
                <?php endif; ?>
                <?php if (!empty($courseSpecialization) && !$isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => $parentCourseName . ' Specialization',
                        'lists' => $courseSpecialization ?? [],
                        'class' => 'desktopOnly',
                    ]) ?>
                <?php endif; ?>
                <?php if (!empty($courseSpecialization) && $isMobile): ?>
                    <?= $this->render('partials/_course-list', [
                        'title' => $parentCourseName . ' Specialization',
                        'lists' => $courseSpecialization ?? [],
                    ]) ?>
                <?php endif; ?>
            </aside>
        </div>
    </div>
    <?php /* if ($author->slug): ?>
    <div class="contentProvider">
        <div class="profilePic">
            <img class="lazyload" loading="lazy" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author->name . ' Image' ?>" />
        </div>
        <div class="profileInfo">
            <p class="name"><?= $article->author->name ?></p>
            <p class="position"><?= isset($article->author->profile->job_role) ? $article->author->profile->job_role . ',' : '' ?> GETMYUNI</p>
            <p><?= isset($article->author->profile->about) ? $article->author->profile->about : '' ?></p>
        </div>
    </div>
<?php endif;*/ ?>
    <!--section class="commentSection">
        ?= $this->render('/partials/comment/_form', [
            'model' => $commentModel,
            'entity' => Article::ENTITY_ARTICLE,
            'entity_id' => $article->id
        ]) ?-->
    <!--  //$this->render('/partials/comment/_comment', [
        //     'comments' => $comments,
        //     'entity' => Article::ENTITY_ARTICLE,
        //     'entityId' => $article->id
        // ]) ?> 
    </section-->
    <!-- reviews widget -->
    <?php if (!empty($reviews)): ?>
        <?= $this->render('partials/_review-card', [
            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
            'reviews' => $reviews,
            'rating' => $revRating,
            'distrbutionRating' => $revDistributionRating,
            'categoryRating' => $revCategoryRating,
            'viewAllURL' => Url::toCollege($college->slug, 'reviews')
        ]) ?>
    <?php endif; ?>
    <?php
    if (!empty($liveApplicationForm) && !$isMobile): ?>
        <?=
        $this->render('partials/_live-application-desktop', [
            'liveApplicationForm' => $liveApplicationForm,
            'entityName' => $entityName,
            'assetUrl' => DataHelper::s3Path(null, 'azure_college_image', 'path') . '/',
            'entityDisplayName' => $entityDisplayName,
        ])
        ?>
    <?php endif; ?>
    <?php if (!empty($liveApplicationForm) && $isMobile): ?>
        <?=
        $this->render('partials/_live-application-mobile', [
            'liveApplicationForm' => $liveApplicationForm,
            'entityName' => $entityName,
            'assetUrl' => DataHelper::s3Path(null, 'azure_college_image', 'path') . '/',
            'entityDisplayName' => $entityDisplayName,
        ])
        ?>
    <?php endif; ?>
    <!-- college widget -->
    <?php if (!empty($stateColleges)): ?>
        <?= $this->render('../board/partials/_colleges-card', [
            'stateColleges' => $stateColleges,
            'board' => $board,
            'assetUrl' => $assetUrl
        ]) ?>
    <?php endif; ?>

    <!-- exam widget -->
    <?php  /*if (!empty($boardExamList)): ?>
        <?= $this->render('../board/partials/_exam-card', [
            'exams' => $boardExamList,
            'board' => $board,
            'assetUrl' => $assetUrl
        ]) ?>
    <?php endif; ?>
    <!-- Board Article  -->
    <?php if (!empty($boardarticles)): ?>
        <?= $this->render('../board/partials/_board-articles', [
            'articles' => $boardarticles,
            'board' => $board,
        ]) ?>
    <?php endif;*/ ?>
    <!-- Board News  -->
    <?php if (!empty($boardExams)): ?>
        <?= $this->render('../board/partials/_board-news', [
            'boardExamNews' => $boardExams,
            'board' => $board,
        ]) ?>
    <?php endif; ?>
    <?php if (!empty($courseCollege) && $courseCollege['items']): ?>
        <?= $this->render('partials/_college-list', [
            'collegeCard' => $courseCollege['items'],
            'stream' => $course,
            'is_sponsored' => $courseCollege['is_sponsored'],
        ]); ?>
    <?php endif; ?>

    <?php
    if (!empty($courseExams)):  ?>
        <?= $this->render('partials/_exam-list', [
            'examCard' => $courseExams,
            'title' => 'Top ' . $streamName . ' Entrance Exams',
            'stream' => $course->stream
        ]); ?>
    <?php endif; ?>

    <?php if ($showOtherCourseCategory): ?>
        <section class="pageData ohterCategoryArticles">
            <h2><?= Yii::t('app', 'Other Category Courses') ?></h2>
            <div class="otherCategorySection">
                <i class="spriteIcon scrollLeft over"></i>
                <i class="spriteIcon scrollRight"></i>
                <div class="row">
                    <?php foreach (CourseHelper::$categoryArr as $key => $value): ?>
                        <div class="categoryArticles">
                            <a href="<?= Url::toCoursesStream($key) ?>">
                                <div class="categoryArticlesImg">
                                    <span class="courseSprite <?= $key ?>"></span>
                                </div>
                                <p><?= $value ?></p>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- other colleges under university -->
    <?php /*if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege->display_name) ? $parentCollege->display_name : $parentCollege->name),
            'colleges' => $affiliatedCollege,
        ]) ?>
    <?php endif; */ ?>
    <!-- gallery -->
    <?php if (!empty($gallery)): ?>
        <div class="pageData photoGallery">
            <h2 class="row"><?= Yii::t('app', 'Gallery') ?> <a href="<?= Url::toCollege($college->slug, 'images-videos') ?>"><?= Yii::t('app', 'View All') ?></a></h2>
            <div class="row">
                <?php foreach ($gallery as $image): ?>
                    <div class="picture">
                        <img class="lazyload" width="273" height="206" loading="lazy" data-src="<?= Url::getCollegeImage($college->slug, $image->file) ?>" src="<?= Url::getCollegeImage($college->slug, $image->file) ?>" alt="">
                    </div>
                <?php endforeach; ?>

                <?php if ($isMobile): ?>
                    <div class="picture mobileOnly">
                        <div class="viewAllDiv">
                            <a href="<?= Url::toCollege($college->slug, 'images-videos') ?>"><i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'View All') ?></a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- contact us -->

    <?php if (!empty($collegeExams)): ?>
        <?= $this->render('partials/_college-exam', [
            'title' => 'Exams Accepted by ' . (!empty($college->display_name) ? $college->display_name : $college->name),
            'collegeExams' => $collegeExams
        ]) ?>
    <?php endif; ?>

    <!-- colleges accepting exams -->
    <?php /*if (!empty($collegeAcceptingExams['college']) && count($collegeAcceptingExams['college'])): ?>
        <?= $this->render('/exam/partials/_college-accepting-exam', [
            'collegeAcceptingExams' => $collegeAcceptingExams,
            'exam' => $article->exam[0]
        ])
        ?>
    <?php endif; */ ?>

    <?php if (!empty($intrestedExams)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $article->exam[0]->slug,
            'title' => Yii::t('app', 'YOU MAY ALSO BE INTERESTED IN'),
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'pages' => $interestedInPages,
            'details' => $intrestedExams,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php endif; ?>
    <?php if (!empty($upcomingExamByStream)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $article->exam[0]->slug,
            'title' => Yii::t('app', 'Upcoming') . ' ' . $primaryStream->name . ' ' . Yii::t('app', 'Exams'),
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'pages' => $upcomingDisciplinePages,
            'details' => $upcomingExamByStream,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
            'viewAllUrl' => Url::toDisciplineExam($primaryStream->slug),
            'viewAllTitle' => $streams[0]['name'] . ' Entrance Exams in India'
        ])
        ?>
    <?php endif; ?>
    <?php if (!empty($popularExams)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => '',
            'title' => Yii::t('app', 'Popular Exams'),
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'details' => $popularExams,
            'pages' => $popularExamsPages,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php /*if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => Yii::t('app', 'Explore Nearby Colleges'),
            'colleges' => $nearByCollege,
            'viewAllUrl' => "{$college->city->slug}",
        ]) ?>
    <?php endif;*/ ?>

    <!-- related article -->
    <?php if (!empty($productArticle) && count($productArticle) > 1): ?>
        <?= $this->render('partials/_productArticleCard', [
            'relatedArticles' => $productArticle,
            'article' => $article
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($productNews) && empty($article->college) && empty($article->exam) && empty($article->course) && empty($article->board)): ?>
        <?= $this->render('partials/_productNewsCard', [
            'news' => $productNews,
        ]); ?>
    <?php endif; ?>

    <?php if (empty($article->college) && empty($article->exam) && empty($article->course) && empty($article->board)): ?>
        <?php if (!empty($article->news)): ?>
            <?= $this->render('partials/_productNewsCard', [
                'news' => $article->news,
            ]); ?>
        <?php endif; ?>

        <?php
        if (empty($article->college) && empty($article->exam) && empty($article->course) && empty($article->board)): ?>
            <?php if (!empty($article->article) || !empty($relatedArticles)): ?>
                <?= $this->render('partials/_productArticleCard', [
                    'relatedArticles' => empty($article->article) ? $relatedArticles : $article->article,
                    'article' => $article
                ]); ?>
            <?php endif; ?>
        <?php endif; ?>

    <?php endif; ?>

    <div id="comment-reply-form-js"></div>
</div>

<amp-lightbox id="my-bindable-lightbox-lead-0" layout="nodisplay">
    <div class="lightbox">
        <?= $this->render('/layouts/_lead_form', [
            'formId' => 'my-bindable-lightbox-lead-0',
            'id' => 2,
            'ctaLocation' => $ctaLocation0,
            'ctaText' => $ctaText0,
            'formTitle' => $formTitle0
        ]); ?>
    </div>
</amp-lightbox>

<amp-lightbox id="my-bindable-lightbox-lead-1" layout="nodisplay">
    <div class="lightbox">
        <?= $this->render('/layouts/_lead_form', [
            'formId' => 'my-bindable-lightbox-lead-1',
            'id' => 2,
            'ctaLocation' => $ctaLocation1,
            'ctaText' => $ctaText1,
            'formTitle' => $formTitle1
        ]); ?>
    </div>
</amp-lightbox>

<amp-lightbox id="my-bindable-lightbox-lead-2" layout="nodisplay">
    <div class="lightbox">
        <?= $this->render('/layouts/_lead_form', [
            'formId' => 'my-bindable-lightbox-lead-2',
            'id' => 2,
            'ctaLocation' => $ctaLocation2,
            'ctaText' => $ctaText2,
            'formTitle' => $formTitle2
        ]); ?>
    </div>
</amp-lightbox>

<amp-lightbox id="my-bindable-lightbox-lead-3" layout="nodisplay">
    <div class="lightbox">
        <?= $this->render('/layouts/_lead_form', [
            'formId' => 'my-bindable-lightbox-lead-3',
            'id' => 2,
            'ctaLocation' => $ctaLocation3,
            'ctaText' => $ctaText3,
            'formTitle' => $formTitle3
        ]); ?>
    </div>
</amp-lightbox>