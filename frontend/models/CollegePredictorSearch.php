<?php

namespace frontend\models;

use Yii;
use yii\base\Model;
use yii\data\ArrayDataProvider;
use common\models\Exam;
use common\services\CollegeService;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * CollegePredictorSearch represents the model behind the search form of college predictor.
 */
class CollegePredictorSearch extends Model
{
    public $rank;
    public $category;
    public $course;
    public $collegeType;
    public $state;
    public $examId;
    public $selectedFilters = [];
    public $perPage = 4;
    public $currentPage = 1;

    public $minRankValues;
    public $allCollegeTypes = [];
    public $allStates = [];
    public $allCourses = [];
    public $allPrograms = [];

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['course', 'collegeType', 'state', 'rank', 'category'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'rank' => 'Rank',
            'category' => 'Category',
            'course' => 'Course',
            'collegeType' => 'College Type',
            'state' => 'State',
        ];
    }

    /**
     * Returns the list of all attribute names of the model.
     * @return array list of attribute names.
     */
    public function attributes()
    {
        return [
            'rank',
            'category',
            'course',
            'collegeType',
            'state',
            'currentPage',
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @return ArrayDataProvider
     */
    public function search($params)
    {
        $getExamId = Exam::find()->select(['id'])->where(['slug' => $params['examSlug']])->one();

        $this->examId = $getExamId->id;

        $this->loadParams($params);
        $this->minRankValues = $this->getMinRankValues();
        $this->populateAllFilterOptions();

        // Query to get colleges based on rank range
        $query = new Query();

        $query->select([
            'c.id',
            'c.name',
            'c.slug',
            'c.display_name',
            'c.logo_image as logo',
            'c.cover_image',
            'c.type as college_type',
            'p.id as program_id',
            'p.name as program_name',
            'co.id as course_id',
            'co.name as course_name',
            'iprm.lowest_rank',
            'iprm.highest_rank',
            'iprm.round',
            's.id as state_id',
            's.name as state_name',
            'cc.name as category_name',
            'cc.id as category_id'
        ])
            ->from('institute_program_rank_mapping iprm')
            ->leftJoin('college c', 'c.id=iprm.college_id')
            ->leftJoin('program p', 'p.id=iprm.program_id')
            ->leftJoin('course co', 'co.id=iprm.course_id')
            ->leftJoin('state s', 's.id=iprm.state_id')
            ->leftJoin('cutoff_category cc', 'cc.id=iprm.cutoff_category_id')
            ->where(['iprm.status' => 1])
            ->andWhere(['iprm.exam_id' => $getExamId->id]);

        // Apply other filters
        $this->applyFilters($query);

        // Group by college and program
        $query->groupBy(['c.id', 'p.id']);

        // Execute query
        $results = $query->all();

        $colleges = $this->processResults($results);

        // Create data provider
        $dataProvider = new ArrayDataProvider([
            'allModels' => array_values($colleges),
            'pagination' => [
                'pageSize' => $this->perPage,
                'page' => $this->currentPage - 1,
            ],
        ]);

        return $dataProvider;
    }

    public function applyFilters(Query $query)
    {
        // If rank and category are provided, apply those filters
        if (!empty($this->rank) && !empty($this->category)) {
            $rank = (int) $this->rank;

            $maxCutoff = $rank + 100;
            $query->andWhere(['<=', 'iprm.highest_rank', $rank])
                ->andWhere(['>=', 'iprm.lowest_rank', $rank])
                ->andWhere(['<=', 'iprm.lowest_rank', $maxCutoff]);
        }

        // Apply program filter
        if (!empty($this->course)) {
            $query->andWhere(['iprm.program_id' => $this->course]);
            $this->selectedFilters['course'] = $this->course;
        }

        // Apply college type filter
        if (!empty($this->collegeType)) {
            $query->andWhere(['iprm.college_type' => $this->collegeType]);
            $query->andWhere(['not', ['iprm.college_type' => '']]);
            $this->selectedFilters['collegeType'] = $this->collegeType;
        }

        // Apply state filter
        if (!empty($this->state)) {
            $query->andWhere(['s.id' => $this->state]);
            $this->selectedFilters['state'] = $this->state;
        }
    }

    private function getMinRankValues()
    {
        return [
            'highest' => (new Query())->select(['MIN(iprm.highest_rank)'])->from('institute_program_rank_mapping iprm')->scalar(),
            'lowest' => (new Query())->select(['MIN(iprm.lowest_rank)'])->from('institute_program_rank_mapping iprm')->scalar(),
        ];
    }

    /**
     * Populate all available filter options (college types, states, courses, programs).
     * This method ensures these lists are always comprehensive, irrespective of active search filters.
     */
    protected function populateAllFilterOptions()
    {
        // Get all college types
        $collegeTypesQuery = (new Query())
            ->select(['iprm.college_type'])
            ->from('institute_program_rank_mapping iprm')
            ->where(['NOT', ['iprm.college_type' => '']])
            ->groupBy('iprm.college_type')
            ->orderBy(['iprm.college_type' => SORT_ASC])
            ->all();
        $this->allCollegeTypes = ArrayHelper::map($collegeTypesQuery, 'college_type', 'college_type');

        // Get all states
        $statesQuery = (new Query())
            ->select(['s.id', 's.name'])
            ->from('institute_program_rank_mapping iprm')
            ->leftJoin('state s', 'iprm.state_id = s.id')
            ->where(['not', ['s.id' => null]])
            ->where(['NOT', ['s.name' => '']])
            ->groupBy('s.id')
            ->orderBy(['s.name' => SORT_ASC])
            ->all();
        $this->allStates = ArrayHelper::map($statesQuery, 'id', 'name');

        // Get all courses
        $coursesQuery = (new Query())
            ->select(['co.id', 'co.name'])
            ->from('institute_program_rank_mapping iprm')
            ->innerJoin('course co', 'iprm.course_id = co.id')
            ->groupBy('co.id')
            ->orderBy(['co.name' => SORT_ASC])
            ->all();
        $this->allCourses = ArrayHelper::map($coursesQuery, 'id', 'name');

        // Get all programs
        $programsQuery = (new Query())
            ->select(['p.id', 'p.name'])
            ->from('institute_program_rank_mapping iprm')
            ->innerJoin('program p', 'iprm.program_id = p.id')
            ->groupBy('p.id')
            ->orderBy(['p.name' => SORT_ASC])
            ->all();
        $this->allPrograms = ArrayHelper::map($programsQuery, 'id', 'name');
    }

    /**
     * Get course options for dropdown. Now uses the pre-populated 'allCourses'.
     *
     * @return array
     */
    public function getCourseOptions()
    {
        return $this->allCourses;
    }

    /**
     * Get program options for dropdown. Now uses the pre-populated 'allPrograms'.
     *
     * @return array
     */
    public function getProgramOptions()
    {
        return $this->allPrograms;
    }

    /**
     * Get state options for dropdown. Now uses the pre-populated 'allStates'.
     *
     * @return array
     */
    public function getStateOptions()
    {
        return $this->allStates;
    }

    /**
     * Get college type options for dropdown. Now uses the pre-populated 'allCollegeTypes'.
     *
     * @return array
     */
    public function getCollegeTypeOptions()
    {
        return $this->allCollegeTypes;
    }

    public function processResults($results)
    {
        $colleges = [];

        foreach ($results as $result) {
            $collegeKey = $result['id'];
            $brochure = self::getBrochure($result['id']);

            if (!isset($colleges[$collegeKey])) {
                $colleges[$collegeKey] = [
                    'id' => $result['id'],
                    'name' => !empty($result['display_name']) ? $result['display_name'] : $result['name'],
                    'slug' => $result['slug'],
                    'logo' => $result['logo'],
                    'cover_image' => $result['cover_image'],
                    'brochure' => $brochure,
                    'state' => $result['state_name'],
                    'college_type' => $result['college_type'],
                    'programs' => [],
                    'highest_cutoff' => $result['lowest_rank'],
                    'highest_program' => $result['program_name'],
                    'highest_program_id' => $result['program_id'],
                    'lowest_cutoff' => $result['highest_rank'],
                    'lowest_program' => $result['program_name'],
                    'lowest_program_id' => $result['program_id']
                ];
            }

            // Add program to college
            $colleges[$collegeKey]['programs'][$result['program_id']] = [
                'id' => $result['program_id'],
                'name' => $result['program_name'],
                'course_id' => $result['course_id'],
                'course_name' => $result['course_name'],
                'lowest_rank' => $result['lowest_rank'],
                'highest_rank' => $result['highest_rank']
            ];

            $this->updateCutoffs($colleges, $collegeKey, $result);
        }

        return $colleges;
    }

    public function updateCutoffs(&$colleges, $collegeKey, $result)
    {
        // Update highest cutoff (lowest rank value) if this program has a lower rank
        if ($result['lowest_rank'] < $colleges[$collegeKey]['highest_cutoff']) {
            $colleges[$collegeKey]['highest_cutoff'] = $result['lowest_rank'];
            $colleges[$collegeKey]['highest_program'] = $result['program_name'];
            $colleges[$collegeKey]['highest_program_id'] = $result['program_id'];
        }

        // Update lowest cutoff (highest rank value) if this program has a higher rank
        if ($result['highest_rank'] > $colleges[$collegeKey]['lowest_cutoff']) {
            $colleges[$collegeKey]['lowest_cutoff'] = $result['highest_rank'];
            $colleges[$collegeKey]['lowest_program'] = $result['program_name'];
            $colleges[$collegeKey]['lowest_program_id'] = $result['program_id'];
        }
    }


    public function getCategory()
    {
        if (empty($this->category)) {
            return 'All Categories';
        }

        $query = new Query();
        $query->select(['cc.id', 'cc.name'])
            ->from('cutoff_category cc')
            ->where(['cc.id' => $this->category]);

        $category = $query->one();
        return $category ? $category['name'] : 'All Categories';
    }

    public function getBrochure($collegeId)
    {
        $brochure = (new CollegeService())->getCollegeBroucher($collegeId, 'college');

        return $brochure;
    }

    public function loadParams($params)
    {
        $attributes = $this->attributes();

        foreach ($params as $key => $value) {
            if ($key === 'page') {
                $this->currentPage = (int)$value;
                continue;
            }

            if (in_array($key, $attributes)) {
                $this->$key = $value;
            }
        }
    }
}
