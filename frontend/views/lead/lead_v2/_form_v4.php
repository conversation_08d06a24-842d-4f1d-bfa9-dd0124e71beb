<?php

// page specific assets
use common\helpers\DataHelper;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;

$currentUrl = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
$student = \Yii::$app->user->identity;
$userCity = DataHelper::getUserLocation();
$styleCityField = $userCity['cityId'] == '' ? 'block' : 'none';
$emailValue = !empty($student->email) ? $student->email : '';

if (isset($_SERVER['HTTP_REFERER'])) {
    $refererParts = parse_url($_SERVER['HTTP_REFERER']);
    if (isset($refererParts['query'])) {
        parse_str($refererParts['query'], $queryStringArray);
    }
}
?>

<div class="pageMask"></div>
<div class="signupModal" id="signupModalId">
    <div class="form__main__content">
        <span class="closeDialog spriteIconTwo"></span>
        <div class="headerAndForm">
            <div class="signupModalCommonHeader">
                <h3 class="textDivHeading">Join to Begin Your Journey</h3>
            </div>
            <form class="signupModalForm" name="signupModalForm" id="firstScreen">
                <section id="step-1" class="form-step">
                    <div class="modalFormStepOne inputGrid">
                        <div class="inputNameContainer modalInputContainer nameClass">
                            <label for="inputName"></label>
                            <input class="inputContainerField data-gtm formName" data-user-input="name" id="formName" type="text" name="name" placeholder="Name" value="<?= $student->name ?? '' ?>" autofocus="off" autocomplete="off" maxlength="50">
                            <span class="spriteIconTwo signupModalIcon nameIcon"></span>
                        </div>
                        <div class="inputEmailContainer modalInputContainer emailClass">
                            <label for="inputEmail"></label>
                            <input class="inputContainerField data-gtm" id="formEmail" type="text" data-user-input="email" name="email" placeholder="Email Address" value="<?= $emailValue ?? '' ?>" autocomplete="off" maxlength="50">
                            <span class="validEmailIcon spriteIconTwo">
                                <!-- <svg width="17" height="13" viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M5.7 12.025L0 6.325L1.425 4.9L5.7 9.175L14.875 0L16.3 1.425L5.7 12.025Z" fill="#00C8B5" />
                                </svg> -->
                            </span>
                            <span class="spriteIconTwo signupModalIcon emailIcon"></span>
                            <p class="error errorMsg errorMsgEmail"></p>
                        </div>
                        <div class="inputCityContainer modalInputContainer cityClass current_city" style="display: <?= $styleCityField ?>;">
                            <select class="inputContainerField select2HookClass city data-gtm data-gtm-change" data-user-input="city" name="current_city" id="leadform-interested_location">
                                <option></option>
                            </select>
                            <span class="spriteIcon signupModalIcon cityIcon"></span>
                        </div>
                        <div class="inputStreamContainer modalInputContainer streamClass streamCategory inputStream">
                            <select class="inputContainerField select2HookClass stream data-gtm data-gtm-change" data-user-input="stream" id="interested_stream_lead" name="inputStream">
                                <option></option>
                            </select>
                            <span class="spriteIconTwo signupModalIcon courseIcon"></span>
                        </div>
                        <div class="inputLevelContainer modalInputContainer levelClass levelCategory inputLevel">
                            <select class="inputContainerField select2HookClass level data-gtm data-gtm-change" data-user-input="level" id="interested_level_lead" name="inputLevel" disabled>
                                <option></option>
                            </select>
                            <span class="signupModalIcon spriteIconTwo level">
                                <!-- <svg width="21" height="14" viewBox="0 0 21 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1.48227 12.3522L0 10.927L7.41134 3.80069L11.3641 7.60138L18.3801 0L19.7636 1.33024L11.3641 10.4519L7.41134 6.65121L1.48227 12.3522Z" fill="#D8D8D8" />
                                    <path d="M2.47108 13.5873L1.48193 12.3521L7.41004 6.26976L11.3628 10.0704L19.7623 1.23511L20.9975 2.47033L11.3628 12.921L7.41004 9.12028L2.47108 13.5873Z" fill="#D8D8D8" />
                                </svg> -->
                            </span>
                        </div>
                        <div class="inputMobileContainer modalInputContainer">
                            <div class="countryCode otherCountryCode">
                                <div class="dialCodeDiv">
                                    <i class="spriteIcon flagIcon"></i>
                                    <span class="dialCode">+91</span>
                                </div>
                            </div>
                            <label for="inputMobileNumber"></label>
                            <input class="inputContainerField mobileNumberField data-gtm" id="formMobile" type="tel" data-user-input="phone" name="phone" placeholder="Mobile Number" maxlength="10" disabled value="<?= $student->phone ?? '' ?>" autocomplete="off" />
                        </div>
                        <!-- <div class="inputOTPContainer modalInputContainer">
                            <label for="inputOTP"></label>
                            <input class="inputContainerField OTPField" type="number" name="inputOTP" placeholder="Enter OTP">
                            <span class="otpTimer"></span>
                        </div> -->
                        <div class="inputOTPContainer modalInputContainer otpclass">
                            <label for="inputOTP"></label>
                            <input class="inputContainerField OTPField" id="OTPField" type="text" oninput="validateOtpInput(this)" name="inputOTP" placeholder="Enter OTP" />
                            <span class="otpTimer"></span>
                            <a class="sendOtpButton" style="display: none;">Send OTP</a>
                            <p class="error errorMsg"></p>
                        </div>
                    </div>
                    <div class="modalFormStepOne detailRow">
                        <div class="termsAndLogin">
                            <!-- <input type="checkbox" class="desktop__tnc__checkbox"> -->
                            <p class="termsAndConditionText">By proceeding forward, I agree to GetMyUni <a href="https://www.getmyuni.com/terms-and-conditions" title="Terms & Conditions" target="_blank">Terms & Conditions</a></p>
                        </div>
                        <button class="btn-navigate-form-step stepOne" id="firstScreenSubmit" type="button" step_number="2" disabled>Next</button>
                        <?php if (\Yii::$app->user->isGuest): ?>
                            <p id="lead-login-new" class="accountExistsText">Already Having An Account? <a class="loginClass">Login Now</a></p>
                        <?php endif; ?>
                    </div>
                </section>
                <input type="hidden" name="current_city_ip" id="current_city_ip" value="<?= $userCity['cityId'] ?>">
                <input type="hidden" name="current_state_ip" id="current_state_ip" value="<?= $userCity['stateId'] ?>">
                <input type="hidden" id="leadform-url" name="url" value="<?= empty($currentUrl) ? '' : $currentUrl ?>">
                <input type="hidden" name="source" value="<?= empty($_GET['source']) ? '' : DataHelper::$leadSource['organic'] ?>">
                <input type="hidden" id="leadform-utm_source" name="utm_source" value="<?= empty($queryStringArray) || empty($queryStringArray['utm_source']) ? '' : $queryStringArray['utm_source'] ?>">
                <input type="hidden" id="leadform-utm_medium" name="utm_medium" value="<?= empty($queryStringArray) || empty($queryStringArray['utm_medium']) ? '' : $queryStringArray['utm_medium'] ?>">
                <input type="hidden" id="leadform-utm_campaign" name="utm_campaign" value="<?= empty($queryStringArray) || empty($queryStringArray['utm_campaign']) ? '' : $queryStringArray['utm_campaign'] ?>">
                <input type="hidden" id="leadform-entity" name="entity">
                <input type="hidden" id="leadform-entity_id" name="entity_id">
                <input type="hidden" name="platform" value="<?= \Yii::$app->devicedetect->isMobile() ? 'wap' : 'web' ?>">
                <input type="hidden" name="cta_location" id="cta_location" data-cta-location="">
                <input type="hidden" name="cta_text" id="cta_text">
                <input type="hidden" name="cta_title" id="cta_title">
                <input type="hidden" name="alternate-media" id="alternate-media">
                <input type="hidden" name="alternate-pageSlug" id="alternate-pageSlug">
                <input type="hidden" id="durl">
                <input type="hidden" id="rank_predictor_lead_value" value="">
                <input type="hidden" id="dynamic_redirection">
                <input type="hidden" id="entity_subtype" name="entity_subtype">
                <input type="hidden" id="entity_type" name="entity_type">
                <input type="hidden" id="leadform-state_id" name="state_id">
                <input type="hidden" id="leadform-state_name" name="state_name">
                <input type="hidden" id="activity_id" name="activity_id">
                <input type="hidden" id="course_id" name="course_id">
                <input type="hidden" id="degree_id" name="degree_id">
                <input type="hidden" id="program_id" name="program_id">
                <input type="hidden" id="page_name" name="page_name">
                <input type="hidden" id="college_filter_exam" name="college_filter_exam">
                <input type="hidden" id="hiddenNumber" name="hiddenNumber" value="<?= $student->phone ?? '' ?>">
                <input type="hidden" id="ctaIdentity" name="ctaIdentity" value="">
                <input type="hidden" id="ctaIndex" name="ctaIndex" value="">
                <input type="hidden" id="ctaTabIndex" name="ctaTabIndex" value="">
                <input type="hidden" id="ctaSubTopic" name="ctaSubTopic" value="">
                <input type="hidden" id="indexcorrectanswer" name="indexcorrectanswer" value="">
                
            </form>
            <form class="signupModalForm" name="signupModalFormTwo" id="secondScreen">
                <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
                <section id="step-2" class="form-step inactiveStep">
                    <div class="modalFormStepTwo inputGrid">
                        <div class="specializationText modalInputLabelContainer">
                            <label class="modalInputLabel">Specialization <span class="requiredFieldContainer">*</span></label>
                        </div>
                        <div class="specializationContainer modalInputContainer">
                            <select class="inputContainerField select2HookClass specialization data-gtm data-gtm-change" data-user-input="specialization" id="interested_specialization_lead" name="inputSpecialization[]" multiple="multiple">
                            </select>
                            <div class="whiteScrollBg">
                                <span class="signupModalIcon spriteIconTwo heartIcon">
                                    <!-- <svg width="17" height="15" viewBox="0 0 17 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8.5 15L7.2675 13.9373C5.83667 12.6975 4.65375 11.6281 3.71875 10.7289C2.78375 9.8297 2.04 9.02248 1.4875 8.30722C0.935 7.59196 0.548958 6.9346 0.329375 6.33515C0.109792 5.73569 0 5.12262 0 4.49591C0 3.21526 
                                    0.44625 2.14578 1.33875 1.28747C2.23125 0.429155 3.34333 0 4.675 0C5.41167 0 6.11292 0.149864 6.77875 0.449591C7.44458 0.749319 8.01833 1.17166 8.5 1.71662C8.98167 1.17166 9.55542 0.749319 10.2213 0.449591C10.8871 
                                    0.149864 11.5883 0 12.325 0C13.6567 0 14.7688 0.429155 15.6613 1.28747C16.5538 2.14578 17 3.21526 17 4.49591C17 5.12262 16.8902 5.73569 16.6706 6.33515C16.451 6.9346 16.065 7.59196 15.5125 8.30722C14.96 9.02248 14.2163 
                                    9.8297 13.2812 10.7289C12.3463 11.6281 11.1633 12.6975 9.7325 13.9373L8.5 15Z" fill="#D8D8D8" />
                                </svg> -->
                                </span>
                            </div>
                        </div>
                        <div class="locationText modalInputLabelContainer">
                            <label class="modalInputLabel">College Destination</label>
                        </div>
                        <div class="destinationContainer modalInputContainer">
                            <select class="inputContainerField select2HookClass college_location data-gtm data-gtm-change" data-user-input="college-location" name="college_location[]" id="interested_location" multiple="multiple">
                            </select>
                            <div class="whiteScrollBg">
                                <span class="signupModalIcon spriteIconTwo collegeLocationIcon">
                                    <!-- <svg width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7 11.7C7.81667 11.7 8.55312 11.4938 9.20937 11.0813C9.86562 10.6688 10.3833 10.125 10.7625 9.45C10.2521 9.015 9.67604 8.68125 9.03438 8.44875C8.39271 8.21625 7.71458 
                                    8.1 7 8.1C6.28542 8.1 5.60729 8.21625 4.96562 8.44875C4.32396 8.68125 3.74792 9.015 3.2375 9.45C3.61667 10.125 4.13438 10.6688 4.79063 11.0813C5.44688 11.4938 6.18333 
                                    11.7 7 11.7ZM7 7.2C7.48125 7.2 7.89323 7.02375 8.23594 6.67125C8.57865 6.31875 8.75 5.895 8.75 5.4C8.75 4.905 8.57865 4.48125 8.23594 4.12875C7.89323 3.77625 
                                    7.48125 3.6 7 3.6C6.51875 3.6 6.10677 3.77625 5.76406 4.12875C5.42135 4.48125 5.25 4.905 5.25 5.4C5.25 5.895 5.42135 6.31875 5.76406 6.67125C6.10677 7.02375 
                                    6.51875 7.2 7 7.2ZM7 18C4.65208 15.945 2.89844 14.0363 1.73906 12.2738C0.579687 10.5113 0 8.88 0 7.38C0 5.13 0.703646 3.3375 2.11094 2.0025C3.51823 0.6675 
                                    5.14792 0 7 0C8.85208 0 10.4818 0.6675 11.8891 2.0025C13.2964 3.3375 14 5.13 14 7.38C14 8.88 13.4203 10.5113 12.2609 12.2738C11.1016 14.0363 9.34792 15.945 7 18Z" fill="#D8D8D8" />
                                </svg> -->
                                </span>
                            </div>
                        </div>
                        <div class="budgetText modalInputLabelContainer">
                            <label class="modalInputLabel">Budget<span style="font-size: 12px;">(per
                                    year)</span></label>
                        </div>
                        <div id="ajaxLeadBudget"></div>
                        <div class="admissionText modalInputLabelContainer">
                            <label class="modalInputLabel">Are you looking for admission in 2025?<span class="requiredFieldContainer">*</span></label>
                        </div>
                        <div class="admissionRadioContainer modalInputContainer">
                            <div class="admissionRadioRow">
                                <div class="admissionItem">
                                    <input class="admissionRadio  data-gtm data-gtm-change-radio yes-impression" data-user-input="admission-year" type="radio" id="labelIDYes" name="admissionYearSelection" value=<?= 1 ?>>
                                    <label class="admissionLabel" for="labelIDYes">Yes</label>
                                </div>
                                <div class="admissionItem">
                                    <input class="admissionRadio   data-gtm-change-radio no-impression" type="radio" data-user-input="admission-year" id="labelIDNo" name="admissionYearSelection" value=<?= 0 ?>>
                                    <label class="admissionLabel" for="labelIDNo">No</label>
                                </div>
                            </div>
                        </div>
                        <div class="admissionText modalInputLabelContainer">
                            <label class="modalInputLabel">Are you Looking for</label>
                        </div>
                        <div class="distanceCourseContainer modalInputContainer">
                            <input type="checkbox" name="distanceEducation" class="data-gtm data-gtm-change" data-user-input="distance-education">
                            <p class="distanceCourseText">Distance Course</p>
                            <input type="checkbox" name="studyAbroadCheck" class="data-gtm data-gtm-change" data-user-input="study-abroad">
                            <p class="distanceCourseText">Study Abroad</p>
                        </div>
                    </div>
                    <div class="modalFormStepTwo detailRow">
                        <button class="btn-navigate-form-step step-2" id="secondScreenSubmit" type="button" step_number="3" disabled>Next</button>
                    </div>
                </section>
            </form>
            <form class="signupModalForm" name="signupModalFormThree" id="thirdScreen">
                <section id="step-3" class="form-step inactiveStep">
                    <div class="sponsorCollegeLead"></div>
                    <div class="modalFormStepThree detailRow">
                        <button class="btn-navigate-form-step submit-btn step-3" id="thirdScreenSubmit" type="submit">Submit</button>
                    </div>
                </section>
            </form>
        </div>
        <div class="finalScreen">
            <img src="/yas/images/lead-form-thankyou.png" loading="lazy">
            <img src="/yas/images/lead-form-final-logo.png" class="lead__form__logo" loading="lazy">
            <p class="messageOneLeadFormSubmit">Thanks for signing up!</p>
            <p class="messageTwoLeadFormSubmit">Our team will contact you shortly</p>
        </div>
    </div>
    <div class="form__side__content">
        <div class="form__img desktopOnly">
            <!-- <img class="lead-form-side-img" src="/yas/images/lead-form-side-img-1.png" alt="form side image"> -->
            <picture class="lead-form-side-img">
                <source srcset="/yas/images/lead-form-side-img-1.png" media="(min-width: 1023px)">
                <img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" alt="form side image" loading="lazy" width='227px' height='197px'>
            </picture>
        </div>
        <div class="form__text">
            <ul>
                <li class="headingOne"> Invest in Your Future: Get Help with Your Registration Today</li>
                <li class="subHeadingOne">
                    <span class="spriteIconTwo headingIconTick">
                        <!-- <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.88 11.68L12.52 6.04L11.4 4.92L6.88 9.44L4.6 7.16L3.48 8.28L6.88 11.68ZM8 16C6.89333 16 5.85333 15.79 4.88 15.37C3.90667 
                            14.95 3.06 14.38 2.34 13.66C1.62 12.94 1.05 12.0933 0.63 11.12C0.21 10.1467 0 9.10667 0 8C0 6.89333 0.21 5.85333 0.63 4.88C1.05 3.90667 
                            1.62 3.06 2.34 2.34C3.06 1.62 3.90667 1.05 4.88 0.63C5.85333 0.21 6.89333 0 8 0C9.10667 0 10.1467 0.21 11.12 0.63C12.0933 1.05 12.94 1.62 
                            13.66 2.34C14.38 3.06 14.95 3.90667 15.37 4.88C15.79 5.85333 16 6.89333 16 8C16 9.10667 15.79 10.1467 15.37 11.12C14.95 12.0933 14.38 12.94 
                            13.66 13.66C12.94 14.38 12.0933 14.95 11.12 15.37C10.1467 15.79 9.10667 16 8 16ZM8 14.4C9.78667 14.4 11.3 13.78 12.54 12.54C13.78 11.3 14.4 9.78667 
                            14.4 8C14.4 6.21333 13.78 4.7 12.54 3.46C11.3 2.22 9.78667 1.6 8 1.6C6.21333 1.6 4.7 2.22 3.46 3.46C2.22 4.7 1.6 6.21333 1.6 8C1.6 9.78667 2.22 11.3 
                            3.46 12.54C4.7 13.78 6.21333 14.4 8 14.4Z" fill="white" />
                        </svg> -->

                    </span>
                    <span>
                        Get Expert Help to Apply to Your Dream College!
                    </span>
                </li>
                <li class="subHeadingTwo">
                    <span class="spriteIconTwo headingIconTick">
                        <!-- <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.88 11.68L12.52 6.04L11.4 4.92L6.88 9.44L4.6 7.16L3.48 8.28L6.88 11.68ZM8 16C6.89333 16 5.85333 15.79 4.88 15.37C3.90667 
                            14.95 3.06 14.38 2.34 13.66C1.62 12.94 1.05 12.0933 0.63 11.12C0.21 10.1467 0 9.10667 0 8C0 6.89333 0.21 5.85333 0.63 4.88C1.05 3.90667 
                            1.62 3.06 2.34 2.34C3.06 1.62 3.90667 1.05 4.88 0.63C5.85333 0.21 6.89333 0 8 0C9.10667 0 10.1467 0.21 11.12 0.63C12.0933 1.05 12.94 1.62 
                            13.66 2.34C14.38 3.06 14.95 3.90667 15.37 4.88C15.79 5.85333 16 6.89333 16 8C16 9.10667 15.79 10.1467 15.37 11.12C14.95 12.0933 14.38 12.94 
                            13.66 13.66C12.94 14.38 12.0933 14.95 11.12 15.37C10.1467 15.79 9.10667 16 8 16ZM8 14.4C9.78667 14.4 11.3 13.78 12.54 12.54C13.78 11.3 14.4 9.78667 
                            14.4 8C14.4 6.21333 13.78 4.7 12.54 3.46C11.3 2.22 9.78667 1.6 8 1.6C6.21333 1.6 4.7 2.22 3.46 3.46C2.22 4.7 1.6 6.21333 1.6 8C1.6 9.78667 2.22 
                            11.3 3.46 12.54C4.7 13.78 6.21333 14.4 8 14.4Z" fill="white" />
                        </svg> -->

                    </span>
                    <span>
                        Stay up-to date with Exam Notification and News
                    </span>
                </li>
                <li class="subHeadingThree">
                    <span class="spriteIconTwo headingIconTick">
                        <!-- <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.88 11.68L12.52 6.04L11.4 4.92L6.88 9.44L4.6 7.16L3.48 8.28L6.88 11.68ZM8 16C6.89333 16 5.85333 15.79 4.88 15.37C3.90667 
                            14.95 3.06 14.38 2.34 13.66C1.62 12.94 1.05 12.0933 0.63 11.12C0.21 10.1467 0 9.10667 0 8C0 6.89333 0.21 5.85333 0.63 4.88C1.05 3.90667 
                            1.62 3.06 2.34 2.34C3.06 1.62 3.90667 1.05 4.88 0.63C5.85333 0.21 6.89333 0 8 0C9.10667 0 10.1467 0.21 11.12 0.63C12.0933 1.05 12.94 1.62 
                            13.66 2.34C14.38 3.06 14.95 3.90667 15.37 4.88C15.79 5.85333 16 6.89333 16 8C16 9.10667 15.79 10.1467 15.37 11.12C14.95 12.0933 14.38 12.94 
                            13.66 13.66C12.94 14.38 12.0933 14.95 11.12 15.37C10.1467 15.79 9.10667 16 8 16ZM8 14.4C9.78667 14.4 11.3 13.78 12.54 12.54C13.78 11.3 14.4 9.78667 
                            14.4 8C14.4 6.21333 13.78 4.7 12.54 3.46C11.3 2.22 9.78667 1.6 8 1.6C6.21333 1.6 4.7 2.22 3.46 3.46C2.22 4.7 1.6 6.21333 1.6 8C1.6 9.78667 2.22 11.3 
                            3.46 12.54C4.7 13.78 6.21333 14.4 8 14.4Z" fill="white" />
                        </svg> -->
                    </span>
                    <span>
                        Ace the Exam with Our Expertly Crafted Sample Papers!
                    </span>
                </li>
            </ul>
        </div>
    </div>
</div>