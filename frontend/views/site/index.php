<?php

use common\helpers\DataHelper;
use frontend\assets\AppAsset;
use frontend\helpers\Url;

/* @var $this yii\web\View */

$this->title = 'GetMyUni - Explore Top Colleges, Courses, Fees and Exams';
$this->context->description = 'Get Detailed Information on Top Colleges, Courses & Exams in India.Get Alerts on Results,Cutoff,Admission, Placements, Rankings and more.';
$this->context->ogType = 'website';
$this->context->ogImage = '/yas/images/home_new/homepage_background_3.jpg';
$this->registerCssFile(Yii::$app->params['cssPath'] . 'home_new.css', ['depends' => [AppAsset::class]]);

$this->registerCssFile('https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile('https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile('https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/jqvmap.min.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile('https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css', ['depends' => [AppAsset::class]]);

$this->params['entity'] = 'home';
$this->params['entity_name'] = 'home_page';
$this->params['entity_id'] = 0;

?>
<script type='application/ld+json'>
  {
    "@context": "http://schema.org",
    "@type": "Organization",
    "name": "Getmyuni",
    "url": "https://www.getmyuni.com",
    "logo": "https://media.getmyuni.com/azure/assets/images/logo_squre.png",
    "legalName": "GETMYUNI Education Services Private Limited",
    "telephone": "+91-888-40-32-828",
    "email": "<EMAIL>",
    "sameAs": [
      "https://www.facebook.com/getmyuniedu",
      "https://twitter.com/getmyuniedu",
      "https://www.youtube.com/channel/UCvczFiMv9OZwYkFydoNdMCA",
      "https://www.linkedin.com/company/getmyuni",
      "https://www.instagram.com/getmyuni/"
    ]

  }
</script>
<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Getmyuni",
    "url": "https://www.getmyuni.com"
  }
</script>

<div class="indexPage">
  <section class="indexSection p-0">
    <div class="carouselSection">
      <?php if (!empty($homeSlides)) {
            foreach ($homeSlides as $homeSlide) {
                $url = DataHelper::parseDomainUrl($homeSlide['redirect_link']);
                ?>
          <div class="carouselDiv">
                  <img src="https://media.getmyuni.com/assets/images/homepage/homepage_slides/<?= $homeSlide['image']; ?>" 
                    srcset="https://media.getmyuni.com/assets/images/homepage/homepage_slides/<?= $homeSlide['image']; ?>?w=600 600w,
                      https://media.getmyuni.com/assets/images/homepage/homepage_slides/<?= $homeSlide['image']; ?>?w=1200 1200w,
                      https://media.getmyuni.com/assets/images/homepage/homepage_slides/<?= $homeSlide['image']; ?>?w=1600 1600w"
                    sizes="(max-width: 768px) 100vw, 1200px" height="444" width="1200" alt="<?= htmlspecialchars($homeSlide['display_name'], ENT_QUOTES); ?>" 
                    decoding="async" fetchpriority="high"/>
                  <a href="<?= $url ?>" class="sliderName" aria-label="Read more about <?= htmlspecialchars($homeSlide['display_name'], ENT_QUOTES); ?>" 
                    <?= (strpos($homeSlide['redirect_link'], 'getmyuni.com') === false) ? 'rel="nofollow"' : '' ?>>
                    <?= $homeSlide['display_name']; ?>
                  </a>
                </div>
            <?php }
      } ?>
    </div>
    <div class="bannerContent">
      <h1>
        GetMyUni - Where Educational Choices Are Made Easy
      </h1>
      <div class="searchSection">
        <div>
          <ul class="bannerTabButtons">
            <li class="tab-nav-link tabLink" data-target="#college-tab">
              Colleges
            </li>
            <li class="tab-nav-link" data-target="#exams-tab">Exams</li>
            <li class="tab-nav-link" data-target="#course-tab">Courses</li>
          </ul>
        </div>
        <div class="inputSection">
          <div id="college-tab" class="tab-content activeTab">
            <div class="row m-0">
              <input type="text" placeholder="Enter College Name" spellcheck="false" class="college-name-text-box foucus-search" autocomplete="off" />
              <button class="primaryBtn searchIcon-home">
                <i class="spriteIcon searchIcon"></i><span class="desktopOnly">Search</span>
              </button>
            </div>
          </div>
          <div id="exams-tab" class="tab-content">
            <div class="row m-0">
              <input type="text" placeholder="Enter Exam Name eg: JEE,CAT,XAT" class="exam-name-text-box foucus-search" />
              <button class="primaryBtn searchIcon-home">
                <i class="spriteIcon searchIcon"></i><span class="desktopOnly"> Search</span>
              </button>
            </div>
          </div>
          <div id="course-tab" class="tab-content">
            <div class="row m-0">
              <input type="text" placeholder="Enter Course Name" class="course-name-text-box foucus-search" />
              <button class="primaryBtn searchIcon-home">
                <i class="spriteIcon searchIcon"></i><span class="desktopOnly">Search</span>
              </button>
            </div>
          </div>
          <div class="trending"></div>
        </div>
      </div>
    </div>
  </section>
  <section class="indexSection pb-0">
    <div class="container">
      <h2>Trending Now</h2>
      <div class="trendingTopicsList">
        <!-- <i class="spriteIcon scrollLeft over"></i>
        <i class="spriteIcon scrollRight"></i> -->
        <ul class="marquee">
          <?php if (!empty($trendingToday)) {
                foreach ($trendingToday as $trending) {
                    $url = DataHelper::parseDomainUrl($trending['url']);
                    echo '<li><a href="' . $url . '">' . $trending['display_name'] . '</a></li>';
                }
          }
            ?>
        </ul>
      </div>
    </div>
  </section>
  <section class="indexSection">
    <div class="container">
      <h2>Featured Colleges</h2>
      <div class="customSlider four-cardDisplay">
        <i class="spriteIcon scrollLeft featuredScrollLeft"></i>
        <i class="spriteIcon scrollRight featuredScrollRight"></i>
        <div class="customSliderCards homeFeaturedCollege">
          <?php if (!empty($sponsoredColleges)) { ?>
                <?php foreach ($sponsoredColleges as $college) { ?>
              <a class="displayCard" href="<?= Url::toDomain() ?>college/<?= $college['slug']; ?>">
                <figure>
                  <img loading="lazy" src="https://media.getmyuni.com/azure/college-image/big/<?= $college['cover_image'] ?? $college['image']; ?>" alt="img" width="275" height="206" />
                </figure>
                <div class="textDiv pt-0">
                  <img loading="lazy" src="https://media.getmyuni.com/azure/college-image/small/<?= $college['slug']; ?>.jpg" class="collegeLogo" alt="img" width="56" height="56" />
                  <p class="widgetCardHeading">
                    <?= $college['display_name']; ?>
                  </p>
                  <p class="subText">
                    <span class="spriteIcon locationIcon"></span> <?= $college['cityName']; ?>, <?= $college['stateName']; ?>
                  </p>
                </div>
              </a>
                <?php } ?>
          <?php } ?>
        </div>
      </div>
    </div>
  </section>
  <section class="indexSection pt-0">
    <div class="container">
      <h2>
        Select Your Dream College In Your Desired City
      </h2>
      <div class="customSlider four-cardDisplay">
        <i class="spriteIcon scrollLeft over"></i>
        <i class="spriteIcon scrollRight"></i>
        <div class="customSliderList">
          <?php if (!empty($trendingCollegeCities)) { ?>
                <?php foreach ($trendingCollegeCities as $trendingcity) { ?>
              <a href="<?= Url::toDomain() ?>all-colleges/<?= $trendingcity['slug']; ?>" class="sliderCard">
                <img loading="lazy" src="https://media.getmyuni.com/assets/images/city-logos/<?= $trendingcity['slug']; ?>.webp" width="276" height="207" alt="img" />
                <p class="cityName"><?= $trendingcity['name']; ?></p>
              </a>
                <?php } ?>
          <?php } ?>
        </div>
      </div>
    </div>
  </section>
  <section class="indexSection bg-lightgray">
    <div class="container">
      <h2>
        Explore Colleges, Courses & Exams That Are Curated For You
      </h2>
      <div class="collegesWithCategory">
        <ul>
          <li class="tab-nav-link tabLink" data-target="#colleges-category">
            Colleges
          </li>
          <li class="tab-nav-link" data-target="#exams-category">Exams</li>
          <li class="tab-nav-link" data-target="#courses-category">
            Courses
          </li>
        </ul>
      </div>
      <div class="collegesWithCategoryData">
        <div id="colleges-category" class="tab-content activeTab">
          <div class="row limitCards">
            <?php if (!empty($collegeCountbyStream)) { ?>
                <?php foreach ($collegeCountbyStream as $stream) { ?>
                <a href="<?= Url::toDomain() ?><?= $stream['stream_slug']; ?>-colleges" class="dataCard">
                  <span title="<?= $stream['stream_name']; ?> Exam" class="indexSprite <?= $stream['stream_slug']; ?>"></span>
                  <div class="dataCardText">
                    <p><?= $stream['stream_name']; ?></p>
                    <p class="count"><?= $stream['college_count']; ?> Colleges</p>
                  </div>
                </a>
                <?php } ?>
            <?php } ?>

          </div>
          <?php if (count($collegeCountbyStream) > 18) { ?>
            <div class="col-12 text-center">
              <button class="primaryBtn viewMoreCards">View More</button>
            </div>
          <?php } ?>
        </div>

        <div id="exams-category" class="tab-content">
          <div class="row limitCards">
            <?php if (!empty($examCountbyStream)) { ?>
                <?php foreach ($examCountbyStream as $stream) { ?>
                <a href="<?= Url::toDomain() ?>exams/<?= $stream['stream_slug']; ?>-exams-in-india" class="dataCard">
                  <span title="<?= $stream['stream_name']; ?> Exam" class="indexSprite <?= $stream['stream_slug']; ?>"></span>
                  <div class="dataCardText">
                    <p><?= $stream['stream_name']; ?></p>
                    <p class="count"><?= $stream['exam_count']; ?> Exams</p>
                  </div>
                </a>
                <?php } ?>
            <?php } ?>
          </div>
          <?php if (count($examCountbyStream) > 18) { ?>
            <div class="col-12 text-center">
              <button class="primaryBtn viewMoreCards">View More</button>
            </div>
          <?php } ?>
        </div>

        <div id="courses-category" class="tab-content">
          <div class="row limitCards">
            <?php if (!empty($courseCountbyStream)) { ?>
                <?php foreach ($courseCountbyStream as $stream) { ?>
                <a href="<?= Url::toDomain() ?>courses/<?= $stream['stream_slug']; ?>" class="dataCard">
                  <span title="<?= $stream['stream_name']; ?> Exam" class="indexSprite <?= $stream['stream_slug']; ?>"></span>
                  <div class="dataCardText">
                    <p><?= $stream['stream_name']; ?></p>
                    <p class="count"><?= $stream['course_count']; ?> Courses</p>
                  </div>
                </a>
                <?php } ?>
            <?php } ?>
          </div>
          <?php if (count($courseCountbyStream) > 18) { ?>
            <div class="col-12 text-center">
              <button class="primaryBtn viewMoreCards">View More</button>
            </div>
          <?php } ?>
        </div>
      </div>
    </div>
  </section>
  <section class="indexSection">
    <div class="container">
      <h2>Study Abroad Options</h2>
      <p class="sectionSubheading">
        Choose from the top study destinations that the world has to offer. Be informed about universities, rankings, admission details, and exams.
      </p>
      <div class="row">
        <div class="col-md-6">
          <div id="worldmap" style="width: auto; height: 400px;"> </div>

        </div>
        <div class="col-md-6">
          <div class="studyAbroad row m-0">
            <a class="studyAbroadCard" href="<?= Url::toDomain() ?>canada">
              <img src="<?= 'https://media.getmyuni.com/assets/img/home_new/canada.webp' ?>" alt="CANADA" title="CANADA" width="75" height="75" loading="lazy" />
              <p>CANADA</p>
            </a>
            <a class="studyAbroadCard" href="<?= Url::toDomain() ?>uk">
              <img src="<?= 'https://media.getmyuni.com/assets/img/home_new/uk.webp' ?>" alt="UK" title="UK" width="75" height="75" loading="lazy" />
              <p>UK</p>
            </a>
            <a class="studyAbroadCard" href="<?= Url::toDomain() ?>usa">
              <img src="<?= 'https://media.getmyuni.com/assets/img/home_new/usa.webp' ?>" alt="USA" title="USA" width="75" height="75" loading="lazy" />
              <p>USA</p>
            </a>
            <a class="studyAbroadCard" href="<?= Url::toDomain() ?>australia">
              <img src="<?= 'https://media.getmyuni.com/assets/img/home_new/australia.webp' ?>" alt="AUSTRALIA" title="AUSTRALIA" width="75" height="75" loading="lazy" />
              <p>AUSTRALIA</p>
            </a>
            <a class="studyAbroadCard" href="<?= Url::toDomain() ?>germany">
              <img src="<?= 'https://media.getmyuni.com/assets/img/home_new/germany.webp' ?>" alt="GERMANY" title="GERMANY" width="75" height="75" loading="lazy" />
              <p>GERMANY</p>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="indexSection articleIndexBox">
    <div class="container">
      <div class="articleRelataedLinks">
        <p class="btn_left over">
          <i class="spriteIcon left_angle"></i>
        </p>
        <p class="btn_right">
          <i class="spriteIcon right_angle"></i>
        </p>
        <ul>
          <?php foreach ($homePageArticleCategory as $key => $category): ?>
            <li class="homePageArticle">
              <a class=<?= ($key == 0) ? 'activeLink' : ''; ?> href="javascript:;" data-tab=<?= $category['slug']; ?> title=<?= $category['name']; ?>><?= $category['name']; ?></a>
            </li>
          <?php endforeach; ?>
        </ul>
      </div>
      <div class="articleRelatedCtn">
        <div class="quickLinks">
          <h2>Recent Article</h2>
          <div class="loader" style="display:none;">
            <ul id="recentArticleHome1">
              <?php foreach ([1, 2, 3, 4, 5] as $data): ?>
                <li>
                  <a>
                    <div class="animate"></div>
                    <div class="animate"></div>
                  </a>
                </li>
              <?php endforeach ?>
            </ul>
          </div>
          <ul id="recentArticleHome"></ul>
        </div>
        <div class="verticalLine"></div>
        <div class="quickLinks">
          <h2>Popular Article</h2>
          <div class="loader" style="display:none;">
            <ul id="popularArticleHome1">
              <?php foreach ([1, 2, 3, 4, 5] as $data): ?>
                <li>
                  <a>
                    <div class="animate"></div>
                    <div class="animate"></div>
                  </a>
                </li>
              <?php endforeach ?>
            </ul>
          </div>
          <ul id="popularArticleHome"></ul>
        </div>
      </div>
    </div>
  </section>
  <?php
    $sections = [
    [
      'sectionClass' => 'bg-lightgray',
      'sectionTitle' => 'Trending Exams',
      'items' => $trendingExams,
      'entityType' => 'exam',
      'subpageItems' => $trendingSubpageExams,
    ],
    [
      'sectionClass' => '',
      'sectionTitle' => 'Trending Courses',
      'items' => $trendingCourses,
      'entityType' => 'course',
      'subpageItems' => $trendingSubpageCourses,
    ],
    [
      'sectionClass' => 'bg-lightgray',
      'sectionTitle' => 'Top School Exams in India',
      'items' => $trendingBoards,
      'entityType' => 'board',
      'subpageItems' => $trendingSubpageBoards,
    ],
    ];

    foreach ($sections as $section): ?>
    <section class="indexSection <?= $section['sectionClass'] ?>">
      <div class="container">
        <h2><?= $section['sectionTitle'] ?></h2>
        <div class="trendingBtnSection">
            <?= $this->render('partials/_trendingSection', [
            'items' => $section['items'],
            'entityType' => $section['entityType']
          ]); ?>
        </div>
      </div>
      <div class="container mt-5">
          <?= $this->render('partials/_trendingCards', [
            'items' => $section['subpageItems'],
            'entityType' => $section['entityType']
        ]); ?>
      </div>
    </section>
    <?php endforeach; ?>

  </section>
  <?php if (!empty($popularColleges)) { ?>
    <section class="indexSection">
      <div class="container">
        <h2>Popular Colleges</h2>
        <div class="customSlider four-cardDisplay">
          <i class="spriteIcon scrollLeft over"></i>
          <i class="spriteIcon scrollRight"></i>
          <div class="customSliderCards">
            <?php foreach ($popularColleges as $college) { ?>
              <a class="displayCard" href="<?= Url::toDomain() ?>college/<?= $college['slug']; ?>">
                <figure>
                  <img loading="lazy" src="https://media.getmyuni.com/azure/college-image/big/<?= $college['cover_image'] ?? $college['image']; ?>" alt="img" width="275" height="206" />
                </figure>
                <div class="textDiv pt-0">
                  <img loading="lazy" src="https://media.getmyuni.com/azure/college-image/small/<?= $college['slug']; ?>.jpg" class="collegeLogo" alt="img" width="56" height="56" />
                  <p class="widgetCardHeading">
                    <?= $college['display_name']; ?>
                  </p>
                  <p class="subText">
                    <span class="spriteIcon locationIcon"></span> <?= $college['cityName']; ?>, <?= $college['stateName']; ?>
                  </p>
                </div>
              </a>
            <?php } ?>
          </div>
        </div>
      </div>
    </section>
  <?php } ?>
  <section class="indexSection bg-lightgray">
    <div class="container">
      <h2>Explore Scholarships</h2>
      <p class="sectionSubheading">
        Search among 500+ government, and competitive entrance exams
      </p>
      <div class="exploreScholorshipDiv row">
        <a title="State Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/state" class="scholorshipCard">
          <i class="spriteIcon stateScholarships"></i>
          <p>State Wise Scholarships</p>
        </a>
        <a title="Class Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/study" class="scholorshipCard">
          <i class="spriteIcon classScholarships"></i>
          <p>Class Wise Scholarships</p>
        </a>
        <a title="Discipline Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/discipline" class="scholorshipCard">
          <i class="spriteIcon disciplineScholarships"></i>
          <p>Discipline Wise Scholarships</p>
        </a>
        <a title="Course Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/course" class="scholorshipCard">
          <i class="spriteIcon courseScholarships"></i>
          <p>Course Wise Scholarships</p>
        </a>
        <a title="Category Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/category" class="scholorshipCard">
          <i class="spriteIcon categoryScholarships"></i>
          <p>Category Wise Scholarships</p>
        </a>
        <a title="Type Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/type" class="scholorshipCard">
          <i class="spriteIcon typeScholarships"></i>
          <p>Type Wise Scholarships</p>
        </a>
        <a title="Gender Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/gender" class="scholorshipCard">
          <i class="spriteIcon genderScholarships"></i>
          <p>Gender Wise Scholarships</p>
        </a>
        <a title="Country Wise Scholarships" href="<?= Url::toDomain() ?>scholarships/country" class="scholorshipCard">
          <i class="spriteIcon countryScholarships"></i>
          <p>Country Wise Scholarships</p>
        </a>
      </div>
    </div>
  </section>
  <?php if (!empty($topOlympiads)) { ?>
    <section class="indexSection">
      <div class="container">
        <h2>Top Olympiads</h2>
        <div class="trendingBtnSection">
          <?php /*if (!empty($topOlympiads)) { */ ?>
          <?php foreach ($topOlympiads as $olympiad) { ?>
            <a href="<?= $olympiad['url']; ?>" class="basicCta"><?= $olympiad['display_name']; ?></a>
          <?php } ?>
          <?php /*}*/ ?>
        </div>
      </div>
    </section>
  <?php } ?>
  <section class="indexSection bg-lightgray">
    <div class="container">
      <h2>Latest News</h2>
      <div class="customSlider four-cardDisplay">
        <i class="spriteIcon scrollLeft over"></i>
        <i class="spriteIcon scrollRight"></i>
        <div class="customSliderCards">
          <?php if (!empty($latestNews)) { ?>
                <?php foreach ($latestNews as $news) { ?>
              <a class="displayCard" href="<?= Url::toNewGetmyuni() ?><?= $news['slug']; ?>">
                <figure>
                  <img loading="lazy" src="https://media.getmyuni.com/assets/images/news-images/<?= $news['banner_image']; ?>" alt="img" width="275" height="206" />
                </figure>
                <div class="textDiv">
                  <p class="widgetCardHeading">
                    <?= $news['name']; ?>
                  </p>
                  <p class="subText"><?= Yii::$app->formatter->asDate($news['updated_at'] ?? 'today') ?></p>
                </div>
              </a>
                <?php } ?>
            <div class="displayCard viewAllDiv">
              <a href="<?= Url::toNewGetmyuni() ?><?= 'latest'; ?>">
                <i class="spriteIcon viewAllIcon"></i>
                <p>VIEW ALL</p>
              </a>
            </div>
          <?php } ?>
        </div>
      </div>
    </div>
  </section>
  <section class="indexSection bg-lightgray">
    <div class="container">
      <h2>Latest Articles</h2>
      <div class="customSlider four-cardDisplay">
        <i class="spriteIcon scrollLeft over"></i>
        <i class="spriteIcon scrollRight"></i>
        <div class="customSliderCards">

          <?php if (!empty($latestArticles)) { ?>
                <?php foreach ($latestArticles as $article) { ?>
              <div class="displayCard">
                <a href="<?= Url::toDomain() ?>articles/<?= $article['slug']; ?>">
                  <figure>
                    <img loading="lazy" src="https://media.getmyuni.com/assets/images/articles/<?= $article['cover_image']; ?>" alt="img" width="275" height="206" />
                  </figure>
                  <div class="textDiv">
                    <p class="widgetCardHeading">
                      <?= $article['title']; ?>
                    </p>
                  </div>
                </a>
                <a class="authorName" href="<?= Url::toDomain() ?>author/<?= $article['user_slug']; ?>">
                  <p class="subText" style="padding-left: 20px;">
                    <?= isset($article['name']) ? $article['name'] : '' ?>
                  </p>
                </a>
              </div>
                <?php } ?>
          <?php } ?>
        </div>
      </div>
    </div>
  </section>
  <div class="aboutUsSection">
    <div class="container">
      <div class="mobileOnly">
        <h2>GetMyUni - Top Education Search Platform.</h2>
        <p class="sectionSubheading">
          Your dreams are valuable, let the experts guide to achieve them.
        </p>
      </div>
      <!-- <?php //dd($stats);
        ?> -->
      <div class="row m-0">
        <div class="col-md-6">
          <div class="row m-0">
            <div class="aboutUsCard">
              <i class="spriteIcon reviewIcon"></i>
              <p class="cardHeading">Reviews</p>
              <p class="totalCount"><?= number_format($stats['totalReviews']) ?></p>
            </div>
            <div class="aboutUsCard">
              <i class="spriteIcon ratingIcon"></i>
              <p class="cardHeading">Ratings</p>
              <p class="totalCount"><?= number_format($stats['totalRatings']) ?></p>
            </div>
            <div class="aboutUsCard">
              <i class="spriteIcon collegeIcon"></i>
              <p class="cardHeading">Colleges</p>
              <p class="totalCount"><?= number_format($stats['totalColleges']) ?></p>
            </div>
            <div class="aboutUsCard">
              <i class="spriteIcon usersIcon"></i>
              <p class="cardHeading">Users</p>
              <p class="totalCount"><?= number_format($stats['totalStudents'] + 20500) ?></p>
            </div>
            <div class="aboutUsCard">
              <i class="spriteIcon questionsIcon"></i>
              <p class="cardHeading">Questions</p>
              <p class="totalCount"><?= number_format($stats['totalQuestions']) ?></p>
            </div>
            <div class="aboutUsCard">
              <i class="spriteIcon competitionsIcon"></i>
              <p class="cardHeading">Competitions</p>
              <p class="totalCount"><?= number_format($stats['total_competitions']) ?>+</p>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <img class="websiteImg desktopOnly" src="<?= 'https://media.getmyuni.com/assets/img/home_new/site.webp' ?>" loading="lazy" alt="img" width="660" height="450" />
        </div>
      </div>
    </div>
  </div>
  <section class="indexSection student-testimonial-section">
    <div class="container">
      <h2>Our Students Say</h2>
      <div class="customSlider student-testimonial">
        <i class="spriteIcon scrollLeft over"></i>
        <i class="spriteIcon scrollRight"></i>
        <div class="customSliderCards">
          <!-- -->
          <div class="studentReviewCard">
            <figure>
              <img loading="lazy" src="https://media.getmyuni.com/assets/img/home_new/testimonials/muskan-bebele.webp" alt="img" width="45" height="45" />
            </figure>
            <span class="studentName">Muskan Bebele</span>
            <p class="subText"> B.Tech CSE,</p>
            <p class="subText">
              Sharda University
            </p>
            <div class="studentReviewDiv">
              <p class="studentReview">I am Muskan Babele from Jhansi, Uttar Pradesh. My experience at Sharda University, Greater Noida, in the B.Tech CSE course, is excellent. I got admission in June 2022. Thank you, GetMyUni, for making my career better.
              </p>
            </div>
          </div>
          <div class="studentReviewCard">
            <figure>
              <img loading="lazy" src="https://media.getmyuni.com/assets/img/home_new/testimonials/liya-dominic.webp" alt="img" width="45" height="45" />
            </figure>
            <span class="studentName">Liya Dominic</span>
            <p class="subText">B.Des,</p>
            <p class="subText">
              Presidency University
            </p>
            <div class="studentReviewDiv">
              <p class="studentReview">
                This portal has allowed me to enhance my design skills and enrich my knowledge. Presidency University has dependent teachers who make everyone thrilled about future classes. Thanks to Getmyuni for assisting me.
              </p>
            </div>
          </div>
          <div class="studentReviewCard">
            <figure>
              <img loading="lazy" src="https://media.getmyuni.com/assets/img/home_new/testimonials/vikas.webp" alt="img" width="45" height="45" />
            </figure>
            <span class="studentName">Vikas</span>
            <p class="subText">UG,</p>
            <p class="subText">
              Sharda University
            </p>
            <div class="studentReviewDiv">
              <p class="studentReview">
                I am Vikash from Samastipur, Bihar. When I was connected with GetMyUni for my future career in Computer Applications, they were more than happy to help me select the best college according to my financial preferences.
              </p>
            </div>
          </div>
          <div class="studentReviewCard">
            <figure>
              <img loading="lazy" src="https://media.getmyuni.com/assets/img/home_new/testimonials/gaurav-sanjay-kumavat.webp" alt="img" width="45" height="45" />
            </figure>
            <span class="studentName">Gaurav Sanjay Kumavat</span>
            <p class="subText">UG,</p>
            <p class="subText">
              NIMS
            </p>
            <div class="studentReviewDiv">
              <p class="studentReview">
                GetMyUni helped me to find my dream university, and I am thankful to them. Parul University of technology, Gujarat, is among the best colleges for knowledge and learning. GetMyUni’s support team helped me a lot.
              </p>
            </div>
          </div>

          <div class="studentReviewCard">
            <figure>
              <img loading="lazy" src="https://media.getmyuni.com/assets/img/home_new/testimonials/ashish-patidar.webp" alt="img" width="45" height="45" />
            </figure>
            <span class="studentName">Ashish Patidar</span>
            <p class="subText">M. Sc,</p>
            <p class="subText">
              SAGE UNIVERSITY INDORE
            </p>
            <div class="studentReviewDiv">
              <p class="studentReview">
                I am thankful for the assistance and guidance from GetMyUni counsellors while choosing the best and budget-friendly college from the never-ending list of colleges.
              </p>
            </div>
          </div>
          <div class="studentReviewCard">
            <figure>
              <img loading="lazy" src="https://media.getmyuni.com/assets/img/home_new/testimonials/bhumika.webp" alt="img" width="45" height="45" />
            </figure>
            <span class="studentName">Bhumika</span>
            <p class="subText">B.Tech CSE,</p>
            <p class="subText">
              Lovely Professional University
            </p>
            <div class="studentReviewDiv">
              <p class="studentReview">
                GetMyUni provided the best academic assistance. Here, counsellors are very supportive and helpful. When I completed 12th, I was confused about my career, but I got proper guidance in every field, so my path was clearer.
              </p>
            </div>
          </div>
          <div class="studentReviewCard">
            <figure>
              <img loading="lazy" src="https://media.getmyuni.com/assets/img/home_new/testimonials/tanvi-sharma.webp" alt="img" width="45" height="45" />
            </figure>
            <span class="studentName">Tanvi Sharma</span>
            <p class="subText">PGDM,</p>
            <p class="subText">
              Lexicon Mile
            </p>
            <div class="studentReviewDiv">
              <p class="studentReview">
                I got to know a lot about LEXICON MILE from GetMyUni. The website provided information about campus placement, courses etc. I got a call from their consultant, which greatly helped me. Thank you.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="indexSection bg-lightgray">
    <h2>GetMyUni in Media</h2>
    <div class="featuredBrandList">
      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/et.webp" alt="img" />
      </div>

      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/i-am-wire.webp" alt="img" />
      </div>

      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/inc-42.webp" alt="img" />
      </div>

      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/news-bugz.webp" alt="img" />
      </div>

      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/tech-in-asia.webp" alt="img" />
      </div>

      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/tropical-post.webp" alt="img" />
      </div>

      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/your-story.webp" alt="img" />
      </div>

      <div class="featuredCard">
        <img loading="lazy" width="100%" src="https://media.getmyuni.com/assets/img/home_new/media/bw-disrupt.webp" alt="img" />
      </div>
    </div>
  </section>
</div>

<?php
$this->registerJsFile('https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/jquery.vmap.min.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
$this->registerJsFile('https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/maps/jquery.vmap.world.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
$this->registerJsFile('https://cdnjs.cloudflare.com/ajax/libs/jQuery.Marquee/1.6.0/jquery.marquee.min.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
$this->registerJsFile('https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
?>