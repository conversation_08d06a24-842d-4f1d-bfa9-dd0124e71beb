<?php
/* @var $this \yii\web\View */
/* @var $content string */

use common\helpers\DataHelper;
use common\services\CollegeService;
use yii\helpers\Html;
use yii\widgets\Breadcrumbs;
use frontend\assets\AppAsset;
use common\widgets\Alert;
use frontend\helpers\Ad;
use frontend\helpers\Schema;
use frontend\helpers\Url;
use common\models\Exam;
use common\models\Board;
use common\models\Course;
use common\models\Article;
use common\models\Scholarship;
use common\models\Qna;
use common\models\College;
use common\models\GetgisArticle;
use common\models\Olympiad;
use frontend\helpers\Freestartads;

$isMobile = \Yii::$app->devicedetect->isMobile();
$userCityWebEnage = DataHelper::getUserLocationWebengageEvent();

AppAsset::register($this);

if (!isset($this->params['qna'])) {
    $this->registerMetaTag(['name' => 'description', 'content' => $this->context->description]);
}
$tageURL = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
$this->registerMetaTag(['property' => 'og:type', 'content' => $this->context->ogType ?? 'website']);
$this->registerMetaTag(['property' => 'og:title', 'content' => ($this->context->ogTitle) ?? $this->title]);
$this->registerMetaTag(['property' => 'og:url', 'content' => $tageURL]);
$this->registerMetaTag(['property' => 'og:site_name', 'content' => 'Getmyuni']);
if (!isset($this->params['qna'])) {
    $this->registerMetaTag(['property' => 'og:description', 'content' => $this->context->description]);
}
$this->registerMetaTag(['property' => 'twitter:card', 'content' => 'summary_large_image']);
$this->registerMetaTag(['property' => 'twitter:site', 'content' => 'Getmyuni']);
$this->registerMetaTag(['property' => 'twitter:creator', 'content' => '@getmyuniedu']);
$this->registerMetaTag(['property' => 'twitter:url', 'content' => $tageURL]);
$this->registerMetaTag(['property' => 'twitter:title', 'content' => $this->title]);
if (!isset($this->params['qna'])) {
    $this->registerMetaTag(['property' => 'twitter:description', 'content' => $this->context->description]);
}

$currentUrl = isset($this->params['canonicalUrl']) ? $this->params['canonicalUrl'] : Url::toCanonical(Yii::$app->request->get('page'));

$this->registerLinkTag(['rel' => 'canonical', 'href' => $currentUrl]);
if (isset($this->params['amphtml']) && isset($this->params['entity'])  && isset($this->params['entity_sub_type']) && $this->params['entity'] == 'articles' && $this->params['entity_sub_type'] == 'detail') {
    $this->registerLinkTag(['rel' => 'amphtml', 'href' => $this->params['amphtml']]);
}

if (isset($this->params['links'], $this->params['links']['next'])) {
    $this->registerLinkTag(['rel' => 'next', 'href' => $this->params['links']['next']]);
}

if (isset($this->params['links'], $this->params['links']['prev'])) {
    $this->registerLinkTag(['rel' => 'prev', 'href' => $this->params['links']['prev']]);
}

if (!empty($this->context->ogImage)) {
    $this->registerMetaTag(['property' => 'og:image', 'content' => $this->context->ogImage]);
    $this->registerMetaTag(['property' => 'twitter:image', 'content' => $this->context->ogImage]);
}

if (isset($this->params['robots'])) {
    $this->registerMetaTag(['name' => 'robots', 'content' => $this->params['robots']]);
}

$currentPath = explode('/', $_SERVER['REQUEST_URI']);
$countryArray = ['canada', 'usa', 'uk', 'germany', 'australia'];

if (Yii::$app->request->get('show_lead_form') == 'true') {
    $this->params['cta_location'] = "'campaign_auto_pop_up_freezed'";
    $this->params['utm_source'] = Yii::$app->request->get('utm_source');
    $this->params['utm_campaign'] = Yii::$app->request->get('utm_campaign');
    $this->params['utm_medium'] = Yii::$app->request->get('utm_medium');
}

$interested_location_state = empty($this->params['interested_location']) ? null : CollegeService::getStateByCityId($this->params['interested_location']);
$lang_code = isset(DataHelper::$languageCode[Yii::$app->language]) ? Yii::$app->language : 'en';

$cssNotRender = ['common-application-form', 'custom-landing-page'];
$notAllowedPages = ['common-application-form', 'study-abroad', 'scholarship-program', 'custom-landing-page'];

if (!in_array(Yii::$app->controller->id, $cssNotRender)) {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'style.css');
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'global.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'script.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'filter_script.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'login.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'lead_form_v4.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
}

if (!in_array(Yii::$app->controller->id, $notAllowedPages)) {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'lead_form_v4.css');
}
if (!$isMobile) {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'header.css');
}

if (isset($this->params['entity']) && !empty($this->params['entity']) && $this->params['entity'] == 'home') {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'getmyUniHomeFooter.css', ['depends' => [AppAsset::class]]);
} else {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'bottom-widget.css', ['depends' => [AppAsset::class]]);
}

if (Yii::$app->controller->id == 'custom-landing-page') {
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'clp_script.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'slick-slick.min.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'clp_slick.css');
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'slick-theme.min.css', ['depends' => [AppAsset::class]]);
}

if (Yii::$app->controller->id == 'common-application-form') {
    $this->registerCssFile('https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css');
    $this->registerCssFile('https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap-theme.min.css');
    $this->registerCssFile('https://media.getmyuni.com/azure/assets/css/main_page.min.css');
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'common_application_form.css');
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'common_application_form.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
}

if ((Yii::$app->controller->id == 'study-abroad') || (Yii::$app->controller->id == 'scholarship-program') || (isset($this->params['identifier']) && $this->params['identifier'] == 'study-abroad-article')) {
    // Register jQuery first without defer to ensure it's loaded before other scripts
    $this->registerJsFile('https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js', ['position' => \yii\web\View::POS_HEAD]);

    $this->registerCssFile('https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/css/intlTelInput.css');
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'sa_lead_form.css');

    // Register other JS files with dependencies
    $this->registerJsFile('https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js', ['position' => \yii\web\View::POS_END, 'depends' => [AppAsset::class]]);
    $this->registerJsFile('https://www.getmyuni.com/yas/js/version2/ajax/libs/js/select2.min.js', ['position' => \yii\web\View::POS_END, 'depends' => [AppAsset::class]]);

    // Register intl-tel-input scripts
    $this->registerJsFile('https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/intlTelInput.min.js', ['position' => \yii\web\View::POS_END]);
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'intl-tel-input-init.js', ['position' => \yii\web\View::POS_END, 'depends' => ['yii\web\JqueryAsset']]);

    // Register SA lead form script last
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'sa_lead_form.js', ['position' => \yii\web\View::POS_END, 'depends' => ['yii\web\JqueryAsset']]);
}

if (Yii::$app->controller->id == 'exam') {
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'college_predictor.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
}

if ((Yii::$app->controller->id == 'study-abroad') || (Yii::$app->controller->id == 'scholarship-program')) {
    // Register CSS files
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'abroad.css');
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'abroad.js', ['position' => \yii\web\View::POS_END, 'depends' => [AppAsset::class]]);
    $this->registerJsFile('https://www.getmyuni.com/assets/js/circlos.js', ['position' => \yii\web\View::POS_END, 'depends' => [AppAsset::class]]);
    $this->registerJsFile('https://www.getmyuni.com/assets/js/vendor/jquery-scrolldepth/jquery.scrolldepth.min.js', ['position' => \yii\web\View::POS_END, 'depends' => [AppAsset::class]]);
    $this->registerJsFile('https://www.getmyuni.com/assets/js/vendor/scroll-to-fixed/jquery-scrolltofixed-min.js', ['position' => \yii\web\View::POS_END, 'depends' => [AppAsset::class]]);
}


if (Yii::$app->controller->id == 'site') {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'search.css');
    $this->registerJsFile(Yii::$app->params['jsPath'] . 'home_page.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]);
}

$host = $_SERVER['HTTP_HOST'] ?? 'localhost';

?>

<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= $lang_code ?>">

<head>
    <meta charset="<?= Yii::$app->charset ?>">
    <!-- <meta http-equiv="Content-Type" content="text/html"> -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0" />
    <meta name="theme-color" content="#545ebd">
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <?php if (in_array($host, ['admission.getmyuni.com', 'admissions.mycollegeedu.com'])): ?>
        <meta name="robots" content="noindex, nofollow">
    <?php endif; ?>
    <!-- <link rel="dns-prefetch" href="//cdnjs.cloudflare.com/"> -->
    <link rel="preconnect" href="https://www.googletagmanager.com/" crossorigin>
    <link rel="preconnect" href="//www.google-analytics.com" />
    <!-- <link rel="preconnect" href="https://cdnjs.cloudflare.com/" /> -->
    <link rel="preconnect" href="https://securepubads.g.doubleclick.net">
    <link rel="dns-prefetch" href="//www.google-analytics.com" />
    <link rel="dns-prefetch" href="https://securepubads.g.doubleclick.net">
    <!-- <link rel="dns-prefetch" crossorigin href="https://fonts.googleapis.com/" /> -->
    <link rel="shortcut icon" type="image/png" href="<?= Url::toDomain() ?>favicon.png" />
    <link rel="icon" href="<?= Url::toDomain() ?>favicon.png" type="image/x-icon">
    <?php /* if ($this->params['entity'] == "study-abroad") : ?>
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Open+Sans" />
        <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.3/themes/smoothness/jquery-ui.css" />
    <?php endif; */ ?>
    <link rel="stylesheet" href="/yas/css/version2/jquery-ui.min.css" media="print" onload="this.media='all'">

    <!-- FreeStart Ads start -->
    <!-- Below is a recommended list of pre-connections, which allow the network to establish each connection quicker, speeding up response times and improving ad performance. -->
    <link rel="preconnect" href="https://a.pub.network/" crossorigin />
    <link rel="preconnect" href="https://b.pub.network/" crossorigin />
    <link rel="preconnect" href="https://c.pub.network/" crossorigin />
    <link rel="preconnect" href="https://d.pub.network/" crossorigin />
    <link rel="preconnect" href="https://c.amazon-adsystem.com" crossorigin />
    <link rel="preconnect" href="https://s.amazon-adsystem.com" crossorigin />
    <link rel="preconnect" href="https://btloader.com/" crossorigin />
    <link rel="preconnect" href="https://api.btloader.com/" crossorigin />
    <link rel="preconnect" href="https://cdn.confiant-integrations.net" crossorigin />
    <!-- Below is a link to a CSS file that accounts for Cumulative Layout Shift, a new Core Web Vitals subset that Google uses to help rank your site in search -->
    <!-- The file is intended to eliminate the layout shifts that are seen when ads load into the page. If you don't want to use this, simply remove this file -->
    <!-- To find out more about CLS, visit https://web.dev/vitals/ -->
    <link rel="stylesheet" href="https://a.pub.network/getmyuni-com/cls.css">

    <!------------- End Free Start  --------------->


    <?php $this->registerCsrfMetaTags() ?>
    <title>
        <?= Html::encode($this->title) ?>
    </title>
    <?php $this->head() ?>
    <?php if (isset($this->params['schema']) && Url::toDomain() !=  Url::toBridgeU()): ?>
        <?= \yii\helpers\Html::script($this->params['schema'], ['type' => 'application/ld+json']) ?>
    <?php endif; ?>
    <?php if (isset($this->params['schema1']) && Url::toDomain() !=  Url::toBridgeU()): ?>
        <?= \yii\helpers\Html::script($this->params['schema1'], ['type' => 'application/ld+json']) ?>
    <?php endif; ?>
    <?php $statusCode = Yii::$app->response->getStatusCode(); ?>
    <?php if (isset($this->params['conversion']) && !empty($this->params['conversion'])):
        preg_match("/'send_to'\s*:\s*'([^']+)'/", $this->params['conversion'], $matches);

        if (!empty($matches[1])):
            $sendTo = $matches[1];
        endif;
    endif;
    ?>
    <script>
        var gmu = {};
        gmu.config = {
            showLeadForm: 'true',
            scriptConversion : "<?= $sendTo ?? '' ?>",
            entity: '<?= $this->params['entity'] ?? 'auto-popup' ?>',
            product_mapping_entity: '<?= $this->params['product_mapping_entity'] ?? 'false' ?>',
            product_mapping_entity_id: <?= $this->params['product_mapping_entity_id'] ?? 0 ?>,
            pageName: '<?= $this->params['pageName'] ?? '' ?>',
            entity_id: <?= $this->params['entity_id'] ?? 0 ?>,
            college_course_count: '<?= $this->params['course_count'] ?? '' ?>',
            course_id: '<?= $this->params['course_id'] ?? '' ?>',
            program_id: '<?= $this->params['program_id'] ?? '' ?>',
            entity_name: "<?= $this->params['entity_name'] ?? '' ?>",
            entity_slug: '<?= $this->params['entitySlug'] ?? '' ?>',
            board_level: '<?= $this->params['board_level'] ?? '' ?>',
            display_name: "<?= $this->params['entityDisplayName'] ?? '' ?>",
            entity_subpage_name: '<?= $this->params['entity_subpage_name'] ?? '' ?>',
            pageName: '<?= $this->params['pageName'] ?? '' ?>',
            page_category: '<?= $this->params['page_category'] ?? '' ?>',
            entity_type: '<?= Yii::$app->controller->entityType ?? Yii::$app->controller->id ?>',
            entity_subtype: '<?= Yii::$app->controller->pageType ?? Yii::$app->controller->action->id ?>',
            entity_city: '<?= Yii::$app->controller->entityCity ?? '' ?>',
            interested_location: '<?= $this->params['interested_location'] ?? null ?>',
            interested_location_state: '<?= $interested_location_state ?? null ?>',
            cta_location: <?= $this->params['cta_location'] ?? "'auto-popup'" ?>,
            auto_popup_form_title: '<?= $this->params['auto_popup_form_title'] ?? null ?>',
            auto_popup_form_text: "<?= $this->params['auto_pop_up_text'] ?? null ?>",
            show_lead_form: <?= (Yii::$app->request->get('show_lead_form') == 'true') ? 'true' : 'false' ?>,
            utm_source: '<?= (Yii::$app->request->get('utm_source') != null) ? Yii::$app->request->get('utm_source') : '' ?>',
            utm_campaign: '<?= (Yii::$app->request->get('utm_campaign') != null) ? Yii::$app->request->get('utm_campaign') : '' ?>',
            utm_medium: '<?= (Yii::$app->request->get('utm_medium') != null) ? Yii::$app->request->get('utm_medium') : '' ?>',
            sponsor_params: <?= isset($this->params['sponsorParams']) ? json_encode($this->params['sponsorParams']) : '[]' ?>,
            courseChartData: <?= isset($this->params['chartData']) ? json_encode($this->params['chartData']) : '[]' ?>,
            isLoggedIn: <?= (\Yii::$app->user->isGuest) ? 'false' : 'true' ?>,
            previous_url: '<?= Yii::$app->request->referrer ?? '' ?>',
            page_url: '<?= $tageURL ?? '' ?>',
            isMobile: '<?= \Yii::$app->devicedetect->isMobile() ?>',
            mapData: <?= isset($this->params['stateList']) ? json_encode($this->params['stateList']) : '[]' ?>,
            statusCode: <?= $statusCode ?? ''; ?>,
            currentLocation: '<?= $tageURL ?>',
            dynamicCta: <?= empty($this->params['dynamicCta']) ? '[]' : json_encode($this->params['dynamicCta']) ?>,
            csrf: '<?= Yii::$app->request->csrfToken ?>',
            language: '<?= $lang_code ?>',
            domainUrl: '<?= Url::toDomain() ?>',
            prodEvn: '<?= YII_ENV ?>',
            careerChartData: <?= isset($this->params['careerChartData']) ? json_encode($this->params['careerChartData']) : '[]' ?>,
            cityWebengage: '<?= $userCityWebEnage['cityName']  ?? '' ?>',
            stateWebengage: '<?= $userCityWebEnage['stateName']  ?? '' ?>',
            streamWebengage: '<?= (isset($this->params['streamWebengage']) && $this->params['streamWebengage'] != null) ? $this->params['streamWebengage'] : ''  ?>',
            levelWebengage: '<?= (isset($this->params['levelWebengage']) && $this->params['levelWebengage'] != null) ? $this->params['levelWebengage'] : '' ?>',
            examWebengage: '<?= (isset($this->params['examWebengage']) && $this->params['examWebengage'] != null) ? $this->params['examWebengage'] : '' ?>',

        };

        var googletag = googletag || {};
        googletag.cmd = googletag.cmd || [];
        // GPT slots
        var gptAdSlots = [];
        googletag.cmd.push(function() {
            <?php echo Ad::renderAdScript() ?>

            // Configure SRA
            googletag.pubads().enableSingleRequest();
            googletag.pubads().enableLazyLoad();
            // Start ad fetching
            googletag.enableServices();
        });

        var freestar = freestar || {};
        freestar.queue = freestar.queue || [];
        freestar.config = freestar.config || {};
        freestar.config.enabled_slots = [];

        freestar.initCallback = function() {
            (freestar.config.enabled_slots.length === 0) ? freestar.initCallbackCalled = false: freestar.newAdSlots(freestar.config.enabled_slots)
        }
    </script>
    <script src="https://a.pub.network/getmyuni-com/pubfig.min.js" data-cfasync="false" async></script>

    <script id="_webengage_script_tag" type="text/javascript">
        var webengage;
        ! function(w, e, b, n, g) {
            function o(e, t) {
                e[t[t.length - 1]] = function() {
                    r.__queue.push([t.join("."), arguments])
                }
            }
            var i, s, r = w[b],
                z = " ",
                l = "init options track screen onReady".split(z),
                a = "feedback survey notification".split(z),
                c = "options render clear abort".split(z),
                p = "Open Close Submit Complete View Click".split(z),
                u = "identify login logout setAttribute".split(z);
            if (!r || !r.__v) {
                for (w[b] = r = {
                        __queue: [],
                        __v: "6.0",
                        user: {}
                    }, i = 0; i < l.length; i++) o(r, [l[i]]);
                for (i = 0; i < a.length; i++) {
                    for (r[a[i]] = {}, s = 0; s < c.length; s++) o(r[a[i]], [a[i], c[s]]);
                    for (s = 0; s < p.length; s++) o(r[a[i]], [a[i], "on" + p[s]])
                }
                for (i = 0; i < u.length; i++) o(r.user, ["user", u[i]]);
                setTimeout(function() {
                    var f = e.createElement("script"),
                        d = e.getElementById("_webengage_script_tag");
                    f.type = "text/javascript",
                        f.async = !0,
                        f.src = ("https:" == e.location.protocol ? "https://widgets.in.webengage.com" : "http://widgets.in.webengage.com") + "/js/webengage-min-v-6.0.js",
                        d.parentNode.insertBefore(f, d);
                }, 10000)
            }
        }(window, document, "webengage");
        webengage.init('in~d3a49c51');
        webengage.onReady(function() {
            webengage.notification.onOpen(function(data) {
                var a = document.getElementById('webklipper-publisher-widget-container-notification-frame');
                var layoutId = a.dataset.notificationLayoutId;
                if (layoutId == '~483819h') {
                    a.style.left = 'auto';
                    a.style.top = 'auto';
                    a.style.transform = 'none';
                    a.style.zIndex = 1;
                    var _stickyBtn = document.getElementsByClassName("getSupport").length ? document.getElementsByClassName("getSupport")[0] : null;
                    a.style.bottom = '0px';
                    if (_stickyBtn) {
                        a.style.bottom = _stickyBtn.getBoundingClientRect().height + 'px';
                    }
                    var throttle = function(callback, delay) {
                        var throttleTimeout = null;
                        var storedEvent = null;

                        var throttledEventHandler = function(event) {
                            storedEvent = event;

                            var shouldHandleEvent = !throttleTimeout;

                            if (shouldHandleEvent) {
                                callback(storedEvent);

                                storedEvent = null;

                                throttleTimeout = setTimeout(function() {
                                    throttleTimeout = null;

                                    if (storedEvent) {
                                        throttledEventHandler(storedEvent);
                                    }
                                }, delay);
                            }
                        };

                        return throttledEventHandler;
                    };
                    var scrollThrottle = throttle(function() {
                        if (_stickyBtn) {
                            a.style.bottom = _stickyBtn.getBoundingClientRect().height + 'px';
                        }
                    }, 1000);
                    document.addEventListener("scroll", scrollThrottle);
                }
            });
        });
    </script>
    <?= $this->render('_google-scripts') ?>

    <?php if (isset($this->params['entity']) && ($this->params['entity'] == Exam::ENTITY_EXAM || $this->params['entity'] == 'articles')): ?>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <?php endif; ?>

    <?php if (isset($this->params['analytics']) && !empty($this->params['analytics'])): ?>
        <?= $this->params['analytics'] ?>
    <?php endif; ?>

    <?php if (isset($this->params['remarketing']) && !empty($this->params['remarketing'])): ?>
        <?= $this->params['remarketing'] ?>
    <?php endif; ?>
</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MMVP8S" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<script>
    (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
            'gtm.start': new Date().getTime(),
            event: 'gtm.js'
        });
        var f = d.getElementsByTagName(s)[0],
            j = d.createElement(s),
            dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src =
            'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-MMVP8S');
</script>
<?php   $this->beginBody() ?>

<?php if (Yii::$app->controller->id == 'news') { ?>
    <header class="page-header">
        <?= $this->render('/layouts/_newsheader'); ?>
    </header>
    <?php if (isset($this->params) && !empty($this->params['entity']) && $this->params['entity'] == 'news' && Yii::$app->request->get('amp') !== 'amp' && $isMobile && isset($this->params['page_category']) && $this->params['page_category'] !== 'news-no-cta'): ?>
        <div class="lead-cta" data-entity="news" data-lead_cta='6'></div>
    <?php endif; ?>
    <?php if (!empty($this->params['topsearchNews'])): ?>
        <?= $this->render('/layouts/_topserach', ['topsearchNews' => $this->params['topsearchNews']]); ?>
    <?php endif; ?>
<?php } elseif (Yii::$app->controller->id == 'study-abroad') { ?>
    <header class="page-header" style="margin: 0;">
        <?= $this->render('/layouts/_abroadHeader'); ?>
    </header>
<?php } elseif (Yii::$app->controller->id == 'scholarship-program') { ?>
    <div class="twoDbackground"></div>
    <?= $this->render('/layouts/_abroadHeader'); ?>
<?php } else { ?>
    <header class="page-header" id="primary-js-nav">
        <?= $this->render('/layouts/_header'); ?>
    </header>
<?php } ?>
<?php //$this->render('/lead/lead_v2/_form.php');
?>
<?php if (Yii::$app->controller->id == 'college'):
    $currentUrl = (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs'])) ? current(array_filter($this->params['breadcrumbs'], function ($item) {
        return isset($item['url']) && '/all-colleges' == $item['url'];
    })) : false;
    if ($currentUrl != false):
        echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
                ]) ?>
            </div>
        </nav>
        <?php
    elseif (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && in_array(Yii::$app->controller->id, DataHelper::$breadCrumbList) && (substr_count(Yii::$app->request->url, '/') < 2) && !isset($this->params['pageName'])): ?>
        <?php echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
                ]) ?>
            </div>
        </nav>
    <?php endif; ?>
<?php elseif (Yii::$app->controller->id == Exam::ENTITY_EXAM):
        $currentUrl = (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs'])) ? current(array_filter($this->params['breadcrumbs'], function ($item) {
            return isset($item['url']) && '/exams' == $item['url'];
        })) : false;
    if ($currentUrl != false):
        echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                    <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
            ]) ?>
            </div>
        </nav>
        <?php
    elseif (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && in_array(Yii::$app->controller->id, DataHelper::$breadCrumbList) && (in_array(Yii::$app->controller->action->id, DataHelper::$examBreadCrumList))): ?>
            <?php echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
                ]) ?>
            </div>
        </nav>
    <?php endif; ?>
<?php elseif (Yii::$app->controller->id == Board::ENTITY_BOARD):
        $currentUrl = (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs'])) ? current(array_filter($this->params['breadcrumbs'], function ($item) {
            return isset($item['url']) && '/boards' == $item['url'];
        })) : false;
    if ($currentUrl != false):
        echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                    <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
            ]) ?>
            </div>
        </nav>
            <?php
    elseif (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && in_array(Yii::$app->controller->id, DataHelper::$breadCrumbList) && (substr_count(Yii::$app->request->url, '/') < 2)): ?>
            <?php
            echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv course-borad-design-breadcrumb news">
            <div class="container">
                <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
                ]) ?>
            </div>
        </nav>
    <?php endif; ?>

<?php elseif (Yii::$app->controller->id == Qna::ENTITY_QNA_URL):
        $currentUrl = (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs'])) ? current(array_filter($this->params['breadcrumbs'], function ($item) {
            return isset($item['url']) && '/q-n-a' == $item['url'];
        })) : false;
    if ($currentUrl != false):
        echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                    <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
            ]) ?>
            </div>
        </nav>
            <?php
    elseif (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && in_array(Yii::$app->controller->id, DataHelper::$breadCrumbList) && (substr_count(Yii::$app->request->url, '/') < 2)): ?>
            <?php echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
                ]) ?>
            </div>
        </nav>
    <?php endif; ?>
<?php elseif (Yii::$app->controller->id == Course::ENTITY_COURSE):
        $currentUrl = (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs'])) ? current(array_filter($this->params['breadcrumbs'], function ($item) {
            return isset($item['url']) && '/courses' == $item['url'];
        })) : false;
    if ($currentUrl != false):
        echo Schema::breadcrumb($this->params['breadcrumbs'])
        ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
            <?= Breadcrumbs::widget([
                'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                'homeLink' => false
            ]) ?>
            </div>
        </nav>
            <?php
    elseif (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && in_array(Yii::$app->controller->id, DataHelper::$breadCrumbList) && (in_array(Yii::$app->controller->action->id, DataHelper::$examBreadCrumList))):
        ?>
            <?php
    elseif (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && in_array(Yii::$app->controller->id, DataHelper::$breadCrumbList) && (substr_count(Yii::$app->request->url, '-') == 0)):
        ?>
            <?php
            echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv course-borad-design-breadcrumb news">
            <div class="container">
                <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
                ]) ?>
            </div>
        </nav>
    <?php endif; ?>
        <?php if (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && !in_array(Yii::$app->controller->id, DataHelper::$breadCrumbList)):
            ?>
            <?php echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv news">
            <div class="container">
                <?= Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false
                ]) ?>
            </div>
        </nav>
        <?php endif; ?>
<?php endif; ?>
<?php if (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && Yii::$app->controller->id == Article::ENTITY_ARTICLE_URL): ?>
    <nav class="breadcrumbDiv news">
        <div class="container">
            <?= Breadcrumbs::widget([
                'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                'homeLink' => false
            ]) ?>
        </div>
    </nav>
<?php endif; ?>
<?php if (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && Yii::$app->controller->id == Scholarship::ENTITY_SCHOLARSHIP_URL): ?>
    <nav class="breadcrumbDiv news">
        <div class="container">
            <?= Breadcrumbs::widget([
                'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                'homeLink' => false
            ]) ?>
        </div>
    </nav>
<?php endif; ?>
<?php if (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && Yii::$app->controller->id == Olympiad::ENTITY_OLYMPIAD): ?>
    <nav class="breadcrumbDiv olympiad">
        <div class="container">
            <?= Breadcrumbs::widget([
                'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                'homeLink' => false
            ]) ?>
        </div>
    </nav>
<?php endif; ?>
<?php if (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs']) && Yii::$app->controller->id == 'college-predictor'): ?>
    <nav class="breadcrumbDiv news">
        <div class="container">
            <?= Breadcrumbs::widget([
                'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                'homeLink' => false
            ]) ?>
        </div>
    </nav>
<?php endif; ?>

<div class="blueBgDiv mobileOnly">
    <!-- do not delete this -->
</div>

<?php
$allowedControllers = ['site', 'immigration', 'study-abroad', 'common-application-form', 'scholarship-program', 'custom-landing-page'];

if (in_array(Yii::$app->controller->id, $allowedControllers)): ?>
    <?= Alert::widget() ?>
    <?= $content ?>
<?php else: ?>
    <div class="container">
        <?= Alert::widget() ?>
        <?= $content ?>
    </div>
<?php endif; ?>

<!-- scroll to top -->
<img src="/yas/images/scroll_to_top.webp" class="scrollToTop" loading="lazy"></span>

<?php /* if (Url::toDomain() !=  Url::toBridgeU()): ?>
    <?= $this->render('/partials/_whatsapp_icon_integration'); ?>
<?php endif;*/ ?>
<?php if ($isMobile): ?>
    <?= $this->render('/partials/_caller_icon'); ?>
<?php endif; ?>
<?php if ((Yii::$app->controller->action->id == 'college-filter') || (Yii::$app->controller->action->id == 'all-colleges')) {  ?>
    <?= $this->render('_footer-filter-page') ?>
<?php } else { ?>
    <?= $this->render('_footer') ?>
<?php  } ?>
<?php $this->endBody() ?>
<?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
    <?php if (in_array($currentPath[1], $countryArray) || (Yii::$app->controller->id == 'article' && Yii::$app->controller->action->id == 'study-abroad-detail')): ?>
        <div id="sa-lead-form-js"></div>
    <?php else: ?>
        <div id="lead-form-js-new" style="display: none"></div>
    <?php endif; ?>
    <?php if (\Yii::$app->user->isGuest) { ?>
        <div id="login-form-js" style="display: none"></div>
    <?php } ?>
<?php endif; ?>
<div id='fees-breakup' style="display: none;"></div>
<!-- <div id="banner-popup"></div> -->

<!-- Loading Icon -->
<?php if (Yii::$app->controller->id !== 'custom-landing-page'): ?>
    <div class="pageLoader" id="filter-loader">
        <div class="pageLoaderDiv">
            <div class="circle"></div>
            <p class="loadText">Loading...</p>
        </div>
    </div>
<?php endif; ?>

<script>
    function hideAdSection(e) {
        var n = e.slot.getSlotElementId();
        if ("" != n || "undefined" != n) {
            var d = "",
                t = "",
                l = "";
            if (d = document.getElementById(n).parentNode, (null != d || "undefined" != d || "" != d)) {
                d.style.display = "none";
            }
        }
    }
    window.addEventListener('load', function() {
        setTimeout(() => {
            const script = document.createElement('script');
            script.src = 'https://securepubads.g.doubleclick.net/tag/js/gpt.js';
            document.getElementsByTagName('head')[0].appendChild(script);
            $('.lazy-ad').each(function() {
                var refreshed = false;
                var threshold = 640;
                var element = $(this);
                var scrollId = element.data('slot');
                if (element.offset().top < threshold) {
                    if (!refreshed) {
                        googletag.cmd.push(function() {
                            googletag.pubads().refresh([gptAdSlots[scrollId]]);
                        });
                        refreshed = true;
                    }
                } else {
                    var listener = function() {
                        var adSlotPos = element.offset().top;
                        var adAlmostVisibleScrollValue = adSlotPos - 700;
                        if (window.scrollY >= adAlmostVisibleScrollValue && !refreshed) {
                            googletag.cmd.push(function() {
                                googletag.pubads().refresh([gptAdSlots[scrollId]]);
                            });
                            refreshed = true;
                            window.removeEventListener('scroll', listener);
                        }
                    }
                    window.addEventListener('scroll', listener);
                }
            })
        }, 7000);
    });
</script>
<script>
    (function() {
        var base_url = window.location.origin;
        var cssAutoComplete = document.createElement('link');
        // cssAutoComplete.href = 'https://cdnjs.cloudflare.com/ajax/libs/tarekraafat-autocomplete.js/8.3.0/css/autoComplete.min.css';
        cssAutoComplete.href = base_url + '/yas/css/version2/ajax/libs/css/autoComplete.min.css';
        cssAutoComplete.rel = 'stylesheet';
        cssAutoComplete.type = 'text/css';
        document.getElementsByTagName('head')[0].appendChild(cssAutoComplete);

        var cssSelect2 = document.createElement('link');
        // cssSelect2.href = 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css';
        cssSelect2.href = base_url + '/yas/css/version2/ajax/libs/css/select2.min.css';
        cssSelect2.rel = 'stylesheet';
        cssSelect2.type = 'text/css';
        document.getElementsByTagName('head')[0].appendChild(cssSelect2);

        var cssSlick = document.createElement('link');
        // cssSlick.href = 'https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css';
        cssSlick.href = base_url + '/yas/css/version2/ajax/libs/css/slick.min.css';
        cssSlick.rel = 'stylesheet';
        cssSlick.type = 'text/css';
        document.getElementsByTagName('head')[0].appendChild(cssSlick);

        var jsAutoComplete = document.createElement('script');
        // jsAutoComplete.src = "https://cdnjs.cloudflare.com/ajax/libs/tarekraafat-autocomplete.js/8.3.0/js/autoComplete.min.js";
        jsAutoComplete.src = base_url + "/yas/js/version2/ajax/libs/js/autoComplete.min.js";
        var script = document.getElementsByTagName('script')[0];
        script.defer = true;
        script.parentNode.insertBefore(jsAutoComplete, script);

    })();

    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[loading="lazy"]');
        images.forEach(img => {
            img.src = img.dataset.src ?? img.src;
        });
    } else {
        var base_url = window.location.origin;
        // Dynamically import the LazySizes library
        const script = document.createElement('script');
        // script.src ='https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.1.2/lazysizes.min.js';
        script.src = base_url + '/yas/js/version2/ajax/libs/js/lazysizes.min.js';
        script.defer = true;
        document.body.appendChild(script);
    }
</script>
<?php if (isset($this->params['pageName']) && ($this->params['pageName'] == 'rank_predictor' || $this->params['pageName'] == 'percentile_predictor')): ?>
    <script src="<?php echo Yii::$app->params['jsPath'] . 'rank_predictor_lead.js'; ?>" defer></script>
<?php endif; ?>
<?php if (is_array($currentUrl) == false && str_contains($currentUrl, 'user-profile')): ?>
    <script src="<?php echo Yii::$app->params['jsPath'] . 'user_profile.js' ?>" defer></script>
<?php endif; ?>
<?php if (isset($this->params['pageName']) && $this->params['pageName'] == 'images-videos'): ?>
    <script>
        var jsVariable = window.location.origin + "/yas/js/version2/ajax/libs/js/jquery.fancybox.min.js";
        var script = document.createElement("script");

        script.src = jsVariable;
        script.defer = true;

        document.body.appendChild(script);
    </script>
<?php endif; ?>

<?php /*if (isset($this->params['conversion']) && !empty($this->params['conversion'])): ?>
    <?= $this->params['conversion']; ?>
<?php endif; */?>
</body>

</html>
<?php $this->endPage() ?>