<?php

use common\models\News;
use common\services\v2\NewsService;
use frontend\helpers\Url;
use common\helpers\DataHelper;

?>
<div class="newsSidebarSection">
    <ul>
        <?php if (!empty($featured)): ?>
            <li data-tab="featuredNews" class="activeLink"><?= Yii::t('app', 'Featured News'); ?></li>
        <?php endif; ?>
        <?php if (!empty($recents)): ?>
            <li data-tab="recentnews" <?= empty($featured) ? 'class="activeLink"' : '' ?>><?= Yii::t('app', 'Recent News'); ?></li>
        <?php endif; ?>
    </ul>

    <div class="trendingNews tab-content activeLink recentnewsList" id="featuredNews">
        <?php if (!empty($featured)): ?>
                <?php foreach ($featured as $feature):
                    if (empty($feature)) {
                        return '';
                    } ?>
                    <?php if (!empty($post) && $feature['slug'] == $post->slug): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <a href="<?= !empty($feature['slug']) ? Url::toNewsDetail($feature['slug'], DataHelper::getLangCode($feature['lang_code'])) : '' ?>" title="<?= !empty($feature['title']) ? $feature['title'] : '' ?>" class="listCard">
                        <div class="recentnewsDiv row">
                            <div class="sidebarImgDiv">
                                <img class="lazyload" width="96px" height="72px" loading="lazy" data-src="<?= !empty($feature['banner_image']) ? Url::toNewsImages($feature['banner_image']) : Url::toNewsImages() ?>" src="<?= !empty($feature['banner_image']) ? Url::toNewsImages($feature['banner_image']) : Url::toNewsImages() ?>" alt="<?= !empty($feature['title']) ? $feature['title'] : '' ?>">
                            </div>
                            <div class=" recentnewsDivText">
                                <p class="sidebarTextLink">
                                    <?php $tags = (new NewsService)->liveTagExpiredAt($feature['expired_at'] ?? '', $feature['is_live']); ?>
                                    <?php if (!empty($tags) && $tags == News::IS_LIVE_YES): ?>
                                        <?= $this->render('_live-updates-icon', [
                                            'models' => $feature['is_live'],
                                            'isAmp'  => 0,
                                            'smallIcone' => 1
                                        ]); ?>
                                    <?php endif; ?>
                                    <?= !empty($feature['title']) ? $feature['title'] : $feature['name'] ?></p>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
           
        <?php endif; ?>
    </div>
    <?php if (!empty($recents)): ?>
        <div class="recentnewsList recentnews tab-content <?= empty($featured) ? 'activeLink' : '' ?>" id="recentnews">
            
                <?php foreach ($recents as $recent):
                    if (empty($recent)) {
                        return '';
                    } ?>
                    <?php if (!empty($post) && $recent['slug'] == $post->slug): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <a href="<?= !empty($recent['slug']) ? Url::toNewsDetail($recent['slug'], DataHelper::getLangCode($recent['lang_code'])) : '' ?>" title="<?= !empty($recent['title']) ? $recent['title'] : '' ?>" class="listCard">
                        <div class="recentnewsDiv row">
                            <div class="sidebarImgDiv">
                                <img class="lazyload" width="96px" height="72px" loading="lazy" data-src="<?= !empty($recent['banner_image']) ? Url::toNewsImages($recent['banner_image']) : Url::toNewsImages() ?>" src="<?= !empty($recent['banner_image']) ? Url::toNewsImages($recent['banner_image']) : Url::toNewsImages() ?>" alt="<?= !empty($recent['title']) ? $recent['title'] : '' ?>">
                            </div>
                            <div class="recentnewsDivText">
                                <p class="sidebarTextLink">
                                    <?php $tags = (new NewsService)->liveTagExpiredAt($recent['expired_at'] ?? '', $recent['tag_id']); ?>
                                    <?php if (!empty($tags) && $tags == News::IS_LIVE_YES): ?>
                                        <?= $this->render('_live-updates-icon', [
                                            'models' => $recent['tag_id'],
                                            'isAmp'  => 0,
                                            'smallIcone' => 1
                                        ]); ?>
                                    <?php endif; ?>
                                    <?= !empty($recent['title']) ? $recent['title'] : '' ?></p>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            
        </div>
    <?php endif; ?>
</div>