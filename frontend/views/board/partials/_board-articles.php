<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use common\helpers\DataHelper;

$lang_code = isset(DataHelper::$languageCode[Yii::$app->language]) ? Yii::$app->language : 'en';
?>

<?php if (!empty($articles)): ?>
    <?php if (count($articles)): ?>
        <section class="pageData">
            <h2 class="row"><?= Yii::t('app', 'Explore articles on Board exams') ?>
                <a href="<?= Url::toArticleDetail('boards', $lang_code) ?>"><?= Yii::t('app', 'VIEW ALL') ?></a>
            </h2>
            <div class="customSlider four-cardDisplay">
                <div class="customSliderCards">
                    <?php $articlesCount = 0 ?>
                    <?php foreach ($articles as $article):
                        if ($articlesCount == 4) {
                            break;
                        }
                        $articlesCount++; ?>
                        <div class="sliderCardInfo">
                           
                                <a href="<?= Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code'])) ?>" title="<?= $article['h1'] ?>">
                                    <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code'])) ?>')" data-src="<?= $article['cover_image'] ? ArticleDataHelper::getImage($article['cover_image']) : ArticleDataHelper::getImage() ?>" src="<?= $article['cover_image'] ? ArticleDataHelper::getImage($article['cover_image']) : ArticleDataHelper::getImage()?>" alt="<?= $article['h1'] ?>" />

                                    <div class="textDiv">
                                        <p class="widgetCardHeading"><?= $article['h1'] ?></p>
                                        <p class="subText"><?= isset($article['user_name']) && !empty($article['user_name']) ? $article['user_name'] : $article['name'] ?? '' ?></p>
                                    </div>
                                </a>
                           
                        </div>
                    <?php endforeach; ?>

                    <div class="sliderCardInfo mobileOnly">
                        <div class="viewAllDiv">
                            <a href="<?= Url::toArticleDetail('boards', $lang_code) ?>"><i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'VIEW ALL') ?></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
<?php endif; ?>