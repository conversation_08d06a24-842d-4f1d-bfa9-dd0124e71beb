h2{
  font-size: 18px;
}
h3,
h4 {
  padding-bottom: 10px;
  line-height: 24px;
}

.heroSection {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin: 20px 0;
  }
  
  .heroSection h1 {
    font-size: 24px;
    line-height: 38px;
    padding-bottom: 20px;
    font-weight: normal;
  }
  
  .heroSection .searchBar {
    position: relative;
    margin-bottom: 20px;
  }
  
  .heroSection input#autoComplete {
      max-width: 480px;
      padding: 10px;
      padding-left: 40px;
      font-size: 14px;
      line-height: 24px;
      width: 100%;
      border-radius: 3px;
      border: var(--border-line);
      background: url(/yas/images/search-icon.png?95553ad6f0ff7425808c4b91f266559d) no-repeat;
      background-position: 15px 48%;
      color: var(--primary-font-color);
    }
    
    .heroSection input#autoComplete::-webkit-input-placeholder {
      color: var(--primary-font-color);
      opacity: 1;
    }
    
    .heroSection input#autoComplete::-moz-placeholder {
      color: var(--primary-font-color);
      opacity: 1;
    }
    
    .heroSection input#autoComplete:-ms-input-placeholder {
      color: var(--primary-font-color);
      opacity: 1;
    }
    
    .heroSection input#autoComplete::-ms-input-placeholder {
      color: var(--primary-font-color);
      opacity: 1;
    }
    
    .heroSection input#autoComplete::placeholder {
      color: var(--primary-font-color);
      opacity: 1;
    }
    
    .heroSection input#autoComplete:focus {
      background-size: auto;
    }
    
    .heroSection input#autoComplete:focus::-webkit-input-placeholder {
      padding-left: 0px;
      font-size: 14px;
    }
    
    .heroSection .autoComplete_list {
      max-width: 480px;
      border-radius: 0;
      margin: 0;
      position: absolute;
      max-height: 205px;
      overflow: auto;
      width: 100%;
      background: #fff;
      z-index: 3;
      left: 0;
      top: 46px;
    }
    
    .heroSection .autoComplete_list li.no_result {
      list-style-type: none;
    }
    
    .heroSection p {
      font-size: 14px;
      line-height: 24px;
      padding-bottom: 20px;
      font-weight: var(--font-semibold);
    }
  
.browseByCategory {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
}

.browseByCategory h2 {
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  text-transform: uppercase;
  margin-bottom: 20px;
}

.browseByCategory p {
  font-size: 15px;
  line-height: 26px;
  padding-bottom: 10px;
}

.heroSection,
.browseByCategory,
.categoryList .categoryDiv,
.latestInfoSection,
.readMoreDiv {
  box-shadow: none;
  border: var(--border-line);
}
    @media (max-width: 1023px) {
        .setAlarmDiv {
          position: fixed;
          width: 100%;
          bottom: 0;
          left: 0;
          z-index: 1;
          margin: 0;
          background: var(--color-white);
        }
      
        .setAlarmDiv .primaryBtn {
          width: 100%;
          margin: 0;
        }
      
        .heroSection {
          padding: 20px;
          margin-top: -155px;
        }
      
        .heroSection .autoComplete_list {
          margin: 0;
          top: 48px;
        }
      
        .heroSection h1 {
          font-size: 18px;
          line-height: 28px;
          padding-bottom: 10px;
          font-weight: normal;
        }
      
        .heroSection input {
          margin-bottom: 10px;
        }
      
        .heroSection p {
          padding-bottom: 10px;
        }
      
        .heroSection .upcomigExam {
          padding: 10px;
          width: 82%;
        }
      
        .heroSection .upcomigExam a {
          font-size: 14px;
          text-decoration: none;
        }
      
        .heroSection .upcomigExam .dateLabel {
          margin-right: 10px;
        }
      
  .browseByCategory {
    padding: 10px;
  }

  .browseByCategory h2 {
    margin-bottom: 10px;
  }

    }      