@charset "UTF-8";

/* ----------- Isolate signup form css from style.css file for ci project ------------- */
html {
    box-sizing: border-box;
    -ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
    box-sizing: inherit;
}

body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
   font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    background: #f3f2ef;
}

img {
    max-width: 100%;
    height: auto;
}

/* .mobileOnly {
    display: none !important;
} */

p,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}


.pageMask {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    overflow-y: hidden;
    z-index: 12;
}

.spriteIcon {
    display: inline-block !important;
    background-image: url("../../images/master_sprite.webp");
    text-align: left;
    overflow: hidden;
}

select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    box-sizing: border-box;
    width: 100%;
    background-image: url(/yas/images/select-angle.png) !important;
    background-repeat: no-repeat !important;
    background-position: 95% 19px !important;
    color: var(--primary-font-color);
}

.selection {
    width: 100% !important;
    max-width: 480px;
    top: -20px !important;
}

.signupModal input[type=checkbox] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 1px solid #dedfdf;
    width: 16px;
    height: 16px;
    display: inline-block;
    position: relative;
    cursor: pointer;
}

.signupModal input[type=checkbox]:checked {
    background: #ff4e53;
    border: 1px solid #ff4e53;
}

.signupModal input[type=checkbox].inputChecked:checked:after,
.signupModal input[type=checkbox]:checked:after {
    content: "";
    display: block;
    position: absolute;
    top: 1px;
    left: 5px;
    width: 5px;
    height: 10px;
    background: #ff4e53;
    border-style: solid;
    border-color: #fff;
    -webkit-border-image: initial;
    -o-border-image: initial;
    border-image: initial;
    border-width: 0 2px 2px 0;
    opacity: 1;
    -webkit-transform: scale(1) rotate(45deg);
    transform: scale(1) rotate(45deg);
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
    -webkit-transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
}

/* .mobileOnly {
    display: none !important;
} */

.inactiveStep {
    display: none;
}

.flagIcon {
    width: 27px;
    height: 22px;
    background-position: 415px -165px;
    vertical-align: middle;
    margin-right: 5px;
}

.questionListIcon {
    background-position: -473px -932px;
    width: 11px;
    flex-shrink: 0;
    height: 13px;
    margin-top: 3px;
}

.signupModal {
    width: 700px;
    border-radius: 4px;
    background-color: #fff;
    border: none;
    display: flex;
    flex-direction: row-reverse;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    counter-reset: listNumber;
    z-index: 12;
}

.signupModal:focus {
    outline: none;
}

.signupModal .budgetText {
    margin-top: 10px;
}

.signupModal .modalFormStepOne.detailRow {
    flex-direction: column;
}

.signupModal .modalFormStepOne.detailRow .btn-navigate-form-step {
    width: 100%;
    margin-top: 14px;
}

.signupModal .form__main__content {
    padding: 20px 30px;
    flex-grow: 1;
    max-width: 385px;
}

.signupModal .form__side__content {
    max-width: 315px;
    padding: 40px 22px 43px 20px;
    background-color: #0966C2;
    flex-shrink: 0;
}

.signupModal .form__side__content .form__img {
    text-align: center;
}

.signupModal .form__side__content .form__text ul {
    padding: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
    color: #fff !important;
    margin: 0;
    list-style-type: none;
}

.signupModal .form__side__content .form__text ul li {
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    gap: 5px;
    display: flex;
    align-items: flex-start;
}

.signupModal .form__side__content .form__text ul li span {
    color: #fff;
}

.signupModal .form__side__content .form__text ul li svg {
    margin-top: 5px;
}

.signupModal .form__side__content .form__text ul li:first-child {
    /* margin-top: 16px; */
    padding: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
    color: #fff;
    margin: 0;
    list-style-type: none;
}

.signupModal .form__side__content .form__text ul li.subHeadingOne {
    margin-top: 16px;
}

.signupModal .form__side__content .form__text ul li:last-child {
    margin-bottom: 0;
}

.signupModal .signupModalCommonHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.signupModal .textDivHeading {
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
    max-width: 319px;
}

.signupModal .inputGrid {
    margin-top: 20px;
    display: grid;
    gap: 20px;
    grid-template-columns: 1fr;
}

.signupModal .inputGrid .fullWidthContainer {
    grid-column-start: 1;
    grid-column-end: -1;
    max-width: unset !important;
    height: unset !important;
}

.signupModal .inputGrid .modalInputContainer {
    width: 100%;
    height: 40px;
    position: relative;
    border-radius: 4px;
    background-color: #fff;
    max-width: 325px;
}

.signupModal .inputGrid .modalInputContainer input.inputContainerField,
.signupModal .inputGrid .modalInputContainer select.inputContainerField {
    width: 100%;
    height: 100%;
    padding-left: 40px;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #282828;
    border: solid 1px #d8d8d8;
    border-radius: 4px;
}

.signupModal .inputGrid .modalInputContainer input.inputContainerField::placeholder,
.signupModal .inputGrid .modalInputContainer select.inputContainerField::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: #989898;
    opacity: 1;
    /* Firefox */
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
}

.signupModal .inputGrid .modalInputContainer input.inputContainerField:focus,
.signupModal .inputGrid .modalInputContainer select.inputContainerField:focus {
    outline: none;
    border: 1px solid rgb(9, 102, 194);
    background-color: rgba(9, 102, 194, 0.1);
}

.signupModal .inputGrid .modalInputContainer .select2-container {
    width: 100% !important;
    height: 100%;
}

.signupModal .inputGrid .modalInputContainer .select2-container .selection {
    height: inherit;
    display: inline-block;
}

.signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection {
    height: inherit;
    border-radius: 4px;
    border: solid 1px #d8d8d8;
}

.signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection .select2-selection__rendered {
    height: inherit;
    padding-left: 40px;
    padding-top: 5px;
}

.signupModal .inputGrid .inputLevelContainer .select2-container .selection .select2-selection .select2-selection__rendered {
    margin-left: 2px;
}

.signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection .select2-selection__placeholder {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #989898;
}

.signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection .select2-selection__arrow b {
    background-image: url(/yas/images/select-angle.png);
    background-color: transparent;
    background-size: contain;
    border: none !important;
    width: 9px;
    height: 6px;
    top: 16px;
    right: 16px;
    margin: 0;
    left: unset;
    transform: scale(1.5);
}

.signupModal .inputGrid .modalInputContainer .signupModalIcon {
    position: absolute;
    top: 12px;
    left: 16px;
}

.signupModal .inputGrid .modalInputContainer .nameIcon {
    background-position: -39px -10px;
    width: 15px;
    height: 16px;
}

.signupModal .inputGrid .modalInputContainer .emailIcon {
    background-position: -149px -13px;
    width: 16px;
    height: 12px;
    top: 15px;
}

.signupModal .inputGrid .modalInputContainer .cityIcon {
    background-position: -153px -312px;
    width: 13px;
    height: 16px;
}

.signupModal .inputGrid .modalInputContainer .courseIcon {
    background-position: -200px -13px;
    width: 21px;
    height: 13px;
    top: 14px;
    left: 13px;
}

.signupModal .inputGrid .modalInputContainer .level {
    background-position: -328px -12px;
    width: 21px;
    height: 14px;
    top: 15px;
    left: 14px;
}

.signupModal .inputGrid .modalInputContainer .heartIcon {
    background-position: -281px -12px;
    width: 17px;
    height: 15px;
}

.signupModal .inputGrid .modalInputContainer .collegeLocationIcon {
    background-position: -361px -10px;
    width: 14px;
    height: 18px;
    top: 11px;
}

.headingIconTick {
    background-position: -176px -11px;
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin-top: 4px;
}

.signupModal .inputGrid .inputMobileContainer,
.signupModal .inputGrid .inputMobileContainerRank {
    display: flex;
}

.signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv {
    position: relative;
    max-width: 105px;
    display: inline-block;
    height: 100%;
}

.signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv .flagIcon {
    position: absolute;
    z-index: 1;
    top: 10px;
    left: 16px;
}

.signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv .select2-selection {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

.signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv .select2-selection__rendered {
    padding-left: 53px !important;
}

.signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv .select2-selection__arrow b {
    right: 10px !important;
}

.signupModal .inputGrid .inputMobileContainer .inputContainerField {
    /* border-left: 0 !important; */
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    padding-left: 10px !important;
}

.inputMobileContainer .countryCode.otherCountryCode,
.inputMobileContainerRank .countryCode.otherCountryCode {
    flex-basis: 101px;
    position: relative;
}

.inputMobileContainer .countryCode input,
.inputMobileContainerRank .countryCode input,
.inputMobileContainer .countryCode .dialCodeDiv,
.inputMobileContainerRank .countryCode .dialCodeDiv {
    background: #eaeaea;
    border-radius: 4px 0 0 4px;
    text-align: center;
    padding: 11px 12px;
    outline: none;
    width: 99.56px;
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    max-width: 105px;
    background-color: #fff;
    border: solid 1px #d8d8d8;
    border-right: none;
}

.signupModal .inputGrid .inputOTPContainer .sendOtpButton {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #3d8ff2;
    position: absolute;
    top: 8px;
    right: 16px;
    cursor: pointer;
    display: none;
}

.signupModal .inputGrid .disabledSendOtpButton {
    color: rgba(61, 143, 242, 0.4) !important;
    pointer-events: none;
}

.signupModal .inputGrid .inputOTPContainer {
    display: none;
}

.signupModal .inputGrid .inputOTPContainer .OTPField {
    padding-left: 16px !important;
}

.signupModal .inputGrid .inputOTPContainer .OTPField::-webkit-outer-spin-button,
.signupModal .inputGrid .inputOTPContainer .OTPField::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.signupModal .inputGrid .inputOTPContainer .otpTimer {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #ff4e53;
    position: absolute;
    right: 16px;
    top: 8px;
}

.signupModal .inputGrid .geoLocationContainer.mobileOnly {
    height: auto;
}

.signupModal .inputGrid .geoLocationContainer.mobileOnly .locationIcon {
    background-position: -253px -882px;
    width: 13px;
    height: 17px;
    position: static;
    vertical-align: middle;
}

.signupModal .inputGrid .geoLocationContainer.mobileOnly .locationText {
    margin: 0;
    display: inline-block;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.67;
    color: #787878;
    margin-left: 12px;
}

.signupModal .inputGrid .geoLocationContainer.mobileOnly .locationText .cityLocation {
    color: #282828;
}

.signupModal .inputGrid .geoLocationContainer.mobileOnly .locationText .changeCity {
    text-decoration: none;
    color: #3d8ff2;
}

.signupModal .inputGrid.modalFormStepTwo {
    row-gap: 10px;
}

.signupModal .desktop__tnc__checkbox {
    flex-shrink: 0;
}

.signupModal .detailRow {
    display: flex;
    margin-top: 20px;
    justify-content: space-between;
    align-items: flex-end;
}

.signupModal .detailRow .termsAndLogin {
    display: flex;
    gap: 10px;
    width: 100%;
}

.signupModal .detailRow .termsAndLogin .termsAndConditionText {
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    color: #787878;
}

.signupModal .detailRow .termsAndLogin .termsAndConditionText a {
    color: #787878;
    font-weight: 600;
}

.signupModal .detailRow .accountExistsText {
    font-size: 15px;
    font-weight: 500;
    line-height: 1.6;
    color: #282828;
    margin-top: 10px;
    width: 100%;
    text-align: center;
}

.signupModal .detailRow .accountExistsText a {
    color: #ff4e53;
}

.signupModal .detailRow .btn-navigate-form-step {
    width: 100%;
    height: 36px;
    border-radius: 3px;
    background-color: #ff4e53;
    font-size: 14px;
    font-weight: bold;
    line-height: 1.71;
    color: #fff;
    border: none;
}

.signupModal .detailRow .btn-navigate-form-step:disabled {
    background-color: rgba(255, 78, 83, 0.4);
}

.signupModal .detailRow .backButton {
    border-radius: 3px;
    border: solid 1px #ff4e53;
    background-color: #fff;
    color: #ff4e53;
}

.signupModal .modalInputLabelContainer .modalInputLabel {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.33;
    color: #787878;
}

.signupModal .admissionRadioContainer {
    display: flex;
    align-items: center;
    height: auto !important;
}

.signupModal .admissionRadioContainer .admissionRadioRow {
    display: flex;
}

.signupModal .admissionRadioContainer .admissionRadioRow .admissionLabel {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #989898;
    margin-left: 10px;
    margin-right: 16px;
    cursor: pointer;
    display: inline-block;
    position: relative;
    left: -4px;
}

.signupModal .admissionRadioContainer .admissionRadioRow .admissionRadio {
    margin: 0;
    accent-color: #ff4e53;
    width: 18px;
    height: 18px;
    vertical-align: middle;
}

.signupModal .examScoreContainer .enterScorePrompt {
    font-size: 15px;
    font-weight: normal;
    line-height: 20px;
    color: #787878;
    display: flex;
    align-items: center;
}

.signupModal .examScoreContainer .enterScorePrompt::before {
    content: "•";
    color: #d8d8d8;
    display: inline-block;
    margin-right: 8px;
    font-size: 20px;
}

.signupModal .examScoreContainer .examScoreBoxList {
    display: flex;
    gap: 20px;
    padding-top: 8px;
}

.signupModal .examScoreContainer .examScoreBoxList .examScoreBox {
    border-radius: 4px;
    border: solid 1px #d8d8d8;
    max-width: 150px;
    height: 48px;
    padding: 12px 0px;
    padding-left: 16px;
}

.signupModal .examScoreContainer .examScoreBoxList .examScoreBox::-webkit-outer-spin-button,
.signupModal .examScoreContainer .examScoreBoxList .examScoreBox::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.signupModal .examScoreContainer .examScoreBoxList .examScoreBox::placeholder {
    font-size: 13px;
    font-weight: normal;
    line-height: 1.85;
    color: #989898;
}

.signupModal .eduBudgetContainer,
.signupModal .distanceCourseContainer {
    margin-top: 8px;
}

.signupModal .distanceCourseContainer {
    display: flex;
    align-items: center;
    gap: 10px;
    height: auto !important;
    margin-top: 0;
    /* Rounded sliders */
}

.signupModal .distanceCourseContainer input:checked+.slider {
    background-color: #f5f5f5;
}

.signupModal .distanceCourseContainer input:not(:checked)~.yesOption {
    color: #fff;
    transition: 0.4s;
}

.signupModal .distanceCourseContainer input:checked~.noOption {
    color: #fff;
    transition: 0.4s;
}

.signupModal .distanceCourseContainer input:focus+.slider {
    box-shadow: 0 0 1px #f5f5f5;
}

.signupModal .distanceCourseContainer input:checked+.slider:before {
    -webkit-transform: translateX(51px);
    -ms-transform: translateX(51px);
    transform: translateX(51px);
}

.signupModal .distanceCourseContainer .slider.round {
    border-radius: 34px;
}

.signupModal .distanceCourseContainer .slider.round:before {
    border-radius: 35px;
}

.signupModal .distanceCourseContainer .distanceCourseText {
    font-size: 14px;
    font-weight: 400;
    line-height: 1.87;
    color: #787878;
}

.signupModal .recommendedCollegeContainer {
    display: flex;
    align-items: flex-start;
}

.signupModal .recommendedCollegeContainer .collegeItemImage {
    width: 45px;
    height: 45px;
    flex-shrink: 0;
    margin-right: 10px;
    background-color: #0966c2;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails {
    display: flex;
    max-width: 418px;
    flex-wrap: wrap;
    flex-grow: 1;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemHeading {
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    color: #0d3d63;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex-basis: 100%;
    padding-bottom: 0px;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemFees,
.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating {
    display: flex;
    align-items: center;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemFees p:first-child,
.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating p:first-child {
    font-size: 12px;
    font-weight: 600;
    color: #282828;
    line-height: 1;
    margin-right: 5px;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemFees p:last-of-type,
.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating p:last-of-type {
    font-size: 12px;
    font-weight: 600;
    line-height: 1;
    color: #787878;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating p {
    font-weight: 500 !important;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating::before {
    content: "|";
    margin-left: 5px;
    margin-right: 5px;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating .collegeItemRatingList {
    margin: 0;
    padding: 0;
    list-style-type: none;
    display: flex;
    align-items: center;
    margin-left: 5px;
}

.signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating .collegeItemRatingList .collegeItemRatingStar {
    background-position: -243px -633px;
    width: 17px;
    height: 17px;
    transform: scale(0.9);
}

.signupModal .educationDetailsContainer .educationLabel {
    font-size: 15px;
    font-weight: normal;
    line-height: 1.6;
    color: #282828;
}

.signupModal .educationDetailsContainer .educationDetailsRow {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 20px;
    max-width: unset;
}

.signupModal .educationDetailsContainer .educationDetailsRow:nth-of-type(2) {
    margin-top: 20px;
}

.signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem {
    background-color: #fff;
    color: #989898 !important;
}

.signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem:nth-child(3n+1) {
    flex-basis: 40%;
    max-width: 270px;
    flex-grow: 1;
    padding-left: 16px;
}

.signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem:nth-child(3n+2) {
    flex-basis: 30%;
    max-width: 175px;
    padding-left: 16px;
    background-position: 90% 19px !important;
}

.signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem:nth-child(3n+3) {
    flex-basis: 30%;
    max-width: 175px;
    padding-left: 12px;
    padding-right: 0;
}

.signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem::placeholder {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #989898;
}

.specializationContainer {
    margin-bottom: 10px;
}

.budgetContainer {
    display: flex;
    margin-bottom: 10px;
}

.budgetContainer .budgetDiv {
    flex: 1;
    border: 1px solid #d8d8d8;
    color: #989898;
    padding: 0 7px;
}

.budgetContainer .budgetDiv:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.budgetContainer .budgetDiv:last-of-type {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-right: 1px solid #d8d8d8 !important;
}

.budgetContainer .budgetDiv:has(input:checked) {
    border: 1px solid #ff4e53 !important;
    color: #ff4e53;
}

.budgetContainer .budgetDiv:not(:last-child) {
    border-right: 0px solid #d8d8d8;
}

.budgetContainer .budgetDiv label {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    white-space: nowrap;
}

.budgetContainer .budgetDiv input {
    opacity: 0;
    width: 0;
    height: 0;
}

.lead__form__logo {
    position: absolute;
    top: 20px;
    left: 20px;
}

.closeDialog {
    background-position: -11px -10px;
    width: 16px;
    height: 16px;
    top: 20px;
    right: 20px;
    position: absolute;
    cursor: pointer;
    z-index: 1;
}

.finalScreen {
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin: 107px 0;
}

.finalScreen .formSubmitIcon {
    background-position: -253px -931px;
    width: 100px;
    height: 100px;
    margin-bottom: 10px;
}

.finalScreen p:first-of-type {
    font-size: 36px;
    font-weight: 600;
    color: #282828;
    margin-bottom: 10px;
}

.finalScreen p:last-of-type {
    font-size: 15px;
    font-weight: normal;
    line-height: 1.6;
    color: #282828;
}

.signupModal .recommendedCollegeContainer {
    padding-bottom: 14px;
    border-bottom: 1px solid #d8d8d8;
}

.signupModal .recommendedCollegeContainer:last-child {
    padding-bottom: 0;
    border: none;
}

.modalFormStepThree.inputGrid {
    row-gap: 15px;
    border: 1px solid #d8d8d8;
    max-width: 100%;
    padding: 23px 16px;
    position: relative;
}

.modalFormStepThree.inputGrid::after {
    content: " ";
    display: inline-block;
    position: absolute;
    bottom: 0px;
    /* background-image: linear-gradient(to top, #ffffff, rgba(255, 255, 255, 0)); */
    width: 100%;
    height: 50px;
}

.modalFormStepThree.detailRow {
    margin: 0 !important;
}

.examScoreContainer .container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.examScoreContainer .material-textfield {
    position: relative;
}

.examScoreContainer label {
    position: absolute;
    font-size: 14px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: white;
    color: #989898;
    padding: 0 0.3rem;
    margin: 0 0.5rem;
    transition: 0.1s ease-out;
    transform-origin: left top;
    pointer-events: none;
}

.examScoreContainer input {
    font-size: 14px;
    outline: none;
    border: 1px solid #d8d8d8;
    border-radius: 5px;
    padding: 1rem 0.7rem;
    color: #282828;
    transition: 0.1s ease-out;
}

.examScoreContainer input:focus {
    border-color: #d8d8d8;
}

.examScoreContainer input:focus+label {
    color: #989898;
    top: 0;
    transform: translateY(-50%) scale(0.9) !important;
}

.examScoreContainer input:not(:placeholder-shown)+label {
    top: 0;
    transform: translateY(-50%) scale(0.9) !important;
}

.examScoreContainer input:not(:focus)::placeholder {
    opacity: 0;
}

.examScoreContainer input:focus::placeholder {
    opacity: 0;
}

.signupModal input[type=checkbox] {
    background: #d8d8d8;
    border: 1px solid #d8d8d8;
}

.signupModal input[type=checkbox].inputChecked:after,
.signupModal input[type=checkbox]:after {
    content: "";
    display: block;
    position: absolute;
    top: 1px;
    left: 5px;
    width: 5px;
    height: 10px;
    background: #d8d8d8;
    border-style: solid;
    border-color: #fff;
    -webkit-border-image: initial;
    -o-border-image: initial;
    border-image: initial;
    border-width: 0 2px 2px 0;
    opacity: 1;
    -webkit-transform: scale(1) rotate(45deg);
    transform: scale(1) rotate(45deg);
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
    -webkit-transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
}

.signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection .select2-selection__rendered {
    height: 40px;
}

.signupModal .datepickerContainer {
    display: none;
}

.signupModal .datepickerContainer .examScoreBoxList .examScoreBox {
    color: #989898;
    padding-right: 8px;
}

.budgetContainer .errorEducationMsg {
    top: 40px;
}

.specializationContainer .select2-container .select2-search--inline .select2-search__field,
.destinationContainer .select2-container .select2-search--inline .select2-search__field {
    font-size: 14px;
    margin-top: 10px;
    font-family: roboto, sans-serif;
}

.specializationContainer .select2-container .select2-search--inline .select2-search__field::-webkit-input-placeholder,
.destinationContainer .select2-container .select2-search--inline .select2-search__field::-webkit-input-placeholder {
    color: #989898;
}

.signupModal .inputGrid .specializationContainer.modalInputContainer,
.signupModal .inputGrid .destinationContainer.modalInputContainer {
    height: unset;
    min-height: 40px;
}

.specializationContainer .select2-container .select2-selection--multiple,
.destinationContainer .select2-container .select2-selection--multiple {
    background-image: url(/yas/images/select-angle.png) !important;
    background-repeat: no-repeat;
    background-position: right;
    background-position-x: 98%;
    padding-right: 9px;
}

.specializationContainer .select2-container .select2-selection--multiple .select2-selection__rendered,
.destinationContainer .select2-container .select2-selection--multiple .select2-selection__rendered {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: inline-flex;
    flex-wrap: wrap;
}

.specializationContainer .select2-container .select2-selection--multiple .select2-selection__rendered::-webkit-scrollbar,
.destinationContainer .select2-container .select2-selection--multiple .select2-selection__rendered::-webkit-scrollbar {
    display: none;
}

.specializationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice,
.destinationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice {
    padding: 0px 12px;
    border-radius: 24px;
    background-color: #fff;
    height: 25px;
    font-size: 13px;
    font-weight: normal;
    color: #fff;
    display: flex;
    align-items: center;
    position: relative;
    padding-right: 30px;
    flex-shrink: 0;
    margin-top: 0px;
    border: 1px solid #ff4e53;
    align-self: flex-end;
}

.specializationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove,
.destinationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    display: inline-block;
    background-image: url("../../images/master_sprite_1.webp");
    background-position: -413px -10px;
    width: 16px;
    height: 16px;
    margin-left: 8px;
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: unset;
    left: unset;
    border: none;
    transform: scale(0.75);
}

.specializationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove span,
.destinationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove span {
    visibility: hidden;
}

.signupModal .inputGrid .specializationContainer.modalInputContainer .select2-container .selection .select2-selection .select2-selection__rendered,
.signupModal .inputGrid .destinationContainer.modalInputContainer .select2-container .selection .select2-selection .select2-selection__rendered {
    height: 100%;
    padding-top: 0px;
    padding-bottom: 7px;
    flex-wrap: nowrap;
    overflow-x: scroll;
    align-items: center;
}

.specializationContainer .select2-container--default .select2-selection--multiple,
.destinationContainer .select2-container--default .select2-selection--multiple {
    display: flex;
    padding-bottom: 0px;
    caret-color: transparent;
}

p.errorMsg {
    color: #ff4e53;
    font-size: 12px;
}

.validEmailIcon,
.validEmailIconRank {
    display: none !important;
    position: absolute;
    top: 1px;
    right: 3px;
    display: none;
    background-position: -377px 0px;
    width: 17px;
    height: 17px;
    background-color: #fff;
    padding: 16px;
}

.validEmailIconRank {
    background-color: unset;
}

#formEmail:focus+.validEmailIcon {
    background-color: rgb(230, 240, 249);
}

span.requiredFieldContainer {
    color: #ff4e53;
    padding: 3px;
}

.specializationContainer .whiteScrollBg,
.destinationContainer .whiteScrollBg {
    background-color: white;
    height: 100%;
    top: 0;
    width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid #d8d8d8;
    border-left: 1px solid #d8d8d8;
    border-bottom: 1px solid #d8d8d8;
    border-radius: 4px;
    position: absolute;
}

.specializationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover,
.specializationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:focus,
.destinationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover,
.destinationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:focus {
    background-color: unset;
}

.select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: rgb(239, 239, 239, 0.3) !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
    color: #ff4e53;
}

.specializationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__display,
.destinationContainer .select2-container--default .select2-selection--multiple .select2-selection__choice__display {
    padding-bottom: 0px;
    /* transform: translateY(-2px); */
    line-height: 13px;
}

.specializationContainer span.select2-search.select2-search--inline,
.destinationContainer span.select2-search.select2-search--inline {
    flex-basis: 100%;
    flex-shrink: 1000;
}

.specializationContainer .select2-container--default .select2-results__option--selected,
.destinationContainer .select2-container--default .select2-results__option--selected {
    position: relative;
}

.specializationContainer .select2-container--default .select2-results__option--selected::after,
.destinationContainer .select2-container--default .select2-results__option--selected::after {
    content: " ";
    display: attr(data-after);
    position: absolute;
    top: 30%;
    right: 11px;
    background-image: url("../../images/master_sprite_1.webp") !important;
    background-repeat: no-repeat;
    background-position: -66px -10px;
    width: 16px;
    height: 16px;
    padding-right: 9px;
}

.signupModal .admissionRadio {
    appearance: auto;
}

.signupModal .admissionRadio::after {
    content: none;
}

@supports (-webkit-touch-callout: none) {

    /* CSS specific to iOS devices */
    input:disabled {
        background-color: #fff !important;
        border: solid 1px #afa8a8 !important;
    }
}

/* rank predictor lead form css starts */
.predict__rank__form__section .signupModalRank {
    position: static;
    transform: none;
    width: 100%;
    animation: none;
    flex-direction: column;
}

.predict__rank__form__section .signupModalRank .form__main__content__rank {
    padding: 0;
    max-width: 100%;
}

.predict__rank__form__section .signupModalRank .closeDialog {
    display: none !important;
}

.predict__rank__form__section .signupModalRank .textDivHeading {
    font-size: 16px;
    max-width: 100%;
}

.predict__rank__form__section .modalFormStepOneRank.inputGrid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.predict__rank__form__section .signupModalRank .inputContainerField {
    max-width: 100%;
}

.predict__rank__form__section .signupModalRank .inputGrid .modalInputContainer {
    max-width: 100%;
}

.predict__rank__form__section .signupModalRank .inputGrid .inputOTPContainer {
    display: block;
}

.predict__rank__form__section .signupModalRank .modalFormStepOneRank.detailRow {
    align-items: flex-start;
}

.predict__rank__form__section .signupModalRank .modalFormStepOneRank.detailRow .disclaimer {
    font-size: 14px;
    font-weight: 500;
    padding-bottom: 0;
}

.predict__rank__form__section .signupModalRank .modalFormStepOneRank.detailRow .btn-navigate-form-step {
    max-width: 160px;
}

.predict__rank__form__section .signupModalRank .inputGrid {
    display: grid;
}

.predict__rank__form__section .errorMsgEmailRank,
.predictorFromSection p {
    top: 40px;
}

.predict__rank__form__section input#formMobileRank {
    padding-left: 10px;
}

/* rank predictor lead form css ends */

@media (max-width: 1023px) {

    /* rank predictor lead form css starts */
    .predict__rank__form__section .modalFormStepOneRank.inputGrid {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .predict__rank__form__section .signupModalRank .modalFormStepOneRank.detailRow .btn-navigate-form-step {
        max-width: 100%;
    }

    /* rank predictor lead form css ends */


    /* ----------- Isolate signup form css from style.css file for ci project ------------- */
    .desktopOnly {
        display: none !important;
    }

    /* .mobileOnly {
        display: block !important;
    } */

    /* .blueBgDiv {
        display: none;
    } */

    @keyframes slideIn {
        from {
            transform: translateY(100vh);
        }

        to {
            transform: translateY(0vh);
        }
    }

    .signupModal {
        max-width: 100%;
        padding: 0;
        top: unset;
        left: unset;
        transform: unset;
        position: fixed;
        animation: slideIn 1000ms ease-in-out;
        bottom: 0;
        flex-direction: column;
        max-height: 85vh;
        overflow-y: scroll;
    }

    .signupModal .desktop__tnc__checkbox {
        display: none;
    }

    .signupModal .form__main__content {
        padding: 20px;
        max-width: unset;
    }

    .signupModal .textDivHeading {
        font-size: 20px;
        font-weight: 500;
        line-height: 24px;
        max-width: 319px;
    }

    .signupModal .form__side__content {
        padding: 20px;
        max-width: 100%;
    }

    .signupModal .signupModalCommonHeader {
        height: auto;
        position: relative;
    }

    .signupModal .inputGrid {
        margin-top: 10px;
        gap: 16px;
        grid-template-columns: 1fr;
    }

    .signupModal .inputGrid .modalInputContainer {
        max-width: calc(100vw - 40px);
        height: 40px;
    }

    .signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection .select2-selection__rendered {
        padding-top: 5px;
    }

    .signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection .select2-selection__arrow b {
        top: 16px;
    }

    .signupModal .inputGrid .modalInputContainer input.inputContainerField::placeholder,
    .signupModal .inputGrid .modalInputContainer select.inputContainerField::placeholder {
        font-size: 13px;
        line-height: 1.85;
    }

    .signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv .flagIcon {
        top: 8px;
    }

    .signupModal .inputGrid .inputOTPContainer .sendOtpButton {
        top: 10px;
        font-size: 13px;
        text-decoration: none;
    }

    .signupModal .inputGrid .inputMobileContainer .inputContainerField {
        padding-left: 16px !important;
    }

    .signupModal .inputGrid .inputOTPContainer .otpTimer {
        top: 10px;
    }

    .signupModal .inputGrid .fullWidthContainer {
        max-width: 100% !important;
    }

    .signupModal .modalFormStepOne.detailRow {
        flex-direction: column;
        margin-top: 16px;
        align-items: center;
    }

    .signupModal .modalFormStepOne.detailRow .termsAndLogin .termsAndConditionText {
        line-height: 1.33;
    }

    .signupModal .modalFormStepOne.detailRow .termsAndLogin .termsAndConditionText a {
        line-height: 1.33;
        display: block;
    }

    .signupModal .modalFormStepOne.detailRow .accountExistsText {
        font-size: 14px;
        line-height: 1.14;
    }

    .signupModal .modalFormStepOne.detailRow .accountExistsText a {
        font-weight: 600;
    }

    .signupModal .examScoreContainer .enterScorePrompt {
        font-size: 14px;
    }

    .signupModal .examScoreContainer .examScoreBoxList {
        gap: 10px;
        flex-wrap: wrap;
    }

    .signupModal .examScoreContainer .examScoreBoxList .examScoreBox {
        max-width: 143px;
        max-height: 40px;
    }

    .signupModal .distanceCourseContainer {
        gap: 6px;
    }

    .signupModal .distanceCourseContainer .switch {
        width: 97px;
    }

    .signupModal .distanceCourseContainer .distanceCourseText {
        font-size: 14px;
        line-height: 2;
    }

    .signupModal .distanceCourseContainer .slider:before {
        width: 50px;
    }

    .signupModal .distanceCourseContainer input:checked+.slider:before {
        -webkit-transform: translateX(46px);
        -ms-transform: translateX(46px);
        transform: translateX(46px);
    }

    .signupModal .eduBudgetContainer,
    .signupModal .distanceCourseContainer {
        margin-top: 0px;
    }

    .signupModal .inputGrid.modalFormStepTwo {
        row-gap: 10px;
    }

    .signupModal .detailRow:not(.modalFormStepOne) {
        gap: 10px;
    }

    .signupModal .detailRow:not(.modalFormStepOne) .btn-navigate-form-step {
        flex: 1;
    }

    .signupModal .recommendedCollegeContainer {
        border-bottom: 1px solid #d8d8d8;
        padding-bottom: 10px;
        align-items: flex-start;
    }

    .signupModal .recommendedCollegeContainer:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .signupModal .recommendedCollegeContainer::before {
        margin-top: 15px;
        margin-right: 2px;
    }

    .signupModal .recommendedCollegeContainer .collegeItemCheckbox {
        top: 50%;
        transform: translateY(calc(-50% - 5px));
        position: absolute;
        right: 0px;
    }

    .signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemHeading {
        font-size: 14px;
        font-weight: 600;
        line-height: 1.29;
        margin-bottom: 5px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        max-height: 36px;
        max-width: calc(100% - 25px);
        flex-grow: 1;
        flex-basis: 100%;
        /* max-width: unset; */
    }

    /* .signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating {
        flex-basis: 100%;
        margin-top: 5px;
    } */

    .signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemFees:after {
        content: '|';
        display: inline-block;
        margin-right: 5px;
        color: #787878 !important;
    }

    .signupModal .recommendedCollegeContainer .collegeItemDetails .collegeItemRating::before {
        content: none;
    }

    .signupModal .educationDetailsContainer .educationLabel {
        font-size: 14px;
        line-height: 1.71;
    }

    .signupModal .educationDetailsContainer .educationDetailsRow {
        flex-direction: column;
        height: initial;
        gap: 10px;
    }

    .signupModal .educationDetailsContainer .educationDetailsRow:nth-of-type(2) {
        margin-top: 10px;
    }

    .signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem {
        max-width: unset !important;
        height: 40px !important;
        flex-basis: unset !important;
    }

    .signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem:nth-child(3n+2) {
        background-position: 95% 15px !important;
    }

    /* Styles to fix select2 in signup screens Start */
    .signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv {
        max-width: 90px;
    }

    .signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv .flagIcon {
        left: 10px;
    }

    .signupModal .inputGrid .inputMobileContainer .mobileContainerCodeDiv .select2-selection__rendered {
        padding-left: 43px !important;
    }

    .signupModal .inputGrid .modalInputContainer .select2-container .selection .select2-selection .select2-selection__rendered {
        height: 40px;
    }

    /* Styles to fix select2 in signup screens End*/
    .signupModal .detailRow {
        margin-top: 16px;
    }

    .finalScreen {
        margin: 127px 0px;
    }

    .finalScreen p:first-of-type {
        font-size: 20px;
        line-height: 30px;
    }

    .finalScreen p:last-of-type {
        font-size: 15px;
    }

    .signupModal .educationDetailsContainer .educationDetailsRow .educationRowItem:nth-child(3n+3) {
        padding-left: 16px;
    }
}

/*# sourceMappingURL=detailedSignupScreen.css.map */