
html {
  box-sizing: border-box;
  -ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.h1_tooltip {
  position: relative;
}

.h1_tooltip .tooltiptext {
  font-size: 14px;
  line-height: 20px;
  visibility: hidden;
  width: 320px;
  background-color: #000;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 7px 9px;
  position: absolute;
  z-index: 4;
  left: 10%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  margin-top: -5px;
}

.h1_tooltip .tooltiptext:before {
  content: "";
  position: absolute;
  top: -10px;
  left: 12%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent #000 transparent;
}

.h1_tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

a.lead-login {
  cursor: pointer;
}

p.forPopup.signupOption {
  cursor: pointer;
}

/* for preview backend - side menu */
.skin-blue .sidebar a i.fa {
    color: #b8c7ce !important;
}

.authorAndDate .authorName {
  font-size: 14px;
  font-weight: 500;
  padding: 20px;
  padding-left: 16px;
  line-height: 1.43;
  color: #282828;
  text-transform: none;
}

/* college category listing page navigation bar*/
.collegeCategoryType {
  padding: 16px 10px;
  padding-bottom: 8px;
  height: 58px;
  border: var(--border-line);
  background: var(--color-white);
  margin-top: 20px;
  margin-bottom: -10px;
  border-radius: 4px;
  color: var(--primary-font-color);
  position: relative;
}

.pageRedirectionLinks .btn_right, .pageRedirectionLinks .btn_left {
  position: absolute;
  width: 48px;
  height: 44px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 3px;
  cursor: pointer;
}

.pageRedirectionLinks .btn_right {
  right: 0;
}

.collegeCategoryType ul {
  margin: 0;
  padding: 0;
  white-space: nowrap;
  overflow: auto;
}

.collegeCategoryType ul li.activeLink {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
    font-weight: 500;
}

.collegeCategoryType ul::-webkit-scrollbar {
  display: none;  /* Hides the scrollbar */
}

.collegeCategoryType ul li {
    font-size: 14px;
    line-height: 20px;
    margin: 0 10px;
    list-style-type: none;
    display: inline-block;
    padding-bottom: 2px;
    color: var(--primary-font-color);
    cursor: pointer;
}

/* end of h1 tooltip */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container,
  .container-sm {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container,
  .container-sm,
  .container-md {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container,
  .container-sm,
  .container-md,
  .container-lg {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl {
    max-width: 1140px;
  }
}

.row {
  display: -webkit-box;
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}


.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-auto,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-sm-auto,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-md-auto,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-lg-auto,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  -webkit-box-flex: 1;
  flex-grow: 1;
  min-width: 0;
  max-width: 100%;
}

.col-auto {
  -webkit-box-flex: 0;
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-1 {
  -webkit-box-flex: 0;
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.col-2 {
  -webkit-box-flex: 0;
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col-3 {
  -webkit-box-flex: 0;
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  -webkit-box-flex: 0;
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-5 {
  -webkit-box-flex: 0;
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.col-6 {
  -webkit-box-flex: 0;
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  -webkit-box-flex: 0;
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-8 {
  -webkit-box-flex: 0;
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.col-9 {
  -webkit-box-flex: 0;
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  -webkit-box-flex: 0;
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.col-11 {
  -webkit-box-flex: 0;
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.col-12 {
  -webkit-box-flex: 0;
  flex: 0 0 100%;
  max-width: 100%;
}

@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    -webkit-box-flex: 1;
    flex-grow: 1;
    min-width: 0;
    max-width: 100%;
  }

  .row-cols-md-1 > * {
    -webkit-box-flex: 0;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .row-cols-md-2 > * {
    -webkit-box-flex: 0;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .row-cols-md-3 > * {
    -webkit-box-flex: 0;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .row-cols-md-4 > * {
    -webkit-box-flex: 0;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .row-cols-md-5 > * {
    -webkit-box-flex: 0;
    flex: 0 0 20%;
    max-width: 20%;
  }

  .row-cols-md-6 > * {
    -webkit-box-flex: 0;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-md-auto {
    -webkit-box-flex: 0;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-md-1 {
    -webkit-box-flex: 0;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-md-2 {
    -webkit-box-flex: 0;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-md-3 {
    -webkit-box-flex: 0;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-md-4 {
    -webkit-box-flex: 0;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-md-5 {
    -webkit-box-flex: 0;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-md-6 {
    -webkit-box-flex: 0;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-md-7 {
    -webkit-box-flex: 0;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-md-8 {
    -webkit-box-flex: 0;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-md-9 {
    -webkit-box-flex: 0;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-md-10 {
    -webkit-box-flex: 0;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-md-11 {
    -webkit-box-flex: 0;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-md-12 {
    -webkit-box-flex: 0;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: -webkit-box !important;
  display: flex !important;
}

.d-inline-flex {
  display: -webkit-inline-box !important;
  display: inline-flex !important;
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: -webkit-box !important;
    display: flex !important;
  }

  .d-md-inline-flex {
    display: -webkit-inline-box !important;
    display: inline-flex !important;
  }
}

.flex-row {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
  flex-direction: row !important;
}

.flex-column {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  flex-direction: column !important;
}

.flex-row-reverse {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: reverse !important;
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  -webkit-box-flex: 1 !important;
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  -webkit-box-flex: 0 !important;
  flex-grow: 0 !important;
}

.flex-grow-1 {
  -webkit-box-flex: 1 !important;
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.justify-content-start {
  -webkit-box-pack: start !important;
  justify-content: flex-start !important;
}

.justify-content-end {
  -webkit-box-pack: end !important;
  justify-content: flex-end !important;
}

.justify-content-center {
  -webkit-box-pack: center !important;
  justify-content: center !important;
}

.justify-content-between {
  -webkit-box-pack: justify !important;
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  -webkit-box-align: start !important;
  align-items: flex-start !important;
}

.align-items-end {
  -webkit-box-align: end !important;
  align-items: flex-end !important;
}

.align-items-center {
  -webkit-box-align: center !important;
  align-items: center !important;
}

.align-items-baseline {
  -webkit-box-align: baseline !important;
  align-items: baseline !important;
}

.align-items-stretch {
  -webkit-box-align: stretch !important;
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 3rem !important;
}

.m-n1 {
  margin: -0.25rem !important;
}

.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}

.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}

.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}

.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important;
}

.m-n2 {
  margin: -0.5rem !important;
}

.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important;
}

.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important;
}

.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important;
}

.ml-n2,
.mx-n2 {
  margin-left: -0.5rem !important;
}

.m-n3 {
  margin: -1rem !important;
}

.mt-n3,
.my-n3 {
  margin-top: -1rem !important;
}

.mr-n3,
.mx-n3 {
  margin-right: -1rem !important;
}

.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important;
}

.ml-n3,
.mx-n3 {
  margin-left: -1rem !important;
}

.m-n4 {
  margin: -1.5rem !important;
}

.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important;
}

.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important;
}

.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important;
}

.ml-n4,
.mx-n4 {
  margin-left: -1.5rem !important;
}

.m-n5 {
  margin: -3rem !important;
}

.mt-n5,
.my-n5 {
  margin-top: -3rem !important;
}

.mr-n5,
.mx-n5 {
  margin-right: -3rem !important;
}

.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important;
}

.ml-n5,
.mx-n5 {
  margin-left: -3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    background: #f3f2ef;
    /* max-width: 1835px; */
}



a {
  color: #3d8ff2;
  text-decoration: none;
}

a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

button {
  cursor: pointer;
  outline: none;
}

button:disabled {
  pointer-events: none;
}

.text-center {
  text-align: center;
}

.pb-8 {
  padding-bottom: 8px;
}

:root {
  --font-bold: 700;
  --font-semibold: 600;
  --font-500: 500;
  --fontsize-14: 14px;
  --fontsize-15: 15px;
  --primary-font-color: #333333;
  --color-red: #ff4e53;
  --color-white: #ffffff;
  --anchor-textclr: #3d8ff2;
  --transition: 0.2s ease;
  --border-line: 1px solid #d8d8d8;
  --footer-bg: #273553;
  --topheader-bg: #0966c2;
}

input[type="checkbox"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #dedfdf;
  width: 16px;
  height: 16px;
  display: inline-block;
  position: relative;
  cursor: pointer;
}

input[type="checkbox"].inputChecked {
  background: var(--color-red);
  border: 1px solid var(--color-red);
}

input[type="checkbox"]:focus {
  outline: none;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-sizing: border-box;
  width: 100%;
  background-image: url(/yas/images/select-angle.png?9962fd8a0aedc3b373e4c9d4a758f3db) !important;
  background-repeat: no-repeat !important;
  background-position: 95% 19px !important;
  color: var(--primary-font-color);
}

input[type="checkbox"].inputChecked:checked:after,
input[type="checkbox"]:checked:after {
  content: "";
  display: block;
  position: absolute;
  top: 1px;
  left: 5px;
  width: 5px;
  height: 10px;
  background: var(--color-red);
  border-style: solid;
  border-color: var(--color-white);
  -webkit-border-image: initial;
  -o-border-image: initial;
  border-image: initial;
  border-width: 0 2px 2px 0;
  opacity: 1;
  -webkit-transform: scale(1) rotate(45deg);
  transform: scale(1) rotate(45deg);
  -webkit-transition: 0.2s ease;
  transition: 0.2s ease;
  -webkit-transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms,
    -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
  transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms,
    -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
  transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms,
    transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
  transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms,
    transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms,
    -webkit-transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
}

input[type="checkbox"]:checked {
  background: var(--color-red);
  border: 1px solid var(--color-red);
}

li {
  font-size: 15px;
  line-height: 28px;
}

html {
  scroll-behavior: smooth;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

h2 {
  font-weight: var(--font-semibold);
  font-size: 18px;
}
h3{
  font-size: 16px;
}
.container {
  max-width: 1236px;
}

img {
  max-width: 100%;
  height: auto;
}

figure {
  margin: 0;
}

figcaption {
  text-align: center;
  padding: 15px 0;
  font-size: 15px;
}

.overflow-scroll {
  overflow: auto;
}

.overflow-scroll::-webkit-scrollbar {
  width: 5px;
}

.overflow-scroll::-webkit-scrollbar-thumb {
  background: #ccc;
}

.overflow-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.table-responsive {
  overflow: auto;
  border-radius: 4px;
}

.no_padding {
  padding: 0;
}

.no_margin {
  margin: 0;
}

.mobileOnly {
  display: none !important;
}

.display_none {
  display: none;
}

table {
  border-spacing: 0;
  width: 100%;
  color: #333;
  border: 0.2px solid #eaeaea;
  border-bottom: 0;
  border-collapse: collapse;
}

.inputSection .autoComplete_list {
  max-width: 100%;
  margin: 0;
  padding: 0;
  background: var(--color-white);
  border: 1px solid #ccc;
}

.autoComplete_list li.no_result {
  list-style-type: none;
}

.inputSection .autoComplete_result {
  margin: 0;
  border-radius: 0;
  font-size: 15px;
}

table th {
  background: #f1f3f4;
  padding: 10px;
  font-size: 15px;
  line-height: 26px;
  border-right: 0.2px solid #eaeaea;
  border-bottom: 0.2px solid #eaeaea;
  text-align: center;
}

table li {
  text-align: left;
}

table thead tr {
  color: var(--primary-font-color);
  background: #f1f3f4;
  font-size: 14px;
  padding: 0;
  font-weight: 700;
  border: 0.2px solid #eaeaea;
  border-bottom: none;
  text-align: center;
}

table th:last-child {
  border-right: none;
}

table td {
  font-size: 14px;
  line-height: 24px;
  padding: 11px;
  border-right: 0.2px solid #eaeaea;
  border-bottom: 0.2px solid #eaeaea;
  min-width: 130px;
  text-align: center;
}

table td:last-child {
  border-right: none;
}

table td:first-child {
  border-right: 0.2px solid #eaeaea;
}

table tr td:last-child {
  border-right: 0.2px solid #eaeaea;
}

caption {
  color: #5a5cc3;
  margin-bottom: 10px;
}

.pageInfo {
  max-height: 200px;
  overflow: hidden;
  border-bottom: 10px solid #fff;
  border-radius: 4px 4px 0 0 !important;
}

.readMoreDiv,
.showMoreCourseWrap {
  position: relative;
}

.readMoreDiv:after,
.showMoreCourseWrap:after {
  content: "";
  background-image: -webkit-gradient(
    linear,
    left bottom,
    left top,
    from(#ffffff),
    to(rgba(255, 255, 255, 0))
  );
  background-image: linear-gradient(to top, #ffffff, rgba(255, 255, 255, 0));
  height: 50px;
  width: 100%;
  position: absolute;
  top: -50px;
  left: 0;
}

.breadcrumbDiv {
  padding: 6px 0;
  background: #f3f2ef;
}

.breadcrumbDiv ul {
  margin: 0;
  padding: 0;
}

.breadcrumbDiv ul li {
  font-size: 13px;
  display: inline-block;
  line-height: 20px;
  color: #989898;
}

.breadcrumbDiv ul li a {
  font-weight: var(--font-bold);
  text-decoration: none;
  color: var(--primary-font-color);
  position: relative;
  padding-right: 13px;
}

.breadcrumbDiv ul li a:hover {
  text-decoration: underline;
  color: #3d8ff2;
}

.breadcrumbDiv ul li a:after {
  content: "";
  background: url(../../images/master_sprite.webp);
  width: 10px;
  height: 12px;
  position: absolute;
  right: 0;
  bottom: 1px;
  background-position: 707px -150px;
}

.primaryBtn,
a.primaryBtn,
button.primaryBtn {
  display: inline-block;
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px 15px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  -webkit-transition: 0.2s ease;
  transition: 0.2s ease;
  outline: none;
}

.primaryBtn:hover,
a.primaryBtn:hover,
button.primaryBtn:hover {
  box-shadow: 0 0 15px #ccc;
}

.primaryBtn a {
  text-decoration: none;
}

.btn-block {
  width: 100%;
}

.readMoreDiv,
.showMoreCourseWrap {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background: #fafbfc;
  text-align: center;
  margin-bottom: 20px;
  border-radius: 0 0 4px 4px;
}

.readMoreInfo,
.showMoreCourseCard {
  font-weight: var(--font-semibold);
  color: var(--color-red);
  font-size: 15px;
  line-height: 26px;
  padding: 10px;
  cursor: pointer;
  text-decoration: none;
}

a.readMore,
.readMore {
  color: var(--color-red) !important;
  text-decoration: none;
  font-weight: var(--font-semibold);
}

a.ctaBtn,
button.ctaBtn,
.ctaBtn {
  border-radius: 3px;
  border: solid 1px var(--anchor-textclr);
  display: block;
  padding: 5px;
  text-align: center;
  background: var(--color-white);
  color: var(--anchor-textclr);
  font-size: 14px;
  line-height: 24px;
  font-weight: var(--font-500);
  width: 100%;
  margin-bottom: 10px;
  text-decoration: none;
  text-transform: uppercase;
  cursor: pointer;
}

a.ctaBtn:last-child,
button.ctaBtn:last-child,
.ctaBtn:last-child {
  margin-bottom: 0;
}

.authorName {
  color: var(--primary-font-color);
}

.pageLoaderDiv {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150px;
  height: 150px;
  border-radius: 50%;
}

.pageLoaderDiv .circle {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-top: 5px solid #262626;
  border-radius: 50%;
  animation: animate 2s infinite linear;
}

.pageLoaderDiv .loadText {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  font-size: 22px;
  font-weight: var(--font-semibold);
}

@keyframes animate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.pageLoader {
  position: fixed;
  top: 0;
  left: 0;
  background: rgb(255 255 255/71%);
  width: 100%;
  height: 100%;
  z-index: 5;
  display: none;
}

.rectangularAdDiv .adDisplay {
  max-height: 300px;
  max-width: 720px;
  margin: 0 auto;
  margin-bottom: 20px;
  overflow: hidden;
}

.horizontalRectangle {
  display: block;
  margin-bottom: 20px;
  padding-top: 1px;
}

.appendAdDiv {
  margin-top: 15px;
  position: relative;
}

.appendAdDiv:first-child::before {
  content: "-------- Advertisement --------";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 11px;
  text-align: center;
  line-height: 10px;
  color: #b7b7b7;
}

.horizontalRectangle .appendAdDiv {
  width: 728px;
  height: 90px;
  margin: 0 auto;
  margin-top: 15px;
}

.squareDiv {
  display: block;
  margin-bottom: 20px;
}

.squareDiv .appendAdDiv {
  width: 300px;
  height: 250px;
  margin: 0 auto;
  margin-top: 15px;
}

.verticleRectangle {
  display: block;
  margin-bottom: 20px;
}

.verticleRectangle .appendAdDiv {
  width: 300px;
  height: 600px;
  margin: 0 auto;
  margin-top: 15px;
}

.pageDescription,
.heroSection {
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(57%, #ffffff),
    to(#fffbec)
  );
  background: linear-gradient(90deg, #ffffff 57%, #fffbec 100%);
}


.advertise {
  font-size: 11px;
  text-align: center;
  line-height: 10px;
  width: 100%;
  color: #b7b7b7;
  margin-bottom: 5px;
}

.scrollToTop {
  display: none;
  position: fixed;
  bottom: 155px;
  right: 32px;
  z-index: 5;
  cursor: pointer;
  width: 60px;
  height: 60px;
  border-radius: 50px;
  /* background-image: url(../../images/scroll_to_top.webp); */
  background-position: 448px -395px;
  box-shadow: 0 4px 4px rgb(0 0 0/25%);
}

.lead-cta,
.lead-cta-college-filter-1,
.lead-cta-college-filter-2 {
  display: contents;
}

input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
  appearance: none !important;
}

input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
  appearance: none !important;
}

.engagementPanelCloseIcon {
  cursor: pointer;
}

#formEmail {
  position: relative;
  display: inline-block;
}

.domainExtention {
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #989898;
}

.doaminClassNameEmailContact .domainExtention {
  border-left: 1px solid #d8d8d8 !important;
  border: none;
}

.doaminClassNameEmailContact {
  position: relative;
}

@media (max-width: 1023px) {
  /*INP CSS Date 13/06/2024*/
    .filterSection, .pageFooter, #liveApplicationForm , .facilitySection, .faq_section, .courseTypeList, .reviewsSection, .photoGallery,
  .contsctUs, .customSlider, .breadcrumbDiv, .sideBarSection, .latestInfoSection, .newsSidebarSection, .quickLinks, .examInfoSlider,
  .articleSidebarSection, .chartContainer, .qnaWidget, .nextStorySection, .shareSection, .askUsQuestion, #vmap, .commentSection, .articleSection, .trendingArtilce {
      content-visibility: auto;
  }
  .nextStorySection{
      contain-intrinsic-size: auto 165px;
  }
  .shareSection{
      contain-intrinsic-size: auto 106px;
  }
  .examInfoSlider{
      contain-intrinsic-size: auto 320px;
  }
  .quickLinks{
      contain-intrinsic-size: auto 600px;
  }
  .commentSection{
      contain-intrinsic-size: auto 450px;
  }
  .trendingArtilce{
      contain-intrinsic-size: auto 215px;
  }
  .articleSection{
      contain-intrinsic-size: auto 460px;
  }
  .chartContainer{
      contain-intrinsic-size: auto 165px;
  }
  #vmap{
      contain-intrinsic-size: auto 360px;
  }
  .newsSidebarSection{
      contain-intrinsic-size: auto 540p;
  }
  .articleSidebarSection{
      contain-intrinsic-size: auto 540p;
  }
  .pageFooter{
      contain-intrinsic-size: auto 500px;
  }
  #liveApplicationForm {
      contain-intrinsic-size: auto 535px;
  }
  .faq_section{
      contain-intrinsic-size: auto 410px;
  }
  .reviewsSection{
      contain-intrinsic-size: auto 580px;
  }
  .photoGallery{
      contain-intrinsic-size: auto 230px;
  }
  .contsctUs{
      contain-intrinsic-size: auto 375px;
  }
  .customSlider{
      contain-intrinsic-size: auto 105px;
  }
  .filterSection{
      contain-intrinsic-size: auto 242px;
  }
  .courseTypeList{
      contain-intrinsic-size: auto 1000px;   
  }
  .facilitySection{
      contain-intrinsic-size: auto 320px;
  }
  .breadcrumbDiv{
      contain-intrinsic-size: auto 40px;
  }
  .sideBarSection{
      contain-intrinsic-size: auto 416px;
  }
  .latestInfoSection{
      contain-intrinsic-size: auto 412px;
  }


  /* */

  .myProfileIcon {
    background-position: -140px -627px !important;
    width: 17px !important;
    height: 20px !important;
  }

  a.myProfileOption {
    color: #282828;
  }

  .h1_tooltip .tooltiptext {
    left: 12%;
  }

  .scrollToTop {
    right: 26px;
    bottom: 164px;
  }

  .pageLoaderDiv {
    width: 100px;
    height: 100px;
  }

  .pageLoaderDiv .loadText {
    font-size: 18px;
  }

  .pageDescription,
  .heroSection {
    background: 0 0;
  }

  select {
    background-position: 95% 15px !important;
  }

  .row {
    margin: 0 -10px;
  }

  .pageInfo {
    max-height: 130px;
  }

  .container {
    padding: 0 10px;
  }

  .col-1,
  .col-2,
  .col-3,
  .col-4,
  .col-5,
  .col-6,
  .col-7,
  .col-8,
  .col-9,
  .col-10,
  .col-11,
  .col-12,
  .col,
  .col-auto,
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12,
  .col-sm,
  .col-sm-auto,
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12,
  .col-md,
  .col-md-auto,
  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12,
  .col-lg,
  .col-lg-auto,
  .col-xl-1,
  .col-xl-2,
  .col-xl-3,
  .col-xl-4,
  .col-xl-5,
  .col-xl-6,
  .col-xl-7,
  .col-xl-8,
  .col-xl-9,
  .col-xl-10,
  .col-xl-11,
  .col-xl-12,
  .col-xl,
  .col-xl-auto {
    padding: 0 10px;
  }

  .mobileOnly {
    display: block !important;
  }

  .desktopOnly {
    display: none !important;
  }

  .rectangularAdDiv .adDisplay {
    max-height: 300px;
    max-width: 720px;
    margin: 0 auto;
    margin-bottom: 20px;
    overflow: hidden;
  }

  .horizontalRectangle,
  .verticleRectangle,
  .squareDiv {
    display: block;
    margin-bottom: 20px;
  }

  .horizontalRectangle .appendAdDiv,
  .verticleRectangle .appendAdDiv {
    width: 300px;
    height: 250px;
    margin: 0 auto;
    margin-top: 15px;
  }
}

.spriteIcon {
  display: inline-block !important;
  background: url(../../images/master_sprite.webp);
  text-align: left;
  overflow: hidden;
}

.spriteIconTwo {
    display: inline-block !important;
    background: url(../../images/master_sprite_1.webp);
    text-align: left;
    overflow: hidden
}

.viewAllIcon {
  width: 77px;
  height: 76px;
  background-position: 358px -73px;
  margin-bottom: 10px;
  display: block !important;
  margin-bottom: 10px;
}

.headerLogo,
.footerLogo {
  width: 179px;
  height: 44px;
  background-position: 256px -72px;
}

.searchIcon {
  width: 21px;
  height: 23px;
  background-position: 535px -72px;
  vertical-align: middle;
}

.hambergerIcon {
  width: 27px;
  height: 20px;
  background-position: 476px -72px;
  margin-left: 0;
  vertical-align: middle;
}

.closeMenu,
.formCloseBtn {
  width: 40px;
  height: 40px;
  background-position: 423px -118px;
}

.cancelIcon {
  width: 24px;
  height: 24px;
  background-position: 535px -132px;
  cursor: pointer;
}

.closeIcon {
  width: 15px;
  height: 14px;
  margin-left: 8px;
  background-position: 652px -237px;
  vertical-align: middle;
  cursor: pointer;
}

.sortIcon {
  width: 24px;
  height: 24px;
  vertical-align: middle;
  background-position: 535px -193px;
  margin-right: 6px;
}

.filterIcon {
  width: 24px;
  height: 24px;
  vertical-align: middle;
  background-position: 535px -162px;
  margin-right: 6px;
}

.close_sortPopup {
  width: 24px;
  height: 24px;
  background-position: 535px -132px;
  margin-left: 8px;
  vertical-align: middle;
  cursor: pointer;
}

.closeLeadForm,
.closeLeadFormUtm,
.closeForm {
  width: 16px;
  height: 16px;
  background-position: 652px -236px;
  cursor: pointer;
}

.alarmIcon {
  width: 24px;
  height: 27px;
  margin-right: 8px;
  vertical-align: middle;
  background-position: 474px -166px;
}

.contactUsIcon {
  background-position: 358px -162px;
  width: 78px;
  height: 78px;
}

.full-star {
  width: 12px;
  height: 12px;
  background-position: 651px -173px;
  vertical-align: middle;
}

.half-start {
  width: 12px;
  height: 12px;
  background-position: 651px -195px;
  vertical-align: middle;
}

.empty-star {
  width: 12px;
  height: 12px;
  background-position: 651px -217px;
  vertical-align: middle;
}

.gmuLable {
  width: 34px;
  height: 34px;
  background: url(/images/clgProfileIcon.png?d172f0a71be89cc4333190e2c63f28a4)
    no-repeat;
  margin-right: 11px;
  vertical-align: middle;
  background-size: 100% 100%;
  border-radius: 50%;
}

.rightLst,
.leftLst,
.scrollLeft,
.scrollRight {
  border: none;
  width: 40px;
  height: 40px;
  background-position: 422px -72px;
  padding: 0;
  cursor: pointer;
}

.leftLst,
.scrollLeft {
  -webkit-transform: translate(0px, -50%) rotate(-180deg);
  transform: translate(0px, -50%) rotate(-180deg);
}

.flagIcon {
  width: 27px;
  height: 22px;
  background-position: 415px -165px;
  vertical-align: middle;
  margin-right: 5px;
}

.greyFbIcon {
  width: 26px;
  height: 26px;
  background-position: 475px -98px;
}

.greyTwitterIcon {
  width: 26px;
  height: 26px;
  background-position: 475px -132px;
}

.right_angle {
  width: 16px;
  height: 21px;
  background-position: 593px -71px;
  margin: 14px 0;
}

.left_angle {
  width: 16px;
  height: 21px;
  background-position: 593px -97px;
  margin: 14px 0;
}

.angle_left {
  width: 13px;
  height: 19px;
  background-position: 653px -131px;
  vertical-align: middle;
  margin-right: 5px;
}

.replyIcon {
  width: 22px;
  height: 18px;
  background-position: 533px -105px;
  vertical-align: middle;
}

.fbIcon {
  background-position: 476px -200px;
}

.twitterIcon {
  background-position: 476px -234px;
}

.instaIcon {
  background-position: 476px -268px;
}

.linkdIn {
  background-position: 476px -302px;
}

.youtubeIcon {
  background-position: 476px -364px;
}

.phoneIcon {
  width: 24px;
  height: 24px;
  background-position: 536px -246px;
  vertical-align: middle;
  margin-right: 7px;
}

.whiteMailIcon {
  width: 24px;
  height: 24px;
  background-position: 536px -219px;
  vertical-align: middle;
  margin-right: 7px;
}

.authorInfo .linkdIn {
  background-position: 476px -336px;
}

.caret {
  width: 12px;
  height: 10px;
  background-position: 707px -135px;
  margin-left: 4px;
  vertical-align: middle;
}

.pdfIcon {
  background-position: 343px -256px;
  width: 52px;
  height: 52px;
}

.whiteCaretIcon {
  width: 12px;
  height: 10px;
  background-position: 708px -72px;
  margin-left: 4px;
  vertical-align: middle;
}

.thankYouIcon {
  width: 123px;
  height: 123px;
  background-position: 255px -117px;
}

.listImage ul {
  margin: 0;
}

.listImage ul li,
ul.listImage li {
  position: relative;
  list-style-type: none;
}

.listImage ul li:before,
ul.listImage li:before {
  content: " ";
  background: url(../../images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -20px;
  top: 5px;
  background-position: 651px -71px;
}

.listImage ul li a,
ul.listImage li a {
  color: var(--primary-font-color);
  text-decoration: none;
}

.listImage ul li a:hover,
ul.listImage li a:hover {
  color: var(--anchor-textclr);
}

.topHeader {
  position: relative;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 12;
  height: 60px;
}

.topHeader {
  background: var(--topheader-bg);
  padding: 8px 0;
}

.topHeader .row {
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
}


ul.study-abroad-options {
  position: absolute;
  background: #fff;
  padding: 0;
  width: 150px;
  border: 1px solid #ccc;
  top: 50px;
  z-index: 1;
  box-shadow: 0 7px 34px rgba(0, 0, 0, 0.25);
  display: none;
}

ul.study-abroad-options li {
  display: block;
  padding: 5px 10px;
}

ul.study-abroad-options li a {
  color: #787878;
}

ul.study-abroad-options li a:hover {
  color: #3d8ff2;
}

.sa_dropdown {
  cursor: pointer;
}

.sa_dropdown:hover ul.study-abroad-options {
  display: block;
}


.sub-header-drop {
  top: 40px !important;
  z-index: 2 !important;
}

.sub-header-drop li {
  line-height: 2px !important;
}

ul#resource-dropdown {
  width: auto;
  left: auto;
  right: 0;
}

.greyBg {
  position: fixed;
  width: 100%;
  height: calc(100% - 101px);
  left: 0;
  bottom: 0;
  background: rgba(51, 51, 51, 0.4);
  z-index: 1;
  display: none;
}

.navigation_header_dropdown:hover p.greyBg {
  display: block;
}

ul.dropdown-menu li.hoverbg {
  background: var(--color-red);
  color: var(--color-white);
  font-weight: var(--font-bold);
}

ul.dropdown-menu li.hoverbg a {
  color: var(--color-white);
  line-height: 20px;
}

ul.dropdown-menu.multi-column .row {
  margin: 0;
}

ul.dropdown-menu .row .no_padding,
#ComputerApplication-blog .row {
  padding: 0;
}

.slider-menu,
.mobileMenu {
  display: none;
}

.advanceSearch {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background: #f9f9f9;
  z-index: 12;
  display: none;
}

.searchSection {
  max-width: 730px;
  margin: 60px auto;
}

.searchSection .search_heading {
  margin: 0;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 20px;
}

/* .searchSection .tabButtons {
    display: -webkit-box;
    display: flex;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    justify-content: space-between;
    padding: 0;
    margin: 0
} */

/* .searchSection .tabButtons li {
    list-style-type: none
}

.searchSection .tabButtons .tab-link {
  flex-basis: 33%;
  text-align: center;
  padding: 10px 15px;
  background-color: #f1f1f1;
  border-radius: 2px;
  cursor: pointer;
  font-weight: 700;
}

.searchSection .tabButtons .tab-link.current {
    background: var(--color-red);
    color: var(--color-white)
} */

.tab-content {
  display: none;
}

.tab-content.current {
  display: block;
}

.tab-content input {
  width: 100%;
  padding: 5px 10px;
  font-size: 15px;
  height: 40px;
  outline: none;
}


/* widgets author name css change */

.widgetAUthorName {
    position: relative;
    z-index: 10;
    color: #989898 !important;
    font-weight: 400 !important;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    padding-left: 16px;
    font-size: 14px !important;
    padding-top: 0px;
    margin-bottom: 3px;
}

.downloadPdfSection {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.downloadPdfCard {
  padding: 15px 10px;
  margin-bottom: 15px;
  border: var(--border-line);
  border-radius: 3px;
}

.downloadPdfCard:last-child {
  margin-bottom: 0;
}

.downloadPdfCard .pdfIcon {
  margin-right: 10px;
  vertical-align: middle;
}

.downloadPdfCard p {
  font-size: 14px;
  line-height: 24px;
  font-weight: var(--font-semibold);
  display: inline-block;
}

.downloadPdfCard .row {
  margin: 0;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
}

.MultiCarousel {
  overflow: hidden;
  width: 100%;
  position: relative;
}

.MultiCarousel .MultiCarousel-inner {
  -webkit-transition: 1s ease all;
  transition: 1s ease all;
}

.MultiCarousel .MultiCarousel-inner .item {
  float: left;
  padding-right: 20px;
}

.MultiCarousel .leftLst,
.MultiCarousel .rightLst,
.scrollRight,
.scrollLeft {
  position: absolute;
  border-radius: 50%;
  top: calc(50% - 20px);
  outline: none;
  cursor: pointer;
  z-index: 2;
}

.MultiCarousel .leftLst,
.scrollLeft {
  left: 0;
}

.MultiCarousel .rightLst,
.scrollRight {
  right: 0;
}

.MultiCarousel .leftLst.over,
.MultiCarousel .rightLst.over {
  pointer-events: none;
  opacity: 0;
}

.over {
  pointer-events: none;
  opacity: 0;
}

/* .fixedQuickLinksDiv {
    position: fixed;
    top: 80px;
    z-index: 1;
    width: 100%;
    max-width: 382px
} */

.removeFixedQuickLink {
  height: 10px;
}

.submit-lead-form:disabled {
  background: #ff4e53bd;
}

.leadFormDiv .error {
  border: 1px solid red !important;
}

.has-error > input {
  border: 1px solid red !important;
}

.has-error > textarea {
  border: 1px solid red !important;
}

.formField {
  position: relative;
}

.thankYouMsg {
  text-align: center;
  background: var(--color-white);
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 3px;
}

.thankYouMsg .thankYouText {
  font-size: 24px;
  line-height: 36px;
  color: var(--primary-font-color);
  padding: 0;
  padding-top: 20px;
}

.leadFormContainer,
.writeAnswerForm {
  background: rgba(51, 51, 51, 0.6);
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  overflow: auto;
  display: none;
}

.closeLeadForm,
.closeLeadFormUtm {
  position: absolute;
  right: 40px;
  top: 40px;
}

.leadFormDiv,
.writeAnswerDiv {
  max-width: 794px;
  margin: 40px auto;
  position: relative;
  border-radius: 4px;
  background: var(--color-white);
  padding: 25px;
  margin-top: 125px;
}

.leadFormDiv .formSumbitBtn,
.writeAnswerDiv .formSumbitBtn {
  text-align: right;
}

.leadFormDiv .col-md-6,
.writeAnswerDiv .col-md-6 {
  padding: 0 10px;
}

.leadFormDiv .mobileNumber.row,
.writeAnswerDiv .mobileNumber.row {
  margin: 0;
}

.leadFormDiv p,
.writeAnswerDiv p {
  color: #787878;
  font-size: 16px;
  line-height: 28px;
  padding-bottom: 20px;
}

.leadFormDiv .headingText,
.writeAnswerDiv .headingText {
  font-size: 24px;
  line-height: 24px;
  padding-bottom: 10px;
  font-weight: var(--font-500);
  color: var(--primary-font-color);
  text-transform: uppercase;
}

.leadFormDiv .primaryBtn,
.writeAnswerDiv .primaryBtn {
  font-size: 14px;
  line-height: 24px;
}

.leadFormDiv .form-group,
.writeAnswerDiv .form-group {
  padding-bottom: 20px;
  position: relative;
}

.leadFormDiv input,
.leadFormDiv select,
.leadFormDiv textarea,
.leadFormDiv .dialCodeDiv,
.writeAnswerDiv input,
.writeAnswerDiv select,
.writeAnswerDiv textarea,
.writeAnswerDiv .dialCodeDiv {
  padding: 11px 12px;
  padding-left: 41px;
  border-radius: 4px;
  border: var(--border-line);
  outline: none;
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  background: var(--color-white);
  min-height: 48px;
  color: var(--primary-font-color);
}

.leadFormDiv input::-webkit-input-placeholder,
.leadFormDiv select::-webkit-input-placeholder,
.leadFormDiv textarea::-webkit-input-placeholder,
.leadFormDiv .dialCodeDiv::-webkit-input-placeholder,
.writeAnswerDiv input::-webkit-input-placeholder,
.writeAnswerDiv select::-webkit-input-placeholder,
.writeAnswerDiv textarea::-webkit-input-placeholder,
.writeAnswerDiv .dialCodeDiv::-webkit-input-placeholder {
  color: #989898;
}

.leadFormDiv input::-moz-placeholder,
.leadFormDiv select::-moz-placeholder,
.leadFormDiv textarea::-moz-placeholder,
.leadFormDiv .dialCodeDiv::-moz-placeholder,
.writeAnswerDiv input::-moz-placeholder,
.writeAnswerDiv select::-moz-placeholder,
.writeAnswerDiv textarea::-moz-placeholder,
.writeAnswerDiv .dialCodeDiv::-moz-placeholder {
  color: #989898;
}

.leadFormDiv input:-ms-input-placeholder,
.leadFormDiv select:-ms-input-placeholder,
.leadFormDiv textarea:-ms-input-placeholder,
.leadFormDiv .dialCodeDiv:-ms-input-placeholder,
.writeAnswerDiv input:-ms-input-placeholder,
.writeAnswerDiv select:-ms-input-placeholder,
.writeAnswerDiv textarea:-ms-input-placeholder,
.writeAnswerDiv .dialCodeDiv:-ms-input-placeholder {
  color: #989898;
}

.leadFormDiv input::-ms-input-placeholder,
.leadFormDiv select::-ms-input-placeholder,
.leadFormDiv textarea::-ms-input-placeholder,
.leadFormDiv .dialCodeDiv::-ms-input-placeholder,
.writeAnswerDiv input::-ms-input-placeholder,
.writeAnswerDiv select::-ms-input-placeholder,
.writeAnswerDiv textarea::-ms-input-placeholder,
.writeAnswerDiv .dialCodeDiv::-ms-input-placeholder {
  color: #989898;
}

.leadFormDiv input::placeholder,
.leadFormDiv input .dialCode,
.leadFormDiv select::placeholder,
.leadFormDiv select .dialCode,
.leadFormDiv textarea::placeholder,
.leadFormDiv textarea .dialCode,
.leadFormDiv .dialCodeDiv::placeholder,
.leadFormDiv .dialCodeDiv .dialCode,
.writeAnswerDiv input::placeholder,
.writeAnswerDiv input .dialCode,
.writeAnswerDiv select::placeholder,
.writeAnswerDiv select .dialCode,
.writeAnswerDiv textarea::placeholder,
.writeAnswerDiv textarea .dialCode,
.writeAnswerDiv .dialCodeDiv::placeholder,
.writeAnswerDiv .dialCodeDiv .dialCode {
  color: #989898;
}

.leadFormDiv select::-webkit-input-placeholder,
.writeAnswerDiv select::-webkit-input-placeholder {
  color: #989898;
}

.leadFormDiv select::-moz-placeholder,
.writeAnswerDiv select::-moz-placeholder {
  color: #989898;
}

.leadFormDiv select:-ms-input-placeholder,
.writeAnswerDiv select:-ms-input-placeholder {
  color: #989898;
}

.leadFormDiv select::-ms-input-placeholder,
.writeAnswerDiv select::-ms-input-placeholder {
  color: #989898;
}

.leadFormDiv select::placeholder,
.writeAnswerDiv select::placeholder {
  color: #989898;
}

.leadFormDiv select:first-child,
.writeAnswerDiv select:first-child {
  color: #989898;
}

.leadFormDiv .countryCode,
.writeAnswerDiv .countryCode {
  flex-basis: 101px;
  position: relative;
}

.leadFormDiv .countryCode input,
.leadFormDiv .countryCode .dialCodeDiv,
.writeAnswerDiv .countryCode input,
.writeAnswerDiv .countryCode .dialCodeDiv {
  background: #eaeaea;
  border-radius: 4px 0 0 4px;
  border-right: var(--border-line);
  text-align: center;
  padding-left: 12px;
}

.leadFormDiv .numberInput,
.writeAnswerDiv .numberInput {
  flex-basis: calc(100% - 101px);
}

.leadFormDiv .numberInput input,
.writeAnswerDiv .numberInput input {
  border-left: none;
  border-radius: 0 4px 4px 0;
  padding-left: 12px;
}

.writeAnswerDiv .closeLeadForm {
  right: 25px;
  top: 25px;
}

.leadFormDiv .select2-container {
  width: 100% !important;
}

.leadFormDiv
  .select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  padding: 11px 12px;
  padding-left: 41px;
  border-radius: 4px;
  border: var(--border-line);
  outline: none;
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  background: var(--color-white);
  min-height: 48px;
  background-image: url(/yas/images/select-angle.png?9962fd8a0aedc3b373e4c9d4a758f3db) !important;
  background-repeat: no-repeat !important;
  background-position: 95% 19px !important;
}

.leadFormDiv
  .select2-container--default
  .select2-selection--single
  .select2-selection__clear,
.leadFormDiv span.select2-selection__arrow {
  display: none;
}

.leadFormDiv .select2-container--default .select2-selection--single {
  border: none;
  height: auto;
  outline: none;
}

.leadFormDiv input.select2-search__field {
  height: 35px;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border: var(--border-line);
  height: 35px;
  outline: none;
}

.writeAnswerDiv {
  max-width: 500px;
  padding: 25px;
}

.writeAnswerDiv .primaryBtn {
  width: 100%;
  text-align: center;
}

.writeAnswerDiv textarea {
  padding-left: 12px;
}

.writeAnswerDiv .headingText {
  padding-bottom: 15px;
}

.checkbox-group {
  padding-bottom: 10px;
}

.checkbox-group input {
  width: 16px;
  height: 16px;
  min-height: auto;
  padding: 0;
  margin: 0;
  border-radius: 0;
  vertical-align: middle;
  cursor: pointer;
  margin-right: 12px;
}

.checkbox-group label {
  font-size: 13px;
  line-height: 28px;
  color: #787878;
}

.numberInputs {
  padding-bottom: 30px;
}

.numberInputs input {
  max-width: 80px;
  margin-right: 18px;
  border-radius: var(--border-line);
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  padding: 12px;
  color: #989898;
  padding-left: 12px;
}

.numberInputs input:last-child {
  margin-right: 0;
}

.optSection .row {
  margin: 0;
  -webkit-box-align: center;
  align-items: center;
}

.optSection .primaryBtn {
  margin-right: 20px;
}

.optSection a {
  color: var(--color-red);
  text-decoration: none;
  font-size: 14px;
  line-height: 24px;
}

.userName input {
  position: relative;
}

.userIcon,
.mailIcon,
.locationIcon,
.bookIcon,
.capIcon,
.userIconMultiple {
  width: 20px;
  height: 19px;
  position: absolute;
  z-index: 1;
  left: 12px;
  top: 14px;
}

.userIcon {
  background-position: 595px -238px;
}

.mailIcon {
  background-position: 594px -262px;
}

.locationIcon {
  background-position: 594px -311px;
}

.bookIcon {
  background-position: 595px -285px;
}

.capIcon {
  background-position: 595px -333px;
}

.userIconMultiple {
  background-position: 536px -418px;
}

.help-block {
  color: var(--color-red);
  font-size: 12px;
  padding-top: 5px;
}

.discussionForum .moreAnswer a:hover,
.discussionForum .writeAnswer a:hover {
  color: var(--color-white);
}

.discussionForum a {
  color: var(--primary-font-color);
}

.discussionForum a:hover {
  color: var(--anchor-textclr);
}

.discussionForum .moreAnswer:hover {
  color: var(--color-white);
}

.discussionForum .writeAnswer:hover {
  color: var(--color-red);
}


#otpResponseText {
  padding-top: 6px;
  padding-bottom: 0;
  color: var(--color-red);
}

.formHeadingDiv.row {
  margin: 0;
  -webkit-box-align: center;
  align-items: center;
  padding-bottom: 20px;
}

.formHeadingDiv .formImg {
  flex-basis: 100px;
  margin-right: 20px;
  box-shadow: rgb(0 0 0/12%) 0 1px 3px, rgb(0 0 0/24%) 0 1px 2px;
  border-radius: 50%;
  min-height: 100px;
  display: flex;
  align-items: center;
}

.formHeadingDiv .formImg img {
  max-width: 62px;
  max-height: 62px;
  display: block;
  margin: 0 auto;
  border-radius: 50%;
}

.formHeadingDiv .formHeading {
  flex-basis: calc(100% - 120px);
}

.formHeadingDiv .headingText {
  padding-right: 20px;
}

.formHeadingDiv p {
  padding: 0;
}

.examInfo ul li,
.pageInfo ul li,
.pageDescription ul li,
.browseByCategory ul li,
.articleInfo ul li {
  position: relative;
  list-style-type: none;
}

.examInfo ul li:before,
.pageInfo ul li:before,
.browseByCategory ul li:before,
.articleInfo ul li::before {
  content: "";
  background: url(../../images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

.tagsDiv ul li::before {
  display: none;
}

.page-header {
  height: 100px;
}

.dynamicCard {
  padding: 20px;
  border-radius: 4px;
  border: var(--border-line);
  margin-bottom: 20px;
  background: #dbebff;
}

.dynamicCard .row {
  margin: 0;
}

.dynamicCard.whiteBg {
  background: var(--color-white);
}

.dynamicCard.text-center {
  text-align: center;
}

.dynamicCard .dynamicCardHeading {
  font-size: 20px;
  line-height: 32px;
  padding: 0;
  padding-bottom: 10px;
  font-weight: 700;
  background: 0 0;
  margin: 0;
}

.dynamicCard p {
  font-size: 15px;
  line-height: 24px;
  padding-bottom: 20px;
}

.dynamicCard .dynamicCardText {
  flex-basis: calc(100% - 224px);
}

.dynamicCard .dynamicCardImage {
  flex-basis: 204px;
  margin-left: 20px;
}

.dynamicCard .dynamicCardImage img {
  height: 156px;
  display: block;
  margin: 0 auto;
}

.primaryBtn.btnCenter {
  display: block;
  margin: 0 auto;
  max-width: fit-content;
}

.withImage .primaryBtn,
.withoutImage .primaryBtn {
  margin: initial;
  display: inline-block;
}

.engagementPanel .headingText {
  text-transform: initial;
}

.whiteDownloadIcon,
.redDownloadIcon {
  width: 19px;
  height: 18px;
  background-position: 233px -353px !important;
  vertical-align: text-bottom;
  margin-left: 4px;
}

.applyWhiteIconCta {
  z-index: 1;
  width: 20px;
  height: 20px;
  background-position: -188px -593px;
  position: absolute;
  top: 127px;
  right: 62px;
}

.talkToExpert .applyWhiteIconCta {
  z-index: 1;
  width: 20px;
  height: 20px;
  background-position: -188px -593px;
  position: absolute;
  top: 127px;
  right: 240px;
}

@media (max-width: 1023px) {
  .applyWhiteIconCta {
    z-index: 1;
    width: 20px;
    height: 20px;
    background-position: -188px -593px;
    position: absolute;
    top: 18px;
    right: 47px;
  }

  .talkToExpert .applyWhiteIconCta {
    z-index: 1;
    width: 20px;
    height: 20px;
    background-position: -188px -593px;
    position: absolute;
    top: 18px;
    right: 47px;
  }

  .getSupport .applyNowCourse .applyWhiteIconCta {
    z-index: 1;
    width: 20px;
    height: 20px;
    background-position: -188px -593px;
    position: absolute;
    top: 18px;
    right: 222px !important;
  }

  .leadFormDiv .dialCodeDiv {
    max-height: 40px;
    min-height: 0 !important;
  }

  .leadFormDiv .mobileNumber.row {
    position: relative;
    margin-bottom: 15px !important;
  }

  .leadFormDiv .mobileNumber .otherCountryCode {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
  }

  .leadFormDiv .mobileNumber .iPhoneCountryCode {
    position: absolute;
    z-index: 2;
    top: 2px;
    left: 0;
  }

  .leadFormDiv .mobileNumber .numberInput {
    padding-left: 80px;
    flex-grow: 1;
  }

  .leadFormDiv .mobileNumber .numberInput .form-group {
    padding-bottom: 0;
  }

  .collegeCheckBox .clgInfo .sponsorFees {
    font-size: 13px;
  }

  .collegeCheckBox .clgName {
    font-size: 13px;
  }

  .sponsorFees span {
    color: #282828;
  }

  .userInputs .row.m-0 .checkbox-group {
    margin-bottom: 10px;
  }

  .leadFormDiv .userInputs .formSumbitBtn {
    margin-bottom: 10px;
  }

  .formHeadingDiv.row {
    padding-bottom: 12px;
  }

  .pageMask {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    overflow-y: hidden;
    z-index: 5;
  }

  .leadFormContainer {
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
  }

  .leadFormContainer {
    top: 3%;
    height: auto;
    width: 95%;
    left: 10px;
    overflow: hidden;
    border-radius: 4px;
    display: none;
  }

  .leadFormContainer .closeLeadFormContainer {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 8;
  }

  .leadFormContainer .leadFormDiv {
    margin-top: 1px;
    padding-bottom: 12px;
  }

  .engagementPanel .formHeading {
    display: none;
  }

    .engagementPanel .formHeading {
        display: none
    }

    span#select2-leadform-current_location-container {
        padding: 7px;
        padding-left: 42px;
        min-height: 40px;
        font-size: 13px;
        line-height: 24px
    }

    .quickLinks,
    .sidebarAds {
        position: static !important;
    }

    .commonHeroSection .primaryBtn {
        flex-basis: 50% !important;
        flex-grow: 1;
    }
}

.collegeCheckBox {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  padding: 0;
}

@media (max-width: 1023px) {
  .collegeCheckBox {
    margin-bottom: 0;
  }
}

.collegeCheckBox input {
  border-radius: 2px;
  background: #d8d8d8;
  border: 1px solid #d8d8d8;
}

.collegeCheckBox input:after {
  content: "";
  display: block;
  position: absolute;
  top: 1px;
  left: 5px;
  width: 5px;
  height: 10px;
  background: #d8d8d8;
  border-style: solid;
  border-color: var(--color-white);
  border-width: 0 2px 2px 0;
  transform: scale(1) rotate(45deg);
}

.collegeCheckBoxCard {
  flex-basis: calc(100% - 30px);
  cursor: pointer;
  padding: 20px;
  border: var(--border-line);
  border-radius: 4px;
}

@media (max-width: 1023px) {
  .collegeCheckBoxCard {
    padding: 10px;
    align-items: center;
    flex-basis: calc(100% - 20px);
  }
}

.collegeCheckBoxCard .clgLogo {
  flex-basis: 72px;
  margin-right: 20px;
}

@media (max-width: 1023px) {
  .collegeCheckBoxCard .clgLogo {
    margin-right: 10px;
  }
}

.collegeCheckBoxCard .clgLogo img {
  display: block;
}

.collegeCheckBoxCard .clgInfo {
  flex-basis: calc(100% - 72px - 20px);
}

@media (max-width: 1023px) {
  .collegeCheckBoxCard .clgInfo {
    flex-basis: calc(100% - 72px - 10px);
  }
}

.collegeCheckBoxCard p {
  padding: 0;
  line-height: 24px;
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-font-color);
}

.collegeCheckBoxCard p:first-child {
  padding-bottom: 4px;
  font-size: 16px;
}

@media (max-width: 1023px) {
  .collegeCheckBoxCard p:first-child {
    font-size: 14px;
  }
}

.collegeCheckBoxCard p span {
  color: #989898;
  font-weight: 400;
}

.leadFormContainer .closeLeadForm,
.closeLeadFormUtm {
  width: 17px;
  height: 17px;
  background-position: 653px -334px;
}

.siq_bR {
  bottom: 70px !important;
  right: 20px !important;
}


.ui-state-active,
.ui-widget-content .ui-state-active {
  background: 0 0 !important;
  border: none !important;
  color: var(--primary-font-color) !important;
}
.ui-menu .ui-state-focus,
.ui-menu .ui-state-active {
  margin: 0 !important;
}

ul.ui-menu.ui-widget.ui-widget-content.ui-autocomplete.ui-front.category {
  border: 1px solid #d8d8d8;
  width: 100% !important;
  background: 0 0;
  left: 0 !important;
  margin-top: 20px;
  top: -21px !important;
  display: block !important;
  z-index: inherit;
}


.recentBlock {
  justify-content: space-between;
  width: 760px;
}

p#letterCount {
  margin-top: 15px;
}


.spriteIcon.rctIcon {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  background-position: -83px -557px;
}

.spriteIcon.starIcon {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  background-position: -40px -560px;
}

.selection {
  width: 100% !important;
  max-width: 480px;
  top: -20px !important;
}

/**************** Olympiad Page CSS ***************************/

#olympiadAutoComplete {
  height: 48px;
  font-size: 16px !important;
}

.selection.olympiadSearchResult {
  position: relative;
  top: 0 !important;
  width: 100% !important;
  max-width: 100%;
  margin-left: 16px;
  box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.olympiadSearchResult
  ul.ui-menu.ui-widget.ui-widget-content.ui-autocomplete.ui-front {
  top: 0 !important;
  margin-top: 0 !important;
}

.olympiadSearchResult .ui-menu-item {
  font-size: 16px;
  border-left: 3px solid transparent;
}

.olympiadSearchResult .ui-menu-item-wrapper {
  font-size: 16px;
}

.olympiadSearchResult .ui-menu-item:hover {
  background-color: #f5f5f58a;
  border-left: 3px solid #ff4353;
}

/**************** Olympiad Page CSS End ***********************/

.close-icone {
  height: 40px;
  width: 40px;
  position: absolute;
  right: 0;
  top: 0;
}

.backIcon {
  background-position: -115px -510px;
  height: 30px;
  width: 25px;
}

.leftArrow {
  height: 100% !important;
  width: 40px !important;
  top: 0 !important;
}

/* Login Signup OTP */
.otpSection #otpResend a {
  color: #ff4353;
}

/* Login Signup Logout */
.logOutOption:hover {
  text-decoration: none;
}

@media (max-width: 1023px) {
  .leadFormContainer .closeLeadForm {
    background: url(https://www.getmyuni.com/yas/images/closeform.png) no-repeat;
  }

  .siqico-close {
    display: flex !important;
  }

  .siq_bR {
    bottom: 0 !important;
    margin-bottom: 65px;
  }

  .xs-h50 {
    height: 50px !important;
  }

  .clgWithCourse h2.row {
    white-space: inherit;
  }

  .pageInfo {
    max-height: 250px;
  }

  .leftLst {
    -webkit-transform: translate(0px, -50%) rotate(-180deg);
    transform: translate(0px, 0%) rotate(-180deg);
  }

  .contactUs {
    position: fixed;
    bottom: 56px;
    left: 10px;
    z-index: 3;
  }

  .setAlarmDiv {
    padding: 10px;
    margin: 0 -10px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    margin-bottom: 10px;
    font-weight: var(--font-semibold);
  }

  .setAlarmDiv .primaryBtn {
    display: block;
    font-weight: var(--font-semibold);
    width: 100%;
  }

  .breadcrumbDiv {
    padding: 8px;
    background: var(--topheader-bg);
  }

  .breadcrumbDiv ul li {
    color: var(--color-white);
  }

  .breadcrumbDiv ul li a {
    color: var(--color-white);
    font-weight: var(--font-semibold);
  }

  .breadcrumbDiv ul li a:after {
    background-position: 707px -118px;
  }

  .breadcrumbDiv ul {
    white-space: nowrap;
    overflow: auto;
  }

  .blueBgDiv {
    background: var(--topheader-bg);
    width: 100%;
    height: 167px;
    margin-top: -1px;
  }

  .topHeader {
    padding: 8px 20px;
    position: relative;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 5;
  }

  .topHeader ul,
  .headerMegaMenu ul {
    display: none;
  }


  .page-header {
    height: 60px;
  }

  .closeMenu {
    position: absolute;
    right: 10px;
    top: 10px;
    transform: scale(0.8);
  }


  .slide_menu_colleges:after,
  .study_abroad:after,
  .slide_menu_courses:after,
  .slide_menu_exams:after,
  .slide_menu_boards:after,
  .slide_menu_resources:after {
    content: " ";
    background: url(../../images/master_sprite.webp);
    width: 12px;
    height: 21px;
    position: absolute;
    right: 10px;
    background-position: 652px -93px;
  }



  .otherEntranceExams {
    padding: 10px;
  }

  .otherEntranceExams h2 {
    padding: 10px;
    margin-bottom: 10px;
    font-weight: var(--font-semibold);
  }


  .downloadPdfSection {
    box-shadow: none;
    border: none;
    border-radius: 0;
    padding: 0;
  }

  .downloadPdfCard .pdfHeading {
    flex-basis: calc(100% - 95px);
  }

  .downloadPdfCard .primaryBtn {
    padding: 6px 14px;
    font-size: 13px;
  }

  .downloadPdfCard p {
    width: calc(100% - 72px);
    letter-spacing: normal;
  }

  .downloadPdfCard .pdfIcon {
    float: left;
    margin-right: 5px;
  }

  .viewAllDiv {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    min-height: 277px;
  }

  .viewAllDiv a {
    text-decoration: none;
    line-height: 24px;
    font-size: 14px;
    color: var(--color-red);
    font-weight: var(--font-semibold);
  }


  .leadFormDiv,
  .writeAnswerDiv {
    padding: 20px;
    max-width: 95%;
    margin: 20px auto;
    margin-top: 65px;
  }

  .leadFormDiv p,
  .writeAnswerDiv p {
    font-size: 12px;
    line-height: 20px;
  }

  .leadFormDiv .headingText,
  .writeAnswerDiv .headingText {
    font-size: 16px;
    line-height: 24px;
    font-weight: var(--font-semibold);
    padding-bottom: 5px;
  }

  .leadFormDiv input,
  .leadFormDiv select,
  .leadFormDiv .dialCodeDiv,
  .writeAnswerDiv input,
  .writeAnswerDiv select,
  .writeAnswerDiv .dialCodeDiv {
    padding: 7px;
    padding-left: 42px;
    min-height: 40px;
    font-size: 13px;
    line-height: 24px;
  }

  .leadFormDiv .form-group,
  .writeAnswerDiv .form-group {
    padding-bottom: 10px;
  }

  .leadFormDiv .primaryBtn,
  .writeAnswerDiv .primaryBtn {
    width: 100%;
    display: block;
    padding: 6px;
  }

  .leadFormDiv .countryCode,
  .leadFormDiv .dialCodeDiv,
  .writeAnswerDiv .countryCode,
  .writeAnswerDiv .dialCodeDiv {
    flex-basis: 90px;
  }

  .leadFormDiv .numberInput,
  .writeAnswerDiv .numberInput {
    flex-basis: calc(100% - 90px);
  }

  .leadFormDiv .numberInput {
    max-height: 40px;
  }

  .writeAnswerDiv {
    top: 50%;
    -webkit-transform: translate(0px, -50%);
    transform: translate(0px, -50%);
    margin-top: 0;
  }

  .checkbox-group {
    padding-bottom: 4px;
  }

  .checkbox-group input {
    padding: 0;
    min-height: auto;
    margin-right: 5px;
  }

  .checkbox-group label {
    font-size: 12px;
    line-height: 20px;
  }

  .userIcon,
  .mailIcon,
  .bookIcon,
  .capIcon,
  .userIconMultiple {
    top: 9px;
  }

  .locationIcon {
    top: 11px;
  }

  .numberInputs input {
    max-width: 19.6%;
    padding-left: 8px;
    margin-right: 12px;
    border-radius: 4px;
  }

  .leadFormDiv .primaryBtn,
  .writeAnswerDiv .primaryBtn {
    margin-right: 0;
  }

  .optSection .primaryBtn {
    margin-bottom: 20px;
  }

  .optSection .row {
    -webkit-box-pack: center;
    justify-content: center;
  }

  .closeLeadForm,
  .closeLeadFormUtm {
    right: 20px;
    top: 20px;
  }

  .formHeadingDiv .formImg {
    flex-basis: 56px;
    margin-right: 10px;
    min-height: 56px;
  }

  .formHeadingDiv .formImg img {
    max-width: 32px;
    max-height: 32px;
    border-radius: 50%;
  }

  .formHeadingDiv p {
    padding: 0;
  }

  .formHeadingDiv .formHeading {
    flex-basis: calc(100% - 66px);
  }

  .formHeadingDiv .headingText {
    padding-right: 8px;
  }

  .leadFormDiv .closeLeadForm,
  .leadFormDiv .closeLeadFormUtm {
    right: 10px;
    top: 10px;
  }

  .headerLogo {
    -webkit-transform: scale(0.86);
    transform: scale(0.86);
  }

  .headerMegaMenu {
    height: 0;
  }

  .leadFormContainer {
    background: var(--color-white);
    z-index: 10;
  }

  .leadFormDiv {
    max-width: 100%;
    margin: 0 auto;
    margin-top: 50px;
  }

  .leadFormHeader {
    background: var(--topheader-bg);
    padding: 3px 20px;
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 1;
  }

  .leadFormHeader .row {
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    margin-right: 0;
  }

  .closeLeadForm {
    background: url(../../images/closeform.png);
    position: initial;
  }

  .leadFormDiv .closeLeadForm {
    display: none !important;
  }

  .dynamicCard {
    text-align: center;
    margin-bottom: 10px;
    padding: 10px;
  }

  .dynamicCard p {
    padding-bottom: 10px;
  }

  .dynamicCard .row {
    flex-direction: column-reverse;
  }

  .dynamicCard .dynamicCardText {
    flex-basis: 100%;
  }

  .dynamicCard .dynamicCardHeading {
    font-size: 18px;
    font-weight: var(--font-semibold);
  }

  .dynamicCard .dynamicCardImage {
    flex-basis: 100%;
    margin: 0;
    text-align: center;
    margin-bottom: 20px;
  }

  .dynamicCard.withoutImage {
    text-align: left;
  }

  .withoutImage .primaryBtn {
    margin: 0 auto;
    display: block;
    max-width: 100px;
  }

  .leadFormDiv .primaryBtn.skip-colleges {
    margin-bottom: 10px !important;
    margin-top: 5px;
  }

  .formSumbitBtn .sponser-colleges {
    margin-bottom: 10px !important;
  }

  .engagementPanel .formHeading .subHeadingText {
    padding-bottom: 10px;
  }

  .clgAsperrequiremtnList .collegeCheckBox .collegeCheckBoxCard {
    margin-bottom: 5px !important;
  }

  .formSumbitBtn {
    margin-bottom: 10px;
  }


  .seachInner .sugstrUl li,
  .seachInner .lstSearches li {
    padding: 10px 16px;
    display: flex;
    align-items: center;
  }

  .recentBlock .recentcol .rcnt .clr-srch {
    position: absolute;
    right: 15px;
  }


  p#letterCount {
    padding-top: 20px;
    padding-left: 18px;
  }

  .overrideZindex {
    display: none !important;
  }


  .searchBar .selection {
    border: 1px solid #d8d8d8;
    display: none;
  }

  ul.ui-menu.ui-widget.ui-widget-content.ui-autocomplete.ui-front.category {
    border: none;
  }

  /* Form height fix */
  .leadFormDiv .form-group {
    position: relative;
    padding-bottom: 17px;
  }

  .leadFormDiv .form-group .help-block {
    position: absolute;
    font-size: 10px;
    padding: 0px;
    bottom: 4px;
    left: 0px;
  }

  .field-leadform-mobile.form-group .help-block {
    margin: 0;
    left: -80px;
    bottom: -12px !important;
  }

  /* Add space between creative and number box*/
  img.mobileOnly.loginCreative {
    margin-bottom: 50px;
  }

  /* Login Pop up */
  .formHeadingDiv .formHeading {
    flex-basis: unset;
  }

  .loginPopupDiv .rightCol .formHeadingDiv .loginHeadingText {
    font-size: 24px !important;
  }

  img.mobileOnly.loginCreative {
    margin: 0 auto;
    margin-bottom: 50px;
  }

  .disclaimer.forPage {
    display: block;
    padding-bottom: 12px;
    font-size: 12px;
    color: #787878;
    line-height: 24px;
  }

  .loginPopupContainer .form-group {
    margin-bottom: 10px !important;
  }

  .loginPopupDiv .rightCol .primaryBtn {
    margin-bottom: 10px;
  }

  p.forPopup.signupOption {
    font-size: 14px;
  }

  .signupOption a,
  .signInOption a {
    font-weight: 500;
  }

  .closeLoginpopup img {
    transform: scale(1.5);
  }

  #otp-form p.error.errorMsg {
    bottom: -7px !important;
  }

  .otpSection img.mobileOnly {
    margin: 0 auto;
    margin-bottom: 14px;
  }

  #lead-form .formHeading {
    flex-basis: calc(100% - 66px);
  }

  /******************* Olympiad Css ************************/

  .selection.olympiadSearchResult {
    margin-left: 0 !important;
  }
}

.lead-login {
  color: #787878;
}

.lead-login:hover {
  text-decoration: none;
  color: #787878;
}

.lead-login span {
  color: var(--color-red);
}

@media screen and (max-width: 768px) {
  .lead-login {
    text-align: center;
    display: block;
  }

  .field-leadform-mobile .help-block {
    margin-left: -90px;
  }
}

@media screen and (min-width: 768px) {
  .field-leadform-mobile .help-block {
    margin-left: -95px;
  }
}

.otpMessage {
  margin-bottom: 10px;
}

.field-leadform-mobile .help-block {
  margin-left: -90px;
}

.myProfileIcon {
  width: 16px;
  height: 16px;
  background-position: -141px -629px;
  vertical-align: text-top;
  margin-right: 5px;
}

.logoutIcon {
  width: 16px;
  height: 20px;
  background-position: -165px -626px;
  vertical-align: text-bottom;
  margin-right: 5px;
}

.logOutOption {
  color: #282828;
}

.Rectangle-856 {
  width: 792px;
  height: 649px;
  flex-grow: 0;
  margin: 348px 32px 0 117px;
  padding: 160px 20px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: #fff;
}

.otpMessage a {
  color: #ff4e53;
}

span#resendTimer span {
  color: #ff4e53;
}

.leadFormDiv .form-group .help-block {
  position: absolute;
  bottom: 3px;
}

.field-leadform-mobile .help-block {
  margin: 0;
  left: -100px;
}

.signInDiv .formHeadingDiv .formHeading {
  flex-basis: 100%;
}

.articleInfo .latestUpdates,
.examInfo .latestUpdates,
.pageData .latestUpdates {
  padding: 10px;
  border-radius: 4px;
  /* background: #f0f8ff; */
  margin-bottom: 20px;
}

.articleInfo .latestUpdates .cardHeading,
.examInfo .latestUpdates .cardHeading,
.pageData .latestUpdates .cardHeading {
  font-size: 15px;
  font-weight: 500;
  line-height: 26px;
  padding-bottom: 10px;
}

.articleInfo .latestUpdates ul,
.examInfo .latestUpdates ul,
.pageData .latestUpdates ul {
  margin: 0;
  padding-left: 20px;
}

.articleInfo .latestUpdates ul li,
.examInfo .latestUpdates ul li,
.pageData .latestUpdates ul li {
  position: relative;
}

.articleInfo .latestUpdates ul li:before,
.examInfo .latestUpdates ul li:before,
.pageData .latestUpdates ul li:before {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  left: -20px;
  top: 5px;
  background: url("../../images/master_sprite.webp");
  background-position: 339px -374px !important;
}

.articleInfo .latestUpdates ul li span,
.examInfo .latestUpdates ul li span,
.pageData .latestUpdates ul li span {
  font-size: 14px;
  color: #ff4e53;
}

.articleInfo .latestUpdates ul li a,
.examInfo .latestUpdates ul li a,
.pageData .latestUpdates ul li a {
  cursor: pointer;
  color: #3d8ff2;
  text-decoration: none;
}

.articleInfo .latestUpdates ul li span p,
.examInfo .latestUpdates ul li span p,
.pageData .latestUpdates ul li span p {
  display: inline;
}

.four-cardDisplay #featuredColl .sliderCardInfo img {
    height: 53px;
}

.container .stickyNavCls,
.quickLinks,
.sidebarAds {
    position: sticky;
    top: 0px;
    z-index: 10;
    /* width: 100%;
    padding: 0px 15px; */
}

.quickLinks,
.sidebarAds {
    top: 58px;
    z-index: 0;
}

/* .collegeRelataedLinks, .pageRedirectionLinks, .examRelataedLinks, .reviewContainer, .review {
    position:sticky !important;
    top:0px;
} */
/*Banner CSS*/
/* 
.bannerContainer {
  display: none;
  position: fixed;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  top:0;
  right: 0;
  z-index: 12;
}
.bannerContainer picture {
  position: relative;
  height: 100%;
  display: inline-block;
}
.close-icon-position{
  position: relative;
}
.bannerContainer .closeBanner {
  background-position: -99px -805px;
  width: 28px;
  height: 28px;
  position: absolute;
  right: -10px;
  top: -10px;
  z-index: 8;
  cursor: pointer;
} */

@media (max-width: 1023px) {
  .bannerContainer {
    width: 100%;
    padding: 5px 10px;
  }
  .bannerContainer .closeBanner {
    top: -15px;
    right: -10px;
  }
}
/* End Banner CSS*/