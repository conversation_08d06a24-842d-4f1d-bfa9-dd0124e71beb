
html {
  box-sizing: border-box;
  -ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

.forPopup {
  display: none !important;
}


.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  background: #fff;
}

a {
  color: #3d8ff2;
  text-decoration: none;
}

button {
  cursor: pointer;
  outline: none;
}

.m-0 {
  margin: 0 !important;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
  position: relative;
}

.align-items-center {
  align-items: center;
}

:root {
  --font-bold: 700;
  --font-semibold: 600;
  --font-500: 500;
  --primary-font-color: #333333;
  --color-red: #ff4e53;
  --color-white: #ffffff;
  --anchor-textclr: #3d8ff2;
  --transition: 0.2s ease;
  --border-line: 1px solid #d8d8d8;
  --footer-bg: #273553;
  --topheader-bg: #0966c2;
}

select,
.select2-container {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-sizing: border-box;
  width: 100%;
  background-image: url("https://www.getmyuni.com/yas/images/select-angle.png") !important;
  background-repeat: no-repeat !important;
  background-position: 95% 19px !important;
  color: var(--primary-font-color);
  width: auto;
}

select {
  width: 100%;
}

.select2-results__option--selectable {
  font-size: 14px;
}

.spriteIcon {
  display: inline-block !important;
  background: url("https://www.getmyuni.com/yas/images/master_sprite.webp");
  text-align: left;
  overflow: hidden;
}

.headerLogo,
.footerLogo {
  width: 179px;
  height: 44px;
  background-position: 256px -72px;
}

.flagIcon {
  width: 27px;
  height: 22px;
  background-position: 415px -165px;
  vertical-align: middle;
  margin-right: 5px;
}

html {
  scroll-behavior: smooth;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

h2 {
  font-weight: var(--font-semibold);
    font-size: 18px;
}
h3{
  font-size: 16px;
} 
.container {
  max-width: 1236px;
}

img {
  max-width: 100%;
  height: auto;
}

.primaryBtn,
a.primaryBtn,
button.primaryBtn {
  display: inline-block;
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px 15px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  transition: 0.2s ease;
  outline: none;
}

.primaryBtn:hover,
a.primaryBtn:hover,
button.primaryBtn:hover {
  box-shadow: 0 0 15px #ccc;
}

.primaryBtn a {
  text-decoration: none;
}

.closeSubscribeForm {
  display: none;
  width: 17px;
  height: 17px;
  background: url("https://www.getmyuni.com/yas/images/closeform.png") no-repeat;
}

.userIcon,
.mailIcon,
.locationIcon,
.bookIcon,
.capIcon {
  width: 20px;
  height: 19px;
  position: absolute;
  z-index: 1;
  left: 12px;
  top: 14px;
}

.userIcon {
  background-position: 595px -238px;
}

.mailIcon {
  background-position: 594px -262px;
}

.locationIcon {
  background-position: 594px -311px;
}

.bookIcon {
  background-position: 595px -285px;
}

.capIcon {
  background-position: 595px -333px;
}

span.select2.select2-container.select2-container--default.select2-container--disabled {
  background-color: #eee;
}

button#submitLoginForm:disabled {
  background-color: rgba(255, 78, 83, 0.4);
}

.mobileLoginField .errorMsgMobile {
  top: 39px;
}

@media (min-width: 768px) {
  .col-md-5 {
    flex: 0 0 41.666667%;
    padding: 0 10px;
    max-width: 41.666667%;
  }

  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 10px;
  }

  .col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
    padding: 0 10px;
  }
}

.mobileOnly {
  display: none;
}

.headerLogo {
  position: relative;
  margin-bottom: 10px;
}

select:required:invalid {
  color: #989898;
  font-size: 13px;
}

option {
  color: var(--primary-font-color);
}

.right-col p.error {
  color: var(--color-red);
  font-size: 10px;
  padding: 0;
  position: absolute;
  line-height: 18px;
}

.numberInput p.error.validationError {
  left: 0;
}

.subscribeSection {
  position: relative;
  background: var(--color-white);
}

.subscribeSection:before {
  content: "";
  position: absolute;
  background: #0966c2;
  width: 33%;
  height: 100%;
  min-height: 100vh;
  left: 0;
  top: 0;
}

.subscribeSection .copyrightsText {
  color: var(--color-white);
  font-size: 16px;
  line-height: 24px;
  position: relative;
  margin-top: 10px;
}

.left-col {
  padding: 40px;
  background: #0d3d63;
  color: var(--color-white);
  border-radius: 10px;
}

.left-col img {
  display: block;
  margin: 0 auto;
  max-width: 400px;
  margin-bottom: 20px;
}

.left-col h1 {
  font-size: 30px;
  line-height: 37px;
  padding-bottom: 15px;
  text-align: center;
}

.left-col p {
  font-size: 18px;
  line-height: 24px;
  text-align: center;
}

.formField {
  position: relative;
  max-width: 350px;
}

.formField input,
.formField select,
.formField .select2-container {
  padding: 7px 12px;
  padding-left: 41px;
  border-radius: 4px;
  border: var(--border-line);
  outline: none;
  width: 100% !important;
  font-size: 13px;
  line-height: 24px;
  background: var(--color-white);
  color: var(--primary-font-color);
}

.formField input::-moz-placeholder,
.formField select::-moz-placeholder,
.formField .select2-container::-moz-placeholder {
  color: #989898;
}

.formField input:-ms-input-placeholder,
.formField select:-ms-input-placeholder,
.formField .select2-container:-ms-input-placeholder {
  color: #989898;
}

.formField input::placeholder,
.formField select::placeholder,
.formField .select2-container::placeholder {
  color: #989898;
}

.formField select {
  background-position: 96% 15px !important;
}

.right-col p {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 10px;
}

.right-col h2 {
  line-height: 30px;
  font-weight: 500;
  margin-bottom: 20px;
}

.right-col .userForm {
  max-width: 350px;
  margin: 0 auto;
}

.right-col .userForm .userIcon,
.right-col .userForm .mailIcon,
.right-col .userForm .locationIcon,
.right-col .userForm .bookIcon,
.right-col .userForm .capIcon {
  top: 12px;
}

.right-col p {
  color: #787878;
}

.right-col p a {
  color: var(--color-red);
  font-weight: 500;
}

.right-col .disclaimer {
  font-size: 12px;
  padding-bottom: 10px;
}

.userForm .form-group {
  margin-bottom: 20px;
}

.userForm .primaryBtn {
  width: 100%;
  text-align: center;
  margin-bottom: 10px;
}

.dialCodeDiv {
  flex-basis: 90px;
  position: relative;
  border-radius: 4px 0 0 4px;
  border: var(--border-line);
  border-right: 0px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialCode {
  color: #989898;
  font-size: 14px;
  line-height: 24px;
}

.numberInput {
  flex-basis: calc(100% - 90px);
}

.numberInput input {
  padding-left: 12px;
  width: 100%;
  border-radius: 0 4px 4px 0;
  width: 100%;
  height: 40px;
}

.otpInputs {
  padding-bottom: 20px;
  position: relative;
}

.otpInputs input {
  max-width: 64px;
  margin-right: 23px;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  padding: 11px;
  color: #989898;
  border-radius: 4px;
  border: var(--border-line);
  outline: none;
}

.otpInputs input:last-child {
  margin-right: 0px;
}

.logInOption {
  padding: 0;
}

.select2-selection.select2-selection--single {
  padding: 0;
  border: none;
  line-height: 24px;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding: 0;
  line-height: 24px;
}

span.select2-selection__arrow {
  display: none;
}

.select2-search--dropdown .select2-search__field {
  padding: 7px 12px;
  font-size: 13px;
  line-height: 24px;
}

.select2-container .select2-selection--single {
  background-image: url(https://www.getmyuni.com/yas/images/select-angle.png) !important;
  background-repeat: no-repeat !important;
  background-position: 100% 9px !important;
  height: 24px;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #989898;
  font-size: 13px;
}

.pageBody {
  display: flex;
  height: 100%;
  justify-content: space-between;
  flex-direction: column;
  padding: 10px 0;
  /* padding-top: 20px; */
}

/* .errorInputField {
  border: 1px solid #ff4e53 !important;
} */

#otp-form p.error.errorMsg {
  bottom: 0;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

#formEmail {
  position: relative;
  display: inline-block;
}

.domainExtention {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #989898;
}

@media (max-width: 1023px) {
  .formField .select2-container {
    width: 100% !important;
  }

  .right-col .userForm,
  .formField {
    max-width: 100%;
  }

  .col-md-6,
  .col-md-5,
  .col-md-7 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .mobileOnly {
    display: block !important;
  }

  .desktopOnly {
    display: none !important;
  }

  .subscribeSection.registrationPage {
    padding: 0;
  }

  .subscribeSection.registrationPage .right-col {
    margin-top: 60px;
  }

  .subscribeSection:before {
    display: none;
  }

  .subscribeSection .left-col {
    display: none;
  }

  .subscribeSection .right-col {
    margin-top: 50px;
  }

  .subscribeSectionHeader {
    background: var(--topheader-bg);
    padding: 3px 9px;
    padding-right: 20px;
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 1;
  }

  .subscribeSectionHeader .headerLogo {
    margin-bottom: 0;
    transform: scale(0.7);
    margin-left: -20px;
  }

  .subscribeSectionHeader .row {
    justify-content: space-between;
    align-items: center;
  }

  .right-col .disclaimer {
    padding-bottom: 12px;
  }

  .right-col .logInOption {
    text-align: center;
    padding-bottom: 0px;
  }

  .userForm .primaryBtn {
    margin-bottom: 10px;
  }

  .logInPage {
    padding-top: 30px;
  }

  .logInPage img {
    margin: 0 auto;
    margin-bottom: 50px;
  }

  .dialCodeDiv img {
    margin: inherit;
  }

  .otpInputs input {
    margin-right: 16px;
  }

  .closeSubscribeForm {
    display: block !important;
  }

  /* Login Signup Popup  */
  .right-col .registerSection h2 {
    margin-bottom: 10px;
    color: #333333;
    font-weight: 600;
  }
}

@media (min-width: 1023px) and (max-height: 690px) {
  .row.m-0.align-items-center {
    margin-top: -55px !important;
  }
}