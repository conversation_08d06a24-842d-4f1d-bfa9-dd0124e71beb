.spriteIconTemporary {
    display: inline-block !important;
    background: url(../../../yas/images/contactUs/master_sprite.webp);
    text-align: left;
    overflow: hidden;
}

.heroAndContactUsForm {
    margin-top: 20px;
    display: flex;
    padding: 0;
}

.heroAndContactUsForm .formSide {
    /* max-width: 736px; */
    padding: 40px 0px;
    flex: 1;
    background-color: #fff;
    border: 1px solid #d8d8d8;
}

.heroAndContactUsForm .heroSide {
    max-width: 470px;
    background-color: #0d3f64;
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.heroAndContactUsForm .heroSide .heroHeading {
    font-size: 24px;
    font-weight: 500;
    line-height: 1.17;
    color: #fff;
    margin-bottom: 10px;
}

.heroAndContactUsForm .heroSide .heroSubheading {
    font-size: 15px;
    font-weight: normal;
    line-height: 1.6;
    color: #fff;
}

.heroAndContactUsForm .heroSide .socialMediaRow {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.heroAndContactUsForm .heroSide .socialMediaRow .socialIconDiv {
    display: inline-flex;
    vertical-align: middle;
    cursor: pointer;
}

.heroAndContactUsForm .heroSide .socialMediaRow .socialIconDiv>* {
    width: 28px;
    height: 28px;
    border-radius: 50px;
}

.heroAndContactUsForm .heroSide .heroImg {
    margin: auto 0;
}

.heroAndContactUsForm .contactUsForm {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 540px;
    margin: 0 auto;
}

.heroAndContactUsForm .contactUsForm .formInput input,
.heroAndContactUsForm .contactUsForm .formInput textarea {
    height: 48px;
    border-radius: 4px;
    border: solid 1px #d8d8d8;
    background-color: #fff;
    padding: 12px 20px;
    width: 100%;
}

.heroAndContactUsForm .contactUsForm .formInput input::placeholder,
.heroAndContactUsForm .contactUsForm .formInput textarea::placeholder {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #989898;
}

.heroAndContactUsForm .contactUsForm .queryDiv textarea {
    height: 130px;
    resize: none;
}

.heroAndContactUsForm .contactUsForm .submitDiv {
    align-self: flex-end;
    margin-top: 10px;
}

.heroAndContactUsForm .contactUsForm .submitDiv button {
    border-radius: 3px;
    font-size: 14px;
    line-height: 24px;
    padding: 6px;
    text-align: center;
    color: var(--color-white);
    font-weight: var(--font-bold);
    border: none;
    background: #ff4e53;
    width: 125px;
    height: 40px;
}

.sectionDiv {
    margin-top: 20px;
    padding: 20px;
    border-radius: 4px;
    border: solid 1px #d8d8d8;
    background-color: #fff;
}

.sectionDiv .sectionHeading {
    font-size: 24px;
    font-weight: 500;
    line-height: 1;
    color: #282828;
}

.contactUsModesSection {
    text-align: center;
}

.contactUsModesSection a,
.boundingBorder .addressDiv a {
    color: black;
}

.contactUsModesSection .contactUsModes {
    display: flex;
    margin-top: 30px;
}

.contactUsModesSection .contactUsMode {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.contactUsModesSection .contactUsMode .contactUsIcon {
    width: 57px;
    height: 57px;
    background-color: #0d3f64;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
}

.contactUsModesSection .contactUsMode .businessQueryIcon {
    background-position: -545px -956px;
    width: 31px;
    height: 31px;
}

.contactUsModesSection .contactUsMode .counsellingQueryIcon {
    background-position: -595px -959px;
    width: 34px;
    height: 25px;
}

.contactUsModesSection .contactUsMode .workWithUsIcon {
    background-position: -645px -957px;
    width: 31px;
    height: 29px;
}

.contactUsModesSection .contactUsMode .contactUsHeading {
    font-size: 18px;
    font-weight: 500;
    line-height: 1.56;
    color: #282828;
    margin-bottom: 10px;
}

.contactUsModesSection .contactUsMode .contactDetails {
    margin-bottom: 10px;
}

.contactUsModesSection .contactUsMode .contactDetails p {
    font-size: 15px;
    font-weight: normal;
    line-height: 1.87;
    color: #282828;
    text-decoration: underline;
    cursor: pointer;
}

.contactUsModesSection .contactUsMode .contactNumber {
    font-size: 15px;
    font-weight: normal;
    line-height: 1.87;
    color: #282828;
}

.contactUsModesSection .middleMode {
    flex: 1.5;
    border-left: 1px solid #d8d8d8;
    border-right: 1px solid #d8d8d8;
}

.tab-content {
    display: none;
}

.tab-content.current {
    display: block;
}

.ourOfficesWidget .boundingBorder {
    display: flex;
}

.ourOfficesWidget .tabberSide {
    padding: 0 40px;
    flex: 1;
}

.ourOfficesWidget .tabberSide h2 {
    font-weight: 500;
    color: #282828;
}

.ourOfficesWidget .selectOfficeRow ul {
    display: flex;
    margin: 0;
    padding: 0;
    list-style-type: none;
    gap: 20px;
    margin-top: 20px;
    position: relative;
}

.ourOfficesWidget .selectOfficeRow ul li {
    cursor: pointer;
    font-size: 15px;
    font-weight: normal;
    line-height: 1.6;
    color: #787878;
    padding-right: 10px;
}

.ourOfficesWidget .selectOfficeRow ul li.current {
    color: #ff4e53;
    position: relative;
    font-weight: 600;
}

.ourOfficesWidget .selectOfficeRow ul li.current::after {
    content: "";
    border-bottom: 3px solid #ff4e53;
    display: inline-block;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -10px;
    z-index: 1;
}

.ourOfficesWidget .selectOfficeRow ul::after {
    display: inline-block;
    height: 0.5px;
    width: calc(100% + 20px);
    border: 1px solid #d8d8d8;
    position: absolute;
    content: "";
    bottom: -10px;
}

.ourOfficesWidget .addressDiv {
    margin-top: 30px;
    font-size: 15px;
    font-weight: normal;
    line-height: 1.6;
    color: #282828;
}

.ourOfficesWidget .addressDiv .mainLine {
    margin-bottom: 10px;
}

.ourOfficesWidget .addressDiv .phoneIcon {
    background-position: -535px -932px;
    width: 13px;
    height: 12px;
    margin-right: 10px;
}

.ourOfficesWidget .addressDiv .emailIcon {
    background-position: -503px -932px;
    width: 17px;
    height: 12px;
    margin-right: 10px;
}

.contactUsFormSubmitted {
    border-radius: 4px;
    background-color: #fff;
    border: none;
    padding: 25px;
    width: 550px;
    height: 306px;
    text-align: center;
}

.contactUsFormSubmitted:focus {
    outline: none;
}

.contactUsFormSubmitted::backdrop {
    background-color: rgba(51, 51, 51, 0.6);
}

.contactUsFormSubmitted .centerAlignClass {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
}

.contactUsFormSubmitted .formSubmitIcon {
    background-position: -253px -931px;
    width: 100px;
    height: 100px;
    margin-bottom: 10px;
}

.contactUsFormSubmitted .contactUsCloseIcon {
    background-position: -91px -335px;
    width: 16px;
    height: 16px;
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
}

.contactUsFormSubmitted p:first-of-type {
    font-size: 24px;
    font-weight: 600;
    color: #282828;
    margin-bottom: 5px;
}

.contactUsFormSubmitted p:last-of-type {
    font-size: 16px;
    font-weight: normal;
    line-height: 1.5;
    color: #282828;
}

@media (max-width: 1023px) {
    .heroAndContactUsForm {
        position: relative;
        margin-top: 0;
    }

    .heroAndContactUsForm .formSide {
        max-width: unset;
        padding: 10px;
        position: absolute;
        top: 268px;
        width: 100%;
        border-radius: 4px;
        border: solid 1px #d8d8d8;
        background-color: #fff;
    }

    .heroAndContactUsForm .heroSide {
        max-width: unset;
        margin: 0 -10px;
        padding: 10px;
        align-items: center;
        text-align: center;
        padding-bottom: 150px;
    }

    .heroAndContactUsForm .heroSide .heroHeading {
        font-size: 18px;
        font-weight: normal;
        line-height: 1.56;
        margin-bottom: 0;
    }

    .heroAndContactUsForm .heroSide .heroSubheading {
        font-size: 15px;
        font-weight: normal;
        line-height: 1.6;
        max-width: 288px;
        margin-bottom: 10px;
    }

    .heroAndContactUsForm .contactUsForm {
        gap: 10px;
    }

    .heroAndContactUsForm .contactUsForm .formInput input,
    .heroAndContactUsForm .contactUsForm .formInput textarea {
        padding: 8px 16px;
        height: 40px;
    }

    .heroAndContactUsForm .contactUsForm .formInput textarea {
        height: 160px;
    }

    .heroAndContactUsForm .contactUsForm .submitDiv {
        align-self: unset;
        margin-top: -5px;
    }

    .heroAndContactUsForm .contactUsForm .submitDiv button {
        width: 100%;
        height: 36px;
    }

    .contactUsModesSection {
        margin-top: 260px;
    }

    .contactUsModesSection .contactUsModes {
        flex-direction: column;
        margin-top: 0;
        border: 1px solid #d8d8d8;
        border-radius: 4px;
    }

    .contactUsModesSection .contactUsMode {
        padding-bottom: 10px;
        padding-top: 10px;
        border-bottom: 1px solid #d8d8d8;
    }

    .contactUsModesSection .contactUsMode .contactUsIcon {
        margin-bottom: 10px;
    }

    .contactUsModesSection .contactUsMode .contactDetails,
    .contactUsModesSection .contactUsMode .contactUsHeading {
        margin-bottom: 5px;
    }

    .contactUsModesSection .middleMode {
        border-left: 0;
        border-right: 0;
    }

    .sectionDiv {
        padding: 10px;
    }

    .sectionDiv .sectionHeading {
        font-size: 18px;
        font-weight: normal;
        line-height: 1.56;
        margin-top: 10px;
    }

    .ourOfficesWidget .boundingBorder {
        border: 1px solid #d8d8d8;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
    }

    .ourOfficesWidget .addressDiv {
        margin-top: 20px;
    }

    .ourOfficesWidget .addressDiv .mainLine {
        margin-bottom: 5px;
    }

    .ourOfficesWidget .addressDiv .mapDiv {
        padding: 0 10px 10px 10px;
    }

    .ourOfficesWidget .mapSide iframe {
        width: 100%;
        border-radius: 4px;
    }

    .ourOfficesWidget .mapSide .mapDiv {
        padding: 0 10px 10px 10px;
    }

    .ourOfficesWidget .tabberSide {
        order: -1;
        padding: 0;
    }

    .ourOfficesWidget .tabberSide h2 {
        font-weight: normal;
        line-height: 1.56;
        text-align: center;
        margin-top: 10px;
    }

    .ourOfficesWidget .selectOfficeRow {
        margin-top: 10px;
    }

    .ourOfficesWidget .selectOfficeRow ul {
        margin-top: 0px;
    }

    .ourOfficesWidget .selectOfficeRow ul::after {
        width: 100%;
    }

    .ourOfficesWidget .selectOfficeRow ul li:first-child {
        padding-left: 10px;
    }

    .pageFooter {
        padding-bottom: 0;
    }
}

.heroAndContactUsForm .contactUsForm .formInput input.error,
.heroAndContactUsForm .contactUsForm .formInput textarea.error {
    border: 1px solid red !important
}

.blueBgDiv {
    height: 0px;
}

button:disabled,
button[disabled] {
    border: 1px solid #e9acac !important;
    background: #e9acac !important;
}

#contactus-mobile-label {
    font-size: 10px;
    color: red;
}