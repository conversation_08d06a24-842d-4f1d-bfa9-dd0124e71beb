// This file contains js for exam landing filter page, college landing filter page, college course and fees filter and review page filter

// EXAM LANDING FILTER START

$('.filter-container').on('click', '.remove-filter', function () {
    var remove = $(this).parent().data('slug');
    $("input[value='" + remove + "']").prop('checked', true).prop('checked', false);
    updateItems();
});
var $examSearchForm = $('#exam-search-form');

function updateItems(sortBy = $('[name="sort"]').val()) {
    $.post($examSearchForm.attr('action').split("?")[0] + '?sort=' + sortBy, $examSearchForm.serialize(), function (html) {
        $('#filter-loader').show();
        $(".filter-container").html($(html).find('div.filter-container').html());
        setTimeout(removePreLoader, 400);
        loadCta();
    }, 'html');
}

$('body').on("change", '[name="sort"]', function () {
    updateItems();
});

$('body').on("click", '#mobileSortBy li', function (e) {
    e.preventDefault();
    $("#mobileSortBy li").removeClass("selected");

    updateItems($(this).data("sort"));

    $(this).addClass("selected");
    $(".mobileSortSection").fadeOut();
})

$("#mobile-reset").on("click", function () {
    $(".filterTab input[type='checkbox']").prop('checked', false)
    updateItems();
});

$examSearchForm.on("change", "input[type='checkbox']", function () {
    updateItems();
});

$("#applyFilterNew").on('click', function (event) {
    event.preventDefault();

})

// EXAM LANDING FILTER END

/** College Course Filter Page starts */

$(document).on('click', '.removeFilter', function (e) {
    e.preventDefault();
    var removeValue = $(this).parent().parent().data('slug');
    $("input[value='" + removeValue + "']").prop('checked', false);
    updateCourseItems();
});

var $collegeCourseSearchForm = $('#college-search-form');
function updateCourseItems(sortBy) {
    $('#filter-loader').show();
    sort = sortBy ? sortBy : '';
    if ($collegeCourseSearchForm.attr('action') != undefined) {
        $.post($collegeCourseSearchForm.attr('action').split("?")[0] + '?sort=' + sort, $('#college-search-form').serialize(), function (html) {
            $('.filterSection').html();
            $(".courseTypeList").html($(html).find('div.courseTypeList').html());
            $(".filtersAll").html($(html).find('section.filterSection').html());

            let buttonLe = $(".filterSectionSelection button").length;
            if (buttonLe > 0) {
                $(".filterSectionForm").css('padding-bottom', '10px');
                $(".filterSectionForm").css('border-bottom', '1px solid #d8d8d8');
                if (gmu.config.isMobile == 1) {
                    $(".filterSectionSelection").css('margin-top', '10px');
                }
            }

            showMoreLessCoursesFees();
            setTimeout(removePreLoader, 400);

            //hide on part time selection 
            let partTimeCheckBox = document.querySelector('#part_time');
            let distanceCheckBox = document.querySelector('#distance_learning');
            if (partTimeCheckBox !== null && partTimeCheckBox.checked === true || distanceCheckBox !== null && distanceCheckBox.checked === true) {
                document.querySelectorAll('.courseItems').forEach((item) => {
                    item.style.display = 'none'
                })
            } else {
                document.querySelectorAll('.courseItems').forEach((item) => {
                    item.style.display = 'block'
                })
            }

            var courseDivLength = $('<div />').html(html).find('.courseListCard').length;
            if (courseDivLength > 3) {
                showMoreContent();
            }
            loadCta('course-category', true);
        }, 'html');
    }
}
var exploreCourseCounts = $('.courseInfo .moreCourse span').length;

$('.courseInfo .moreCourse span:nth-child(-n + 2)').show();
$('.courseInfo .moreCourse .textBlue').on('click', function (e) {
    $(this).parent().children('span').slice(0, exploreCourseCounts).show();
    $(this).remove();
});


function showMoreLessCoursesFees() {
    var courseTypeDiv = $('.courseTypeMaster').length;
    $(".courseTypeMaster").each(function () {
        var asd = $(this).children('.courseTypeDiv').length;
        if (asd > 4) {
            $(this).addClass("limitCard");
            $(this).after('<div class="showMoreCourseWrap"><p class="showMoreCourseCard">Show More</p></div>');
            $(this).css({
                "margin-bottom": "0px",
            });
        }
    });

    $("body .courseTypeList .showMoreCourseWrap").click(function () {
        var toggleText = $(this).text() == "Show Less" ? "Show More" : "Show Less";
        $(this).children().text(toggleText);
        $(this).prev().toggleClass("limitCard");
    });

}
showMoreLessCoursesFees();

$("body").on('click', '.filterCategoryName', function () {
    $(this).next("").slideToggle("").toggleClass("tglClass");
    $(this).toggleClass("down_angle");
});

$(document).ready(function () {


    // $('.courseListContainer .courseListCard:not(:last-child)').css('border-bottom', '1px solid #ccc');

    $('body .filterSection').on("change", "input[type='checkbox']", function (e) {
        updateCourseItems();
    });

    // $('body .filterContentDiv').on("change", "input[type='checkbox']", function (e) {
    //     updateCourseItems();
    // });

    let courseCount = $("#collegecoursesearch-course").children().length;
    let branchCount = $("#collegecoursesearch-branch").children().length;

    $('#resetFilter').click(function () {
        $('.tab-content li').children('input').prop('checked', false);
        updateCourseItems();

    });

    $('body').on("click", "#resetAll", function (e) {
        $('.filterCheckButtons li').children('input').prop('checked', false);
        updateCourseItems();
    });

    // $('#clearAll').click(function () {
    //     $('.filterSearch li').children('input').prop('checked', false);
    //     updateCourseItems();
    // });

    $('body').on("click", ".filtersAll ul.tabs li", function (e) {
        var tab_id = $(this).attr('data-tab');
        $('ul.tabs li').removeClass('current');
        $('.tab-content').removeClass('current');

        $(this).addClass('current');
        $("#" + tab_id).addClass('current');
    });

    $('body').on("click", ".filterCheckContainer.courseMore", function (e) {
        $('.filterCheckContainer.hideCourse ').css('display', 'inline-block');
        $('.filterCheckContainer.courseMore').hide();
        $('#courseViewLess').css('display', 'inline-block');
    });

    $('body').on("click", "#courseViewLess", function (e) {
        $('.filterCheckContainer.hideCourse').css('display', 'none');
        $('#courseViewLess').hide();
        $('.filterCheckContainer.courseMore').show();
    });

    $('body').on("click", ".filterCheckContainer.branchMore", function (e) {
        $('.filterCheckContainer.hideBranch ').css('display', 'inline-block');
        $('.filterCheckContainer.branchMore').hide();
        $('#branchViewLess').css('display', 'inline-block');
    });

    $('body').on("click", "#branchViewLess", function (e) {
        $('.filterCheckContainer.hideBranch').css('display', 'none');
        $('#branchViewLess').hide();
        $('.filterCheckContainer.branchMore').show();
    });

    $('body').on("click", ".examMore", function (e) {
        let id = $(this).data('id');
        $('.' + id + '_hideExam').css('display', 'inline');
        $('.examMore.' + id + 'Exam').css('display', 'none');
    });

    $('body').on("click", ".examMoreFilter", function (e) {
        let id = $(this).data('id');
        $('.' + id + '_hideExamFilter').css('display', 'inline');
        $('.examMoreFilter.' + id + 'Exam').css('display', 'none');
    });


    $('body').on("click", ".programExamCount", function (e) {
        $('.programHideExam').css({ 'display': 'inline', 'color': '#333333' });
        $('.programExamCount').css('display', 'none');
    });

    $('body').on("click", ".specializationMore", function (e) {
        let id = $(this).data('id');
        $('.' + id + '_hideSpecialization').css('display', 'inline');
        $('.specializationMore.' + id + 'Specialization').css('display', 'none');
    });

    // $('body').on("click", ".spriteIcon.closeIconHover", function (e) {
    //     $('.tooltipIconText').attr('style', 'display','none');
    // });

    $('body').on("click", ".lastDateToApply .spriteIcon.closeIcon", function (e) {
        $('.lastDateToApply.mobileOnly').attr('style', 'display: none !important');
    });

    $('body').on("click", '#mobileSortByCollege li', function (e) {
        e.preventDefault();
        $("#mobileSortByCollege li").children('input').attr("checked", false);

        updateCourseItems($(this).children('input').attr("id"));

        $(this).children('input').attr("checked", true);
        $(".mobileSortSection").fadeOut();
    });

    // dynamic cta
    if (document.querySelector('.dynamic-cta') !== undefined) {
        var slugName = $.map($('.dynamic-cta'), function (e) {
            return ($(e).attr('data-slug'));
        });

        if (slugName == '') {
            return false;
        }

        $.ajax({
            type: 'POST',
            url: '/ajax/dynamic-cta',
            data: { slug: slugName, entity_id: gmu.config.entity_id, entity: gmu.config.entity },
            dataType: "json",
            success: function (response) {
                if (response.data) {
                    $(".dynamic-cta").each(function (e) {
                        if (response.data[this.dataset.slug] != undefined) {
                            $(this).html(response.data[this.dataset.slug]);
                        }
                    })
                }
            }
        });
    }
});

/** College Course Filter Page ends */



//filterpage Js
async function getSponsorColleges(params = '') {
    if (gmu.config.sponsor_params != '') {
        if (params) {
            gmu.config.sponsor_params = params
        }
        $.ajax({
            type: "get",
            url: '/ajax/get-sponsor-colleges',
            data: gmu.config.sponsor_params,
            success: function (response) {
                $('.sponser-list').html(response.firstSponsorList);
                var currentIndex = 0;
                var itemWidth = $('.carousel-item').outerWidth();
                var visibleItems = 2;
                var totalItems = $('.carousel-item').length;
                var maxIndex = totalItems - visibleItems;

                // Function to update the active dot
                function updateDots(index) {
                    $('.dot').removeClass('active');
                    $('.dot').eq(index).addClass('active');
                }

                // Function to move the carousel to a specific slide
                function goToSlide(index) {
                    $('.carousel-track').css('transform', 'translateX(' + (-itemWidth * index * 2.1) + 'px)');
                    //  $('.carousel-track').css('transform', 'translateX(' + (-itemWidth * index) + 'px)');
                    updateDots(index);
                }

                // Handle dot click
                $('.dot').click(function () {
                    var index = $(this).data('slide');
                    currentIndex = index;
                    goToSlide(index);
                    // alert(index);
                });

                // Handle mouse scroll
                /*  $('.carousel-container').on('wheel', function (event) {
                          if (event.originalEvent.deltaY < 0) {
                                  // Scrolling up (previous slide)
                                  if (currentIndex > 0) {
                                          currentIndex--;
                                          goToSlide(currentIndex);
                                  }
                          } else {
                                  // Scrolling down (next slide)
                                  if (currentIndex < maxIndex) {
                                          currentIndex++;
                                          goToSlide(currentIndex);
                                  }
                          }
                  });*/

                // Initialize the first dot as active
                updateDots(currentIndex);


                if (response.firstSponsorList != '') {
                    $('#sponsor_list_slot1').html(response.firstSponsorList);
                }
                if (response.secondSponsorList != '') {
                    $('#sponsor_list_slot2').html(response.secondSponsorList);
                }
                if (response.thirdSponsorList != '') {
                    $('#sponsor_list_slot3').html(response.thirdSponsorList);
                }
                loadCtaFilter('college-filter', gmu.config.sponsor_params);
            }
        })
    }
}

async function toTop() {
    $("body .filterSearch ul li input").each(function () {
        var parentElement = $(this).parent();
        if ($(this).is(':checked')) {
            $(this).parent().parent().prepend(parentElement);
        }
    });

    $("body .mobileFilter").on('click', function () {
        $(".mobileFilterSection").fadeIn();
    });

    $("body .mobileSort").on('click', function () {
        $(".mobileSortSection").fadeIn();
    });

    $('body .close_sortPopup').on('click', function () {
        $(".mobileSortSection").fadeOut();
    });

    $('body .closeFilter, .applyFilter').on('click', function () {
        $(".mobileFilterSection").fadeOut();
    });

    $('.filterTab ul.tabs li:first-child').addClass('current');

    $('ul.tabs li').click(function () {
        var tab_id = $(this).attr('data-tab');

        $('ul.tabs li').removeClass('current');
        $('.tab-content').removeClass('current');

        $(this).addClass('current');
        $("#" + tab_id).addClass('current');
    });
}

/** Script for radio-checkbox option transition starts*/
$(document).on('click', '#collegesearch-state input[type="checkbox"]', function () {
    $('#collegesearch-state input[type="checkbox"]').not(this).prop('checked', false);
});
$(document).on('click', '#collegesearch-ownership input[type="checkbox"]', function () {
    $('#collegesearch-ownership input[type="checkbox"]').not(this).prop('checked', false);
});
$(document).on('click', '#collegesearch-stream input', function () {
    $('#collegesearch-stream input').not(this).prop('checked', false);
    $('#collegesearch-course input, #collegesearch-specialization input').prop('checked', false)
});

$(document).on('click', '#collegesearch-course input', function () {
    $('#collegesearch-course input[type="checkbox"]').not(this).prop('checked', false);
    $('#collegesearch-specialization input').prop('checked', false)
});

$(document).on('click', '#collegesearch-specialization input', function () {
    $(' #collegesearch-specialization input').not(this).prop('checked', false);
});

$(document).on('click', '#collegesearch-affiliated_by input', function () {
    $('#collegesearch-affiliated_by input').not(this).prop('checked', false);
});

$(document).on('click', '#collegesearch-approvals input', function () {
    $('#collegesearch-approvals input').not(this).prop('checked', false);
});
/** Script for radio-checkbox option transition ends*/

$('body').on('click', '#selectedFilters .remove-college-filter', function (e) {
    var checkBoxId = $(this).parent("button").attr("id");
    var dataAttr = $(this).parent("button").data("attr");
    $("input[id='" + checkBoxId + "']").prop('checked', false);
    if (dataAttr == 'state') {
        $('#collegesearch-city input').prop('checked', false)
    }
    if (dataAttr == 'stream') {
        $('#collegesearch-course input, #collegesearch-specialization input').prop('checked', false)
    }
    if (dataAttr == 'course') {
        $('#collegesearch-specialization input').prop('checked', false)
    }
    getCollegeFilterData();
});

$("body .foundClgs button").slice(0, 2).show();
$(".showMoreOptions").click(function () {
    $(".foundClgs button").slice(0, 30).show();
    $(this).remove();
});
getSponsorColleges();
var ajaxTriggered = false;
function getCollegeFilterData(page, sortBy, isReset, rank, isWidget = true) {
    if (isWidget == false) {
        isWidget = false;
    }

    var data = $("#college-filter-form").serialize() + '&page=' + page + '&sortBy=' + sortBy + '&rank=' + rank + '&isWidget=' + isWidget;
    var dataVariable = $("#college-filter-form").serialize() + '&page=' + page + '&sortBy=' + sortBy + '&rank=' + rank + '&isWidget=' + isWidget;
    $('#filter-loader').show();
    $.post("/college/get-filter-data", data,
        function (data, textStatus, jqXHR) {
            var html = data.html;
            var url = data.url;
            var oldLocation = window.location.href;
            history.pushState(oldLocation, "page", url);
            ajaxTriggered = false;
            
            if (textStatus == 'success') {
                console.log(dataVariable); 
                $("#collegeFilterDSection").html($(html).find('#collegeFilterDSection').html());
                $(".filter__selected__container").html($(html).find('.filter__selected__container').html());
                $(".college__Landing__Hero__Section1").html($(html).find('div.college__Landing__Hero__Section1').html());
                $(".filtered__college_count").html($(html).find('.filtered__college_count').html());
                $("body nav.breadcrumbDiv").html($(html).find('nav.breadcrumbDiv').html());
                $("body .interestedExam").html($(html).find('.interestedExam'));
                $.post("/college/get-filter-data-college", dataVariable,
                function (data1, textStatus, jqXHR) {
                    //console.log(data1.html); return false;
                    var html = data1.html;
                     var url = data1.url;
                    if (data1.hasNext == true) {
                        $(".loadMoreList").show();
                    } else {
                        $(".loadMoreList").hide();
                    }
                   
                   
                $(".load__more__row").html($(html).find('.load__more__row').html());
                if (page == undefined || page == '') {
                     $(".searchedcollegeList").html($(html).find('div.searchedcollegeList').html());
                 } else if (page != undefined) {
                    alert($(html).find('div.searchedcollegeList').html());
                    $(".searchedcollegeList").append($(html).find('div.searchedcollegeList').html());
                     $(".loadMoreList").attr('data-page', data1.page);
                 }
                 $(".total_count").text(data1.totalCount);
                 $(".total_showing").text(data1.totalCount);
                 $(".total_college_count_ajax").text(data1.totalCount);
                 $(".filter-faq").html($(html).find('div.filter-faq').html());
                 var title = data1.data.meta_title ?? data1.data.title;
                 var desc = data1.data.meta_description ?? '';
                 var fullUrl = location.protocol + '//' + location.host + url;
                 var canonicalUrl = location.protocol + '//' + location.host + url.split("?")[0];
                 $("meta").each(function () {
                    if (($(this).attr("name") == "description") || ($(this).attr("property") == "og:description") || ($(this).attr("property") == "twitter:description")) {
                        $(this).attr("content", desc);
                    } else if (($(this).attr("property") == "og:url") || ($(this).attr("property") == "twitter:url")) {
                        $(this).attr("content", fullUrl);
                    } else if (($(this).attr("property") == "og:title") || ($(this).attr("property") == "twitter:title")) {
                        $(this).attr("content", title);
                    }
                });
                $("title").text(title);
                $('link[rel="canonical"]').attr('href', canonicalUrl);
                readMore(); 
              
                $("body .foundClgs button").slice(0, 2).show();
                $(".showMoreOptions").click(function () {
                    $(".foundClgs button").slice(0, 30).show();
                    $(this).remove();
                });
                $(".faq_answer").hide();
                $(".faq_question").click(function () {
                    $(this).toggleClass("downAngle");
                    $(this).next(".faq_answer").slideToggle("slow").siblings(".faq_answer:visible").slideUp("slow");
                });
                showMoreContent();
                getSponsorColleges(data1.sponsorParams);
                toTop();
                if (isReset) {
                    $(".mobileFilterSection").fadeIn();
                }
                $("body .highlight__value").each(function () {
                    var htmlText = $.trim($(this).html());
                    if (htmlText == '-/-') {
                        $(this).addClass('no__value');
                    }
                });
    
                $("body .highlight__name").each(function () {
                    var htmlText = $.trim($(this).html());
                    if (htmlText == '-/-') {
                        $(this).addClass('no__value');
                    }
                });
    
                setTimeout(removePreLoader, 400)
                },"json");
               // $("body nav.breadcrumbDiv").html($(html).find('nav.breadcrumbDiv').html());

              //  $(".college__Landing__Hero__Section1").html($(html).find('div.college__Landing__Hero__Section1').html());

               
              
                
            }
           
            // loadCtaFilter('college-filter');
            // loadCtaFilterTwo('college-filter')

        },
        "json"
    );
}

$('body').on('change', '#college-sort', function (e) {
    var sort = $(this).children('option:selected').val();
    $(this).children('option:selected').attr('selected', true);
    getCollegeFilterData('', sort);
});

if (window.screen.width > 1023) {
    $('body').on("change", "#collegeFilterDSection .filterDiv ul input", function () {
        var sort = $('#college-sort').children('option:selected').val();
        getCollegeFilterData('', sort);
        $("html, body").animate({ scrollTop: "0" }, 200);
    });
}

if (window.screen.width < 1023) {
    $("body").on('click', '#collegeFilterDSection .applyFilter', function () {
        getCollegeFilterData();
        $("html, body").animate({ scrollTop: "0" }, 200);
    });

    $('body').on("click", '.mobileSortDiv ul li', function (e) {
        // e.preventDefault();
        getCollegeFilterData('', $(this).children('input').attr("id"));
        $(".mobileSortSection").fadeOut();
    })
}

toTop();
$("body").on("click", ".filterSearch ul li input", function () {
    var parentEle = $(this).parent();
    var parent = $(this).parent().parent().attr('id');
    if ($(this).is(':checked')) {
        if (parent == 'collegesearch-state') {
            $('#collegesearch-city input').prop('checked', false)
        } else if (parent == 'collegesearch-stream') {
            $('#collegesearch-ownership input, #collegesearch-exam input, #collegesearch-course input, #collegesearch-specialization input').prop('checked', false)
        } else if (parent == 'collegesearch-course') {
            $('#collegesearch-ownership input, #collegesearch-exam input, #collegesearch-specialization input').prop('checked', false)
        } else if (parent == 'collegesearch-ownership') {
            $('#collegesearch-stream input, #collegesearch-exam input, #collegesearch-course input, #collegesearch-specialization input').prop('checked', false)
        } else if (parent == 'collegesearch-exam') {
            $('#collegesearch-ownership input, #collegesearch-stream input, #collegesearch-course input, #collegesearch-specialization input').prop('checked', false)
        } else if (parent == 'collegesearch-specialization') {
            $('#collegesearch-ownership input, #collegesearch-exam input').prop('checked', false)
        }

        if (window.screen.width > 1023) {
            $(this).parent().parent().prepend(parentEle);
        }

    } else {
        var fetchId = $(this).parent().parent().attr('id');
        $(this).parent().sort(function (a, b) {
            return $(a).text().localeCompare($(b).text());
        }).appendTo("#" + fetchId);
    }
});

$('body').on('click', '.loadMoreList', function () {
    var page = $(this).attr('data-page');
    var rank = $(this).attr('data-irank');
    var sort = $('#college-sort').children('option:selected').val();
    getCollegeFilterDataLoadMore(page, sort, false, rank, false)
});

$('body').on('click', '#clearAllClg', function (e) {
    $('.filterSearch li').children('input').prop('checked', false);
    getCollegeFilterData('', '', true);
});

//review landing page js
async function toTop() {
    $("body .filterReviewSearch ul li input").each(function () {
        var parentElement = $(this).parent();
        if ($(this).is(':checked')) {
            $(this).parent().parent().prepend(parentElement);
        }
    });

    $("body .mobileFilter").on('click', function () {
        $(".mobileFilterSectionReview").fadeIn();
    });

    $("body .mobileSort").on('click', function () {
        $(".mobileSortSection").fadeIn();
    });

    $('body .close_sortPopup').on('click', function () {
        $(".mobileSortSection").fadeOut();
    });

    $('body .closeFilter, .applyFilter').on('click', function () {
        $(".mobileFilterSectionReview").fadeOut();
    });

    $('.filterTabReview ul.tabsReview li:first-child').addClass('current');

    $('ul.tabsReview li').click(function () {
        var tab_id = $(this).attr('data-tab');

        $('ul.tabsReview li').removeClass('current');
        $('.tab-content').removeClass('current');

        $(this).addClass('current');
        $("#" + tab_id).addClass('current');
    });
}

$('body').on('click', '#selectedReviewFilters .remove-review-filter', function (e) {
    var checkBoxId = $(this).parent("button").attr("id");
    var dataAttr = $(this).parent("button").data("attr");
    $("input[id='" + checkBoxId + "']").prop('checked', false);
    if (dataAttr == 'state') {
        $('#reviewsearch-city input, #reviewelasticsearch-city input').prop('checked', false)
    }
    if (dataAttr == 'stream') {
        $('#reviewsearch-course input, #reviewelasticsearch-course input').prop('checked', false)
    }
    getReviewFilterData();
});

$("body .foundReviews button").slice(0, 2).show();
$(".showMoreReviews").click(function () {
    $(".foundReviews button").slice(0, 30).show();
    $(this).remove();
});

$('body').on('click', '#clearAllReviews', function (e) {
    $('.filterReviewSearch li').children('input').prop('checked', false);
    getReviewFilterData('', '', true);
    window.history.pushState("", "", '/college/reviews');
});

$("body").on('click', '.filterCategoryReviewName', function () {
    $(this).next("").slideToggle("").toggleClass("tglClass");
    $(this).toggleClass("down_angle");
});

$('body #review-sort').on("change", function () {
    var sort = $(this).children('option:selected').val();
    $(this).children('option:selected').attr('selected', true);
    getReviewFilterData('', sort);
});

if (window.screen.width > 1023) {
    $('body #reviewFilterDSection').on("change", ".filterReviewDiv ul input", function () {
        var sort = $('#review-sort').children('option:selected').val();
        getReviewFilterData('', sort);
        $("html, body").animate({ scrollTop: "0" }, 200);
    });
}

$('body').on('click', '.loadMoreListReview', function () {
   var page = parseInt($(this).attr('data-page')) || 1;
    page += 1;
    var sort = $('#review-sort').children('option:selected').val();
    getReviewFilterData(page, sort, false)
});

if (window.screen.width < 1023) {
    $("body").on('click', '#reviewFilterDSection .applyFilter', function () {
        getReviewFilterData();
        $("html, body").animate({ scrollTop: "0" }, 200);
    });

    $('body').on("click", '.mobileReviewSortDiv ul li', function (e) {
        getReviewFilterData('', $(this).children('input').attr("id"));
        $(".mobileSortSection").fadeOut();
    })
}

$(document).ready(function () {
    $(document).on('click', 'ul.loadMoreReviews li a', function () {
        var anchorUrl = $(this).attr('href');
        var decodedUrl = decodeURIComponent(anchorUrl);
        $(this).attr("href", decodedUrl);
    });
});

function getReviewFilterData(page, sortBy, isReset) {
    var data = $("#review-filter-form").serialize() + '&page=' + page + '&sortBy=' + sortBy;
    $('#filter-loader').show();
    $.post("/review/index", data,
        function (data, textStatus, jqXHR) {
            var html = data.html;
            var url = data.url;
            var oldLocation = window.location.href;
            history.pushState(oldLocation, "", url);

            if (data.hasNext == true) {
                $(".loadMoreListReview").show();
            } else {
                $(".loadMoreListReview").hide();
            }

            if (textStatus == 'success') {
                $("body nav.breadcrumbDiv").html($(html).find('nav.breadcrumbDiv').html());
                $("#reviewFilterDSection").html($(html).find('#reviewFilterDSection').html());
                $(".loadMoreReviews").html($(html).find('.loadMoreReviews').html());
                if (page == undefined || page == '') {
                    $(".moreReviewsLanding").html($(html).find('div.moreReviewsLanding').html());
                } else if (page != undefined) {
                    $(".moreReviewsLanding").append($(html).find('div.moreReviewsLanding').html());
                    $(".loadMoreListReview").attr('data-page', data.page);
                }
            }

            $("body .foundReviews button").slice(0, 2).show();
            $(".showMoreReviews").click(function () {
                $(".foundReviews button").slice(0, 30).show();
                $(this).remove();
            });

            toTop();
            if (isReset) {
                $(".mobileFilterSectionReview").fadeIn();
            }

            setTimeout(removePreLoader, 400)
        },
        "json"
    );
}

$("body").on("click", ".filterReviewSearch ul li input", function () {
    var parentEle = $(this).parent();
    var parent = $(this).parent().parent().attr('id');
    if ($(this).is(':checked')) {
        // Handle both ReviewSearch and ReviewElasticSearch field IDs
        if (parent == 'reviewsearch-state' || parent == 'reviewelasticsearch-state') {
            $('#reviewsearch-city input, #reviewelasticsearch-city input').prop('checked', false)
        } else if (parent == 'reviewsearch-stream' || parent == 'reviewelasticsearch-stream') {
            $('#reviewsearch-course input, #reviewelasticsearch-course input').prop('checked', false)
        }

        if (window.screen.width > 1023) {
            $(this).parent().parent().prepend(parentEle);
        }

    } else {
        var fetchId = $(this).parent().parent().attr('id');
        $(this).parent().sort(function (a, b) {
            return $(a).text().localeCompare($(b).text());
        }).appendTo("#" + fetchId);
    }
});

/** Script for radio-checkbox option transition starts*/
// $(document).on('click', '#reviewsearch-state input[type="checkbox"]', function () {
//     $('#reviewsearch-state input[type="checkbox"]').not(this).prop('checked', false);
// });
$(document).on('click', '#reviewsearch-stream input, #reviewelasticsearch-stream input', function () {
    $('#reviewsearch-stream input, #reviewelasticsearch-stream input').not(this).prop('checked', false);
    $('#reviewsearch-course input, #reviewelasticsearch-course input').prop('checked', false)
});
$(document).on('click', '#reviewsearch-course input, #reviewelasticsearch-course input', function () {
    $('#reviewsearch-course input[type="checkbox"], #reviewelasticsearch-course input[type="checkbox"]').not(this).prop('checked', false);
});
function getCollegeFilterDataLoadMore(page, sortBy, isReset, rank, isWidget = true) {
    if (isWidget == false) {
        isWidget = false;
    }

    var dataVariable = $("#college-filter-form").serialize() + '&page=' + page + '&sortBy=' + sortBy + '&rank=' + rank + '&isWidget=' + isWidget;
    $('#filter-loader').show();
    
                $.post("/college/get-filter-data-college", dataVariable,
                function (data1, textStatus, jqXHR) {
                    //console.log(data1.html); return false;
                    var html = data1.html;
                     var url = data1.url;
                    if (data1.hasNext == true) {
                        $(".loadMoreList").show();
                    } else {
                        $(".loadMoreList").hide();
                    }
                    $("body nav.breadcrumbDiv").html($(html).find('nav.breadcrumbDiv').html());
                    $(".college__Landing__Hero__Section1").html($(html).find('div.college__Landing__Hero__Section1').html());
                     // $(".number__of__filters").html($(html).find('.number__of__filters').html());
                $(".filtered__college_count").html($(html).find('.filtered__college_count').html());
                $(".load__more__row").html($(html).find('.load__more__row').html());
                if (page == undefined || page == '') {
                     $(".searchedcollegeList").html($(html).find('div.searchedcollegeList').html());
                 } else if (page != undefined) {
                  ;
                    $(".searchedcollegeList").append($(html).find('div.searchedcollegeList').html());
                     $(".loadMoreList").attr('data-page', data1.page);
                 }
                 $(".filter-faq").html($(html).find('div.filter-faq').html());
                 var title = data1.data.meta_title ?? data1.data.title;
                 var desc = data1.data.meta_description ?? '';
                 var fullUrl = location.protocol + '//' + location.host + url;
                 var canonicalUrl = location.protocol + '//' + location.host + url.split("?")[0];
                
                readMore(); 
                $("body .foundClgs button").slice(0, 2).show();
                $(".showMoreOptions").click(function () {
                    $(".foundClgs button").slice(0, 30).show();
                    $(this).remove();
                });
                $(".faq_answer").hide();
                $(".faq_question").click(function () {
                    $(this).toggleClass("downAngle");
                    $(this).next(".faq_answer").slideToggle("slow").siblings(".faq_answer:visible").slideUp("slow");
                });
                //showMoreContent();
                getSponsorColleges(data1.sponsorParams);
                toTop();
                if (isReset) {
                    $(".mobileFilterSection").fadeIn();
                }
                $("body .highlight__value").each(function () {
                    var htmlText = $.trim($(this).html());
                    if (htmlText == '-/-') {
                        $(this).addClass('no__value');
                    }
                });
    
                $("body .highlight__name").each(function () {
                    var htmlText = $.trim($(this).html());
                    if (htmlText == '-/-') {
                        $(this).addClass('no__value');
                    }
                });
    
                setTimeout(removePreLoader, 400)
                },"json");
               // $("body nav.breadcrumbDiv").html($(html).find('nav.breadcrumbDiv').html());

              //  $(".college__Landing__Hero__Section1").html($(html).find('div.college__Landing__Hero__Section1').html());

               
              
                
            
          
}
/** Script for radio-checkbox option transition ends*/
$(window).on('scroll', function () {
    let scrollTop = $(window).scrollTop();
    let windowHeight = $(window).height();
    let documentHeight = $(document).height();

    // Trigger when scrolled past half the page
    if (scrollTop >= (documentHeight - windowHeight) / 2 && !ajaxTriggered ) {
            ajaxTriggered =true;
            loadMoreData();
        
    }
});
function loadMoreData() {
   //alert("hello");
   var data = $("#college-filter-form").serialize();

   $.post("/college/get-filter-data-exam-news-article",data,
   function (data, textStatus, jqXHR) {
        $('.testing').addClass('news__update__section');
        $('.testing').html(data.html).find('div.news-article').html();
        $('.interestedExam').html(data.htmlExam);
   },"json");
  
}