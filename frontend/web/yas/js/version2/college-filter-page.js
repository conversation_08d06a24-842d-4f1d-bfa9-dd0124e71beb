/*async function showMoreContent() {
		var pageInfoText = $('.pageInfo').text();
		var wordsCount = $.trim(pageInfoText.replace(/[\t\n]+/g, ' ')).length;
		if (wordsCount > 250) {
						$('.pageInfo').after('<p class="readMoreInfo">Show More</p>');
						$('.pageInfo').css('margin-bottom', '0px')
		} else {
						$('.pageInfo').css({
										'margin-bottom': '20px',
										'max-height': '100%'
						})
		}
		$(".readMoreInfo").wrap("<div class='readMoreDiv'></div>");
		$('.readMoreInfo').click(function () {
						$(this).parent().prev().toggleClass("pageInfo");
						var text = $(this).text() == 'Show Less' ? 'Show More' : 'Show Less';
						$(this).text(text);
						var infoOnTop = $(this).parent().prev('.pageData, .showMoreScroll');
						if (infoOnTop.length === 0) {
										$("html, body").animate({
														scrollTop: ($(".readMoreInfo").offset().top - $(".pageInfo").outerHeight()) - 100,
										})
						} else if (infoOnTop.hasClass("pageInfo")) {
										console.log('catch');
										$('body,html').animate({
														scrollTop: infoOnTop.offset().top - 70
										}, 20)
						} else {
										return !1
						}
						return !1
		})
}
showMoreContent();*/


// Carousel
/*$(document).ready(function () {
		var currentIndex = 0;
		var itemWidth = $('.carousel-item').outerWidth();
		var visibleItems = 3;
		var totalItems = $('.carousel-item').length;
		alert(totalItems);
		var maxIndex = totalItems - visibleItems;

		// Function to update the active dot
		function updateDots(index) {
						$('.dot').removeClass('active');
						$('.dot').eq(index).addClass('active');
		}

		// Function to move the carousel to a specific slide
		function goToSlide(index) {
						$('.carousel-track').css('transform', 'translateX(' + (-itemWidth * index) + 'px)');
						updateDots(index);
		}

		// Handle dot click
		$('.dot').click(function () {
						var index = $(this).data('slide');
						currentIndex = index;
						goToSlide(index);
						alert(index);
		});

		// Handle mouse scroll
		$('.carousel-container').on('wheel', function (event) {
						if (event.originalEvent.deltaY < 0) {
										// Scrolling up (previous slide)
										if (currentIndex > 0) {
														currentIndex--;
														goToSlide(currentIndex);
										}
						} else {
										// Scrolling down (next slide)
										if (currentIndex < maxIndex) {
														currentIndex++;
														goToSlide(currentIndex);
										}
						}
		});

		// Initialize the first dot as active
		updateDots(currentIndex);
});*/

// TAB logic
$(document).ready(function () {
	$('.tab__list li').click(function () {
		var tab_id = $(this).attr('data-tab');

		$('.tab__list li').removeClass('current__tab');
		$('.tab__section').removeClass('current__tab');

		$(this).addClass('current__tab');
		$("#" + tab_id).addClass('current__tab');
	});
	$('body').on('click', '.latest__news__tabs li', function () {

		var tab_id = $(this).attr('data-tab');

		$('.latest__news__tabs li').removeClass('current__tab');
		$('.recent__latest__section').removeClass('current__tab');

		$(this).addClass('current__tab');
		$("#" + tab_id).addClass('current__tab');
	});
});

// FAQ
/*$(".faq_answer").hide();
$(".faq_question").click(function () {
		$(this).toggleClass("downAngle");
		$(this).next(".faq_answer").slideToggle("slow").siblings(".faq_answer:visible").slideUp("slow")
});*/

if (window.screen.width < 1023) {
	$(".accordian__expand").hide();
	$(".accordian__label").click(function () {
		$(this).toggleClass("downAngle");
		$(this).next(".accordian__expand").slideToggle("slow").siblings(".accordian__expand:visible").slideUp("slow")
	});
}

//     slider
$(".examInfoSlider .row, .liveApllicationFormsInner .row, .customSliderCards, .collegeRelataedLinks, .pageRedirectionLinks ul, .latestInfoList, .topSearchDiv ul, .otherCategorySection .row").scroll(function () {
	var $examInfoSliderWidth = $(this).outerWidth();
	var $examInfoSliderScrollWidth = $(this)[0].scrollWidth;
	var $examInfoSliderScrollLeft = $(this).scrollLeft();
	if (parseInt($examInfoSliderScrollWidth - $examInfoSliderWidth) === parseInt($examInfoSliderScrollLeft)) {
		$(this).prev().addClass("over")
	} else {
		$(this).prev().removeClass("over")
	}
	if ($examInfoSliderScrollLeft === 0) {
		$(this).prev().prev().addClass("over")
	} else {
		$(this).prev().prev().removeClass("over")
	}
});
$(".four-cardDisplay .scrollLeft").click(function () {
	$(this).next().next().animate({
		scrollLeft: "-=297px",
	}, 750)
});
$(".four-cardDisplay .scrollRight").click(function () {
	$(this).next().animate({
		scrollLeft: "+=297px",
	}, 750);
	$(this).prev().removeClass("over")
});

$('body').on('click', '.compareIcon', function () {
	$.ajax({
		url: '/ajax/show-college-compare-select-panel',
		type: 'POST',
		dataType: "json",
		success: function (response) {
			$('#college_compare_header_select_panel').html(response.headerSelectPanel);
			getCollegeCompareDetails();
			document.querySelector(".collegeCompare__container").style.display = 'block';
		}
	});
});
$(".search-listing").keyup(function () {

	// Retrieve the input field text and reset the count to zero
	var filter = $(this).val().toLowerCase(),
		count = 0;
	// console.log(filter);
	// Loop through the comment list
	$("body .college__name a").filter(function () {
		var idHide = $(this).attr('data-hide');
		console.log($(this).text().toLowerCase().indexOf(filter));
		console.log($(this).text().toLowerCase());
		let isCheck = $(this).text().toLowerCase().indexOf(filter) > -1;
		if (isCheck) {
			console.log(isCheck);
			$('#search-' + idHide).show();
			$('.hide__widget').show();
		} else {
			$('#search-' + idHide).hide();
			$('.hide__widget').hide();
		}

	});
	return false;
	$("body .college__name a").each(function () {

		var idHide = $(this).attr('data-hide');

		// If the list item does not contain the text phrase fade it out
		if ($(this).text().search(new RegExp(filter, "i")) < 0) {
			// MY CHANGE
			$('#search-' + idHide).hide();
			$('.hide__widget').hide();
			// console.log($(this).text().search(new RegExp(filter, "i")) );

			// Show the list item if the phrase matches and increase the count by 1
		} else {
			$('#search-' + idHide).show(); // MY CHANGE
			$('.hide__widget').show();
			count++;
		}

	});

});
// Feedback form
$('body').on('click', '.feedback__section  ul li', function () {
	var rating = $(this).attr('data-value');
	$("body").css("overflowY", "hidden");
	showFeedBackOption(rating);
	$('.feedback__container').css('display', 'flex');
	$("#rating" + rating).prop("checked", true);
});
$('.rating__buttons li').click(function () {
	var rating = $(this).attr('data-value');
	showFeedBackOption(rating);

});
$('body .feedback__container .closeIcon').click(function () {
	$("body").css("overflowY", "unset");
	$('.feedback__container').css('display', 'none');
})
$('.recent__latest__section .load__more__row').click(function () {
	$(this).parent().css("maxHeight", "unset");
	$(this).parent().css("overflow", "unset");
	$(this).css("display", 'none');
});

function showFeedBackOption(rating) {
	$('.hide_form').hide();
	$('.show_form').show();
	$('.current-url').val(gmu.config.page_url);
	var text = '';
	var option_text = '';
	//  alert(rating);
	if (rating < 4) {
		text = "You Rated Poor";
		option_text = 'Poor';
		$('.five-to-seven input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);

	}
	if (rating == 4) {
		text = "You Rated Below Average";
		option_text = 'Below Average';
		$('.five-to-seven input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);


	}
	if (rating > 4 && rating < 7) {
		text = "You Rated  Average";
		option_text = 'Average';
		$('.one-to-four input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);
	}
	if (rating > 6 && rating < 9) {
		text = "You Rated  Good";
		option_text = 'Good';
		$('.one-to-four input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);
	}

	if (rating > 8) {

		text = "You Rated Excellent";
		option_text = 'Excellent';
		$('.one-to-four input').prop("checked", false);
		$('.five-to-seven input').prop("checked", false);
	}
	if (text != '') {
		$('.text3').text(text);
	}
	$('.rating_option_text').val(option_text);
	if (rating < 5) {
		$('.one-to-four').show();
		$('.five-to-seven').hide();
		$('.eight-to-ten').hide();
	}
	if (rating > 4 && rating < 8) {
		$('.one-to-four').hide();
		$('.five-to-seven').show();
		$('.eight-to-ten').hide();
	}
	if (rating > 7) {
		$('.one-to-four').hide();
		$('.five-to-seven').hide();
		$('.eight-to-ten').show();
	}
}

$('.feed-back-button').click(function (e) {
	e.preventDefault();
	e.stopImmediatePropagation();
	var data = $("#feedback-form").serialize();
	$('#filter-loader').css('z-index', 12);
	$('#filter-loader').show();
	$(this).attr('disable', true);
	$.post("/college/save-page-experience", data,
		function (response) {
			$('#filter-loader').css('z-index', 6);
			$('#filter-loader').hide();
			if (response.success == true) {
				$('.feedback__form').css('height', 'auto');
				$('.errorHtml').html('');
				$('.hide_form').show();
				$('.show_form').hide();
				$('#option-error-two').html('');
				$('#option-error-one').html('');
			} else {
				var obj = JSON.parse(JSON.stringify(response.message));
				console.log(obj);
				var htmlError = ''
				$('.error__position').html('');
				$.each(obj, function (i, val) {
					console.log(i);
					if (i == 'experience_text') {
						$('#option-error-two').html(val);
					}
					if (i == 'rating_option') {
						$('#option-error-one').html(val);
					}

				});
				$(this).attr('disable', false);
				$('.errorHtml').html('');
				$('.errorHtml').html(htmlError);
			}
		},
		"json"
	);
});/*async function showMoreContent() {
		var pageInfoText = $('.pageInfo').text();
		var wordsCount = $.trim(pageInfoText.replace(/[\t\n]+/g, ' ')).length;
		if (wordsCount > 250) {
						$('.pageInfo').after('<p class="readMoreInfo">Show More</p>');
						$('.pageInfo').css('margin-bottom', '0px')
		} else {
						$('.pageInfo').css({
										'margin-bottom': '20px',
										'max-height': '100%'
						})
		}
		$(".readMoreInfo").wrap("<div class='readMoreDiv'></div>");
		$('.readMoreInfo').click(function () {
						$(this).parent().prev().toggleClass("pageInfo");
						var text = $(this).text() == 'Show Less' ? 'Show More' : 'Show Less';
						$(this).text(text);
						var infoOnTop = $(this).parent().prev('.pageData, .showMoreScroll');
						if (infoOnTop.length === 0) {
										$("html, body").animate({
														scrollTop: ($(".readMoreInfo").offset().top - $(".pageInfo").outerHeight()) - 100,
										})
						} else if (infoOnTop.hasClass("pageInfo")) {
										console.log('catch');
										$('body,html').animate({
														scrollTop: infoOnTop.offset().top - 70
										}, 20)
						} else {
										return !1
						}
						return !1
		})
}
showMoreContent();*/


// Carousel
/*$(document).ready(function () {
		var currentIndex = 0;
		var itemWidth = $('.carousel-item').outerWidth();
		var visibleItems = 3;
		var totalItems = $('.carousel-item').length;
		alert(totalItems);
		var maxIndex = totalItems - visibleItems;

		// Function to update the active dot
		function updateDots(index) {
						$('.dot').removeClass('active');
						$('.dot').eq(index).addClass('active');
		}

		// Function to move the carousel to a specific slide
		function goToSlide(index) {
						$('.carousel-track').css('transform', 'translateX(' + (-itemWidth * index) + 'px)');
						updateDots(index);
		}

		// Handle dot click
		$('.dot').click(function () {
						var index = $(this).data('slide');
						currentIndex = index;
						goToSlide(index);
						alert(index);
		});

		// Handle mouse scroll
		$('.carousel-container').on('wheel', function (event) {
						if (event.originalEvent.deltaY < 0) {
										// Scrolling up (previous slide)
										if (currentIndex > 0) {
														currentIndex--;
														goToSlide(currentIndex);
										}
						} else {
										// Scrolling down (next slide)
										if (currentIndex < maxIndex) {
														currentIndex++;
														goToSlide(currentIndex);
										}
						}
		});

		// Initialize the first dot as active
		updateDots(currentIndex);
});*/

// TAB logic
$(document).ready(function () {
	$('.tab__list li').click(function () {
		var tab_id = $(this).attr('data-tab');

		$('.tab__list li').removeClass('current__tab');
		$('.tab__section').removeClass('current__tab');

		$(this).addClass('current__tab');
		$("#" + tab_id).addClass('current__tab');
	});
	$('body').on('click', '.latest__news__tabs li', function () {
		var tab_id = $(this).attr('data-tab');
       
		$('.latest__news__tabs li').removeClass('current__tab');
		$('.recent__latest__section').removeClass('current__tab');

		$(this).addClass('current__tab');
		$("#" + tab_id).addClass('current__tab');
	});
});

// FAQ
/*$(".faq_answer").hide();
$(".faq_question").click(function () {
		$(this).toggleClass("downAngle");
		$(this).next(".faq_answer").slideToggle("slow").siblings(".faq_answer:visible").slideUp("slow")
});*/

if (window.screen.width < 1023) {
	$(".accordian__expand").hide();
	$(".accordian__label").click(function () {
		$(this).toggleClass("downAngle");
		$(this).next(".accordian__expand").slideToggle("slow").siblings(".accordian__expand:visible").slideUp("slow")
	});
}

//     slider
$(".examInfoSlider .row, .liveApllicationFormsInner .row, .customSliderCards, .collegeRelataedLinks, .pageRedirectionLinks ul, .latestInfoList, .topSearchDiv ul, .otherCategorySection .row").scroll(function () {
	var $examInfoSliderWidth = $(this).outerWidth();
	var $examInfoSliderScrollWidth = $(this)[0].scrollWidth;
	var $examInfoSliderScrollLeft = $(this).scrollLeft();
	if (parseInt($examInfoSliderScrollWidth - $examInfoSliderWidth) === parseInt($examInfoSliderScrollLeft)) {
		$(this).prev().addClass("over")
	} else {
		$(this).prev().removeClass("over")
	}
	if ($examInfoSliderScrollLeft === 0) {
		$(this).prev().prev().addClass("over")
	} else {
		$(this).prev().prev().removeClass("over")
	}
});
$(".four-cardDisplay .scrollLeft").click(function () {
	$(this).next().next().animate({
		scrollLeft: "-=297px",
	}, 750)
});
$(".four-cardDisplay .scrollRight").click(function () {
	$(this).next().animate({
		scrollLeft: "+=297px",
	}, 750);
	$(this).prev().removeClass("over")
});

$('body').on('click', '.compareIcon', function () {
	$.ajax({
		url: '/ajax/show-college-compare-select-panel',
		type: 'POST',
		dataType: "json",
		success: function (response) {
			$('#college_compare_header_select_panel').html(response.headerSelectPanel);
			getCollegeCompareDetails();
			document.querySelector(".collegeCompare__container").style.display = 'block';
		}
	});
});
$(".search-listing").keyup(function () {

	// Retrieve the input field text and reset the count to zero
	var filter = $(this).val().toLowerCase(),
		count = 0;
	// console.log(filter);
	// Loop through the comment list
	$("body .college__name a").filter(function () {
		var idHide = $(this).attr('data-hide');
		console.log($(this).text().toLowerCase().indexOf(filter));
		console.log($(this).text().toLowerCase());
		let isCheck = $(this).text().toLowerCase().indexOf(filter) > -1;
		if (isCheck) {
			console.log(isCheck);
			$('#search-' + idHide).show();
			$('.hide__widget').show();
		} else {
			$('#search-' + idHide).hide();
			$('.hide__widget').hide();
		}

	});
	return false;
	$("body .college__name a").each(function () {

		var idHide = $(this).attr('data-hide');

		// If the list item does not contain the text phrase fade it out
		if ($(this).text().search(new RegExp(filter, "i")) < 0) {
			// MY CHANGE
			$('#search-' + idHide).hide();
			$('.hide__widget').hide();
			// console.log($(this).text().search(new RegExp(filter, "i")) );

			// Show the list item if the phrase matches and increase the count by 1
		} else {
			$('#search-' + idHide).show(); // MY CHANGE
			$('.hide__widget').show();
			count++;
		}

	});

});
// Feedback form
$('body').on('click', '.feedback__section  ul li', function () {
	var rating = $(this).attr('data-value');
	$("body").css("overflowY", "hidden");
	showFeedBackOption(rating);
	$('.feedback__container').css('display', 'flex');
	$("#rating" + rating).prop("checked", true);
});
$('.rating__buttons li').click(function () {
	var rating = $(this).attr('data-value');
	showFeedBackOption(rating);

});
$('body .feedback__container .closeIcon').click(function () {
	$("body").css("overflowY", "unset");
	$('.feedback__container').css('display', 'none');
})
$('.recent__latest__section .load__more__row').click(function () {
	$(this).parent().css("maxHeight", "unset");
	$(this).parent().css("overflow", "unset");
	$(this).css("display", 'none');
});

function showFeedBackOption(rating) {
	$('.hide_form').hide();
	$('.show_form').show();
	$('.current-url').val(gmu.config.page_url);
	var text = '';
	var option_text = '';
	//  alert(rating);
	if (rating < 4) {
		text = "You Rated Poor";
		option_text = 'Poor';
		$('.five-to-seven input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);

	}
	if (rating == 4) {
		text = "You Rated Below Average";
		option_text = 'Below Average';
		$('.five-to-seven input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);


	}
	if (rating > 4 && rating < 7) {
		text = "You Rated  Average";
		option_text = 'Average';
		$('.one-to-four input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);
	}
	if (rating > 6 && rating < 9) {
		text = "You Rated  Good";
		option_text = 'Good';
		$('.one-to-four input').prop("checked", false);
		$('.eight-to-ten input').prop("checked", false);
	}

	if (rating > 8) {

		text = "You Rated Excellent";
		option_text = 'Excellent';
		$('.one-to-four input').prop("checked", false);
		$('.five-to-seven input').prop("checked", false);
	}
	if (text != '') {
		$('.text3').text(text);
	}
	$('.rating_option_text').val(option_text);
	if (rating < 5) {
		$('.one-to-four').show();
		$('.five-to-seven').hide();
		$('.eight-to-ten').hide();
	}
	if (rating > 4 && rating < 8) {
		$('.one-to-four').hide();
		$('.five-to-seven').show();
		$('.eight-to-ten').hide();
	}
	if (rating > 7) {
		$('.one-to-four').hide();
		$('.five-to-seven').hide();
		$('.eight-to-ten').show();
	}
}

$('.feed-back-button').click(function (e) {
	e.preventDefault();
	var data = $("#feedback-form").serialize();
	$('#filter-loader').css('z-index', 12);
	$('#filter-loader').show();
	$(this).attr('disable', true);
	$.post("/college/save-page-experience", data,
		function (response) {
			$('#filter-loader').css('z-index', 6);
			$('#filter-loader').hide();
			if (response.success == true) {
				$('.feedback__form').css('height', 'auto');
				$('.errorHtml').html('');
				$('.hide_form').show();
				$('.show_form').hide();
				$('#option-error-two').html('');
				$('#option-error-one').html('');
			} else {
				var obj = JSON.parse(JSON.stringify(response.message));
				console.log(obj);
				var htmlError = ''
				$('.error__position').html('');
				$.each(obj, function (i, val) {
					console.log(i);
					if (i == 'experience_text') {
						$('#option-error-two').html(val);
					}
					if (i == 'rating_option') {
						$('#option-error-one').html(val);
					}

				});
				$(this).attr('disable', false);
				$('.errorHtml').html('');
				$('.errorHtml').html(htmlError);
			}
		},
		"json"
	);
});
$('body').on('click', '.search-remove', function () {
	$('.search-listing').val('');
	$('.hide__widget').show();
	$('body .college__card__new').show();
});
$("body .highlight__value").each(function () {
	var htmlText = $.trim($(this).html());
	if (htmlText == '-/-') {
		$(this).addClass('no__value');
	}
});

$("body .highlight__name").each(function () {
	var htmlText = $.trim($(this).html());
	if (htmlText == '-/-') {
		$(this).addClass('no__value');
	}
});
$('body').on('click', '.search-remove', function () {
	$('.search-listing').val('');
	$('.hide__widget').show();
	$('body .college__card__new').show();
});
$("body .highlight__value").each(function () {
	var htmlText = $.trim($(this).html());
	if (htmlText == '-/-') {
		$(this).addClass('no__value');
	}
});

$("body .highlight__name").each(function () {
	var htmlText = $.trim($(this).html());
	if (htmlText == '-/-') {
		$(this).addClass('no__value');
	}
});