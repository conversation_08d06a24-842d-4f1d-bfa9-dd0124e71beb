<?php

use yii\db\Migration;

/**
 * Class m250905_065049_craete_table_poll
 */
class m250905_065049_craete_table_poll extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%poll}}', [
            'id' => $this->primaryKey(),
            'question' => $this->text()->notNull(),
            'options' => $this->json()->notNull(),
            'status' => $this->smallInteger()->notNull()->defaultValue(1),
            'created_by' => $this->integer()->null(),
            'modified_by' => $this->integer()->null(),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $options);

        $this->createIndex('idx-poll-id', '{{%poll}}', 'id');
        $this->createIndex('idx-poll-created_at', '{{%poll}}', 'created_at');
        $this->createIndex('idx-poll-updated_at', '{{%poll}}', 'updated_at');

        $this->addForeignKey(
            'fk-poll-created_by',
            '{{%poll}}',
            'created_by',
            '{{%user}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-poll-modified_by',
            '{{%poll}}',
            'modified_by',
            '{{%user}}',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop foreign keys
        $this->dropForeignKey('fk-poll-created_by', '{{%poll}}');
        $this->dropForeignKey('fk-poll-modified_by', '{{%poll}}');

        // Drop indexes
        $this->dropIndex('idx-poll-id', '{{%poll}}');
        $this->dropIndex('idx-poll-created_at', '{{%poll}}');
        $this->dropIndex('idx-poll-updated_at', '{{%poll}}');

        // Drop table
        $this->dropTable('{{%poll}}');
    }
}
