<?php

use yii\db\Migration;

/**
 * Class m250905_075725_craete_table_poll_response
 */
class m250905_075725_craete_table_poll_response extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%poll_response}}', [
            'id' => $this->primaryKey(),
            'entity' => $this->string(),
            'entity_id' => $this->integer()->notNull(),
            'poll_id' => $this->integer()->notNull(),
            'user_id' => $this->integer()->null(),
            'selected_option' => $this->string(255)->notNull(),
            'text_field_value' => $this->string(255)->null(),
            'submitted_at' => $this->dateTime()->defaultExpression('CURRENT_TIMESTAMP'),
            'ip_address' => $this->string(45)->null(), // supports IPv6
            'session_id' => $this->string(255)->null(),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $options);

        // Indexes
        $this->createIndex('idx-poll_response-poll_id', '{{%poll_response}}', 'poll_id');
        $this->createIndex('idx-poll_response-created_at', '{{%poll_response}}', 'created_at');
        $this->createIndex('idx-poll_response-updated_at', '{{%poll_response}}', 'updated_at');

        // Foreign keys
        $this->addForeignKey(
            'fk-poll_response-poll_id',
            '{{%poll_response}}',
            'poll_id',
            '{{%poll}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-poll_response-user_id',
            '{{%poll_response}}',
            'user_id',
            '{{%student}}',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }

    public function safeDown()
    {
        // Drop foreign keys
        $this->dropForeignKey('fk-poll_response-poll_id', '{{%poll_response}}');
        $this->dropForeignKey('fk-poll_response-user_id', '{{%poll_response}}');

        // Drop indexes
        $this->dropIndex('idx-poll_response-poll_id', '{{%poll_response}}');
        $this->dropIndex('idx-poll_response-created_at', '{{%poll_response}}');
        $this->dropIndex('idx-poll_response-updated_at', '{{%poll_response}}');

        // Drop table
        $this->dropTable('{{%poll_response}}');
    }
}
