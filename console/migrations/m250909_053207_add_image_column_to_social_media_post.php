<?php

use yii\db\Migration;

class m250909_053207_add_image_column_to_social_media_post extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('social_media_post', 'image', $this->string()->after('hastag')->defaultValue(null));
        $this->addColumn('social_media_post', 'image_log', $this->json()->after('image')->defaultValue(null));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250909_053207_add_image_column_to_social_media_post cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250909_053207_add_image_column_to_social_media_post cannot be reverted.\n";

        return false;
    }
    */
}
