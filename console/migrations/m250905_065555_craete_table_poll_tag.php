<?php

use yii\db\Migration;

/**
 * Class m250905_065555_craete_table_poll_tag
 */
class m250905_065555_craete_table_poll_tag extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%poll_tag}}', [
            'id' => $this->primaryKey(),
            'entity' => $this->string()->notNull(),
            'entity_id' => $this->integer()->notNull(),
            'poll_id' => $this->integer()->notNull(),
            'expiry_date' => $this->dateTime()->null(),
            'tagged_by' => $this->integer()->null(),
            'modified_by' => $this->integer()->null(),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $options);

        // Indexes
        $this->createIndex('idx-poll_tag-entity_id', '{{%poll_tag}}', 'entity_id');
        $this->createIndex('idx-poll_tag-poll_id', '{{%poll_tag}}', 'poll_id');
        $this->createIndex('idx-poll_tag-created_at', '{{%poll_tag}}', 'created_at');
        $this->createIndex('idx-poll_tag-updated_at', '{{%poll_tag}}', 'updated_at');

        // Foreign keys

        $this->addForeignKey(
            'fk-poll_tag-poll_id',
            '{{%poll_tag}}',
            'poll_id',
            '{{%poll}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-poll_tag-tagged_by',
            '{{%poll_tag}}',
            'tagged_by',
            '{{%user}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-poll_tag-modified_by',
            '{{%poll_tag}}',
            'modified_by',
            '{{%user}}',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop foreign keys first
        $this->dropForeignKey('fk-poll_tag-poll_id', '{{%poll_tag}}');
        $this->dropForeignKey('fk-poll_tag-tagged_by', '{{%poll_tag}}');
        $this->dropForeignKey('fk-poll_tag-modified_by', '{{%poll_tag}}');

        // Drop indexes
        $this->dropIndex('idx-poll_tag-poll_id', '{{%poll_tag}}');
        $this->dropIndex('idx-poll_tag-created_at', '{{%poll_tag}}');
        $this->dropIndex('idx-poll_tag-updated_at', '{{%poll_tag}}');

        // Drop table
        $this->dropTable('{{%poll_tag}}');
    }
}
