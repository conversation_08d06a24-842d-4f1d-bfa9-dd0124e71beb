<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use yii\helpers\console;
use Carbon\Carbon;
use yii\db\DefaultValueConstraint;
use yii\httpclient\Client;
use common\event\SitemapEvent;
use common\models\Article;
use common\models\NewsSubdomain;
use common\event\SitemapEventNew;
use common\models\NewsSubdomainLiveUpdate;
use common\models\SocialMediaPost;
use common\services\DatabaseCleanService;
use yii\db\Query;
use frontend\helpers\Url;

class DatabaseCleanController extends controller
{
    // tablename => ['column', 'months']
    protected $oldRowCleanUpLists = [
        'gmu_transactional_sms_lead' => ['updated_on', '2'],
        'gmu_sms_delivery_log' => ['created_on', '2'],
        'gmu_route_sms_api_log' => ['created_at', '2'],
    ];

    // tablename => ['column', 'days']
    protected $failedJobsList = [
        'failed_jobs' => ['failed_at', '3'],
    ];

    //truncate table list
    protected $cleanAllList = ['gmu_sms_delivery_log_backup'];


    protected $emptyColumnLists = [
        'gmu_bdu_log' => ['action', 'created_on', '3'],
    ];

    protected $emptyColumnByDaysList = [
        'gmu_email_bounce' => ['response', 'created_on', '15'],
    ];

    //function to call
    public function actionCleanTables()
    {
        // $this->cleanAll();
        // $this->cleanUpOldRow();
        // $this->emptyColumnByDays();
        // // $this->emptyColumn();
        // $this->failedJobs();

        $cleanService = new DatabaseCleanService();

        $cleanService->generateBackupdata();
    }

    //scripts for command

    public function cleanUpOldRow()
    {
        // print_r($month);exit();
        foreach ($this->oldRowCleanUpLists as $table => $field) {
            list($column, $month) = $field;

            try {
                $params = [':date' => Carbon::now()->subMonths($month)];
                $command = Yii::$app->db->createCommand(
                    "delete from {$table} where date({$column}) < :date",
                    $params
                )->execute();

                echo $table . ' table cleaned monthwise.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function emptyColumn()
    {
        foreach ($this->oldRowCleanUpLists as $table => $field) {
            list($columnToBeNull, $basedColumn, $month) = $field;

            try {
                $params = [':date' => Carbon::now()->subMonths($month)];
                $command = Yii::$app->db->createCommand(
                    "update {$table} set {$columnToBeNull} = ''"
                )->execute();

                echo $table . ' table row empty.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function failedJobs()
    {

        foreach ($this->failedJobsList as $table => $field) {
            list($column, $day) = $field;
            // print_r($field);

            try {
                $params = [':date' => Carbon::now()->subDays($day)];
                $command = Yii::$app->db->createCommand(
                    "delete from {$table} where date({$column}) < :date",
                    $params
                )->execute();

                echo $table . ' table cleaned daywise.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function cleanAll()
    {
        // print_r($month);exit();
        foreach ($this->cleanAllList as $table) {
            try {
                $command = Yii::$app->db->createCommand()->truncateTable($table)->execute();

                // echo $command->sql;
                echo $table . ' table truncated.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function emptyColumnByDays()
    {
        foreach ($this->emptyColumnByDaysList as $table => $field) {
            list($columnToBeNull, $basedColumn, $days) = $field;

            // print_r($table);
            // print_r($field);exit();

            try {
                $params = [':date' => Carbon::now()->subDays($days)];
                $command = Yii::$app->db->createCommand(
                    "update {$table} set {$columnToBeNull} = '' where date({$basedColumn}) < :date",
                    $params
                )->execute();

                echo 'column ' . $columnToBeNull . ' is empty in table ' . $table;
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function actionPublishNews()
    {
        $query = new Query();
        $query->select(['id', 'name', 'slug', 'scheduled_at'])
            ->from('news_subdomain')->where(['between', 'scheduled_at', Carbon::now(), Carbon::now()->addMinutes(10)])->andWhere(['status' => 0]);
        $result = $query->all();
        
        foreach ($result as $value) {
            if (empty($value['scheduled_at'])) {
                continue;
            }
            try {
                Yii::$app->db->createCommand('UPDATE news_subdomain SET status=:status,published_at=:published_at, updated_at=:updated_at WHERE id=:id')
                    ->bindValue(':id', $value['id'])
                    ->bindValue(':status', 1)
                    ->bindValue(':published_at', $value['scheduled_at'])
                    ->bindValue(':updated_at', $value['scheduled_at'])
                    ->execute();

                (new SitemapEventNew())->updateNewsUpdateXml($value['id'], NewsSubdomainLiveUpdate::STATUS_ACTIVE, $value['scheduled_at'], $value['scheduled_at']);
                
                echo $value['name'] . " is active.\n";
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
        }
    }

    public function actionTwitterPost()
    {
        $postStatus = 0;
        $article = '';

        $getPosts = SocialMediaPost::find()
        ->where(['status' => SocialMediaPost::STATUS_ACTIVE])
        ->andWhere(['published_status'=> SocialMediaPost::PUBLISHED_STATUS_PENDING])
         ->andWhere(['between', 'schedule_at', Carbon::now()->subMinutes(5)->toDateTimeString(), Carbon::now()->toDateTimeString()])
        ->all();
        foreach ($getPosts as $post) {
            $title = trim($post->title);
            $description = trim($post->description);
            $hashtags = str_replace(',', '', $post->hastag);

            if ($post->post_section == SocialMediaPost::POST_SECTION_ARTICLES) {
                $article = Article::findOne(['id' => $post->post_id]);
            }
            if ($post->post_section == SocialMediaPost::POST_SECTION_NEWS) {
                $article = NewsSubdomain::findOne(['id' => $post->post_id]);
            }
            if ($post->post_section ==SocialMediaPost::POST_SECTION_ARTICLES) {
                $url = Url::toGetmyuni() . 'articles/' . $article->slug;
            } elseif ($post->post_section == SocialMediaPost::POST_SECTION_NEWS) {
                $url = Url::toNewsSubdmainGetmyuni() . $article->slug;
            }
            
            $tweetText = "{$title}\n{$description}\n{$hashtags}\n{$url}";
            if (strlen($tweetText) > 280) {
                // Trim description if too long
                $maxDescLength = 280 - strlen($title . $hashtags . $url) - 10;
                $description = mb_substr($description, 0, $maxDescLength) . '...';
                $tweetText = "{$title}\n{$description}\n{$hashtags}\n{$url}";
            }
           
            if (!empty($post->image)) {
                $image = \Yii::$aliases['@twitterImageFrontend'] . '/' . $post->image;
                $parsedUrl = parse_url($image);
                $encodedPath = str_replace(' ', '%20', $parsedUrl['path']);

                $encodedImage = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $encodedPath;
                
                $media = Yii::$app->twitter->uploadMedia($encodedImage);
                if (isset($media['media_id'])) {
                    $post->image_log = json_encode($media ?? null);

                    $response =  Yii::$app->twitter->postTweet($tweetText, $media['media_id']);
                }
            } else {
                $response = Yii::$app->twitter->postTweet($tweetText);
            }
           
            // $response = Yii::$app->twitter->postTweet($tweetText);
            $post->error_log = json_encode($response['data'] ?? null);
            if ($response['httpCode'] != 201) {
                // Failed
                $post->published_status = SocialMediaPost::PUBLISHED_STATUS_FAILED;
                $postStatus = 0;
                $msg = $post->title . ' -  Twitter post failed';
            } else {
                // Success
                $tweetId   = $response['data']['data']['id'];
                $tweetText = $response['data']['data']['text'];
                $tweetUrl  = "https://twitter.com/getmyuniedu/status/{$tweetId}";

                $post->published_status = SocialMediaPost::PUBLISHED_STATUS_POSTED;
                $post->published_at     = Carbon::now();
                // $post->published_by     = Yii::$app->user->identity->id;

                $postStatus = 1;
                $msg = $post->title . " - Tweet posted successfully: <a href='{$tweetUrl}' target='_blank'>{$tweetUrl}</a>";
            }
            if ($post->save()) {
                $result = ['status' => $postStatus, 'message' => $msg];
                print_r($result);
                echo PHP_EOL;
            } else {
                 echo 'Status: error, Message: Failed to save post result in DB' . PHP_EOL;
            }
        }
    }
}
